import { Router } from 'express';
import { newsController } from '@/controllers/news';
import { validateQuery } from '@/middlewares/validation';
import { GetCategoriesQuerySchema } from '@/types';

const router: Router = Router();

/**
 * @route GET /api/categories
 * @desc Get categories for news or crypto
 * @access Public
 * @query {string} type - Category type (news, crypto) - default: news
 */
router.get(
  '/',
  validateQuery(GetCategoriesQuerySchema),
  newsController.getCategories
);

export default router;
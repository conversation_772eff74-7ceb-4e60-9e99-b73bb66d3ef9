import React from 'react';
import { View, ActivityIndicator, Text, StyleSheet } from 'react-native';
import { useAppStore } from '../../store';
import { COLORS, SPACING, FONT_SIZES } from '../../constants';

interface LoadingSpinnerProps {
  size?: 'small' | 'large';
  text?: string;
  color?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'large',
  text = '加载中...',
  color,
}) => {
  const { theme } = useAppStore();
  const colors = COLORS[theme];
  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      <ActivityIndicator
        size={size}
        color={color || colors.primary}
        style={styles.spinner}
      />
      {text && <Text style={styles.text}>{text}</Text>}
    </View>
  );
};

const createStyles = (colors: typeof COLORS.light) =>
  StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: SPACING.xl,
    },
    spinner: {
      marginBottom: SPACING.md,
    },
    text: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      textAlign: 'center',
    },
  });

export default LoadingSpinner;

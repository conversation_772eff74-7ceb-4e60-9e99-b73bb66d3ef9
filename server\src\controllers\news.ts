import { Request, Response } from 'express';
import { newsService } from '@/services/news';
import { dataScheduler } from '@/scheduler/scheduler';
import { logger } from '@/utils/logger';
import {
  GetNewsQuery,
  SearchNewsQuery,
  NewsResponse,
  ArticleResponse,
  CategoriesResponse,
  ApiResponse,
} from '@/types';
import { asyncHandler } from '@/middlewares/error';

/**
 * News Controller
 * Handles all news-related API endpoints
 */
class NewsController {
  /**
   * Get paginated news articles
   * GET /api/news
   */
  public getNews = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const query = req.query as unknown as GetNewsQuery;
    const { page = 1, limit = 20, category, sortBy, sortOrder } = query;

    logger.info('Fetching news articles from scheduler', {
      page,
      limit,
      category,
      sortBy,
      sortOrder,
      requestId: res.getHeader('x-request-id'),
    });

    const startTime = Date.now();

    // Get articles from dataScheduler instead of newsService
    const result = dataScheduler.getPagedData(page, limit, 'news_article');
    
    // Transform data to match expected format
    const articles = result.items.map(item => ({
      id: item.id,
      title: item.type === 'twitter' ? `Tweet by ${item.author.displayName}` : item.title,
      summary: item.type === 'twitter' ? item.content : item.summary,
      content: item.type === 'twitter' ? item.content : item.content,
      imageUrl: item.type === 'twitter' ? item.author.avatar : item.imageUrl,
      publishedAt: item.publishedAt,
      category: item.category || 'news',
      tags: item.type === 'twitter' ? item.hashtags : item.tags,
      source: item.source,
      author: item.type === 'twitter' ? item.author.displayName : item.author,
      readTime: item.readTime || 3,
      isHot: item.isHot || false,
      isImportant: item.isImportant || false,
      engagement: item.engagement,
    }));

    const duration = Date.now() - startTime;

    logger.info('News articles fetched successfully from scheduler', {
      articlesCount: articles.length,
      total: result.total,
      duration: `${duration}ms`,
      requestId: res.getHeader('x-request-id'),
    });

    const response: NewsResponse = {
      success: true,
      data: {
        items: articles,
        total: result.total,
        page: result.page,
        limit: result.limit,
        hasMore: result.hasMore,
        totalPages: Math.ceil(result.total / result.limit),
      },
      message: 'News articles retrieved successfully',
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(response);
  });

  /**
   * Search news articles
   * GET /api/news/search
   */
  public searchNews = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const query = req.query as unknown as SearchNewsQuery;
    const { q, page = 1, limit = 20, category, sortBy } = query;

    logger.info('Searching news articles in scheduler', {
      query: q,
      page,
      limit,
      category,
      sortBy,
      requestId: res.getHeader('x-request-id'),
    });

    const startTime = Date.now();

    // Search articles using dataScheduler
    const result = dataScheduler.searchData(q, page, limit);
    
    // Transform data to match expected format
    const articles = result.items.map(item => ({
      id: item.id,
      title: item.type === 'twitter' ? `Tweet by ${item.author.displayName}` : item.title,
      summary: item.type === 'twitter' ? item.content : item.summary,
      content: item.type === 'twitter' ? item.content : item.content,
      imageUrl: item.type === 'twitter' ? item.author.avatar : item.imageUrl,
      publishedAt: item.publishedAt,
      category: item.category || 'news',
      tags: item.type === 'twitter' ? item.hashtags : item.tags,
      source: item.source,
      author: item.type === 'twitter' ? item.author.displayName : item.author,
      readTime: item.readTime || 3,
      isHot: item.isHot || false,
      isImportant: item.isImportant || false,
      engagement: item.engagement,
    }));

    const duration = Date.now() - startTime;

    logger.info('News search completed in scheduler', {
      query: q,
      resultsCount: articles.length,
      total: result.total,
      duration: `${duration}ms`,
      requestId: res.getHeader('x-request-id'),
    });

    const response: NewsResponse = {
      success: true,
      data: {
        items: articles,
        total: result.total,
        page: result.page,
        limit: result.limit,
        hasMore: result.hasMore,
        totalPages: Math.ceil(result.total / result.limit),
      },
      message: `Search results for "${q}"`,
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(response);
  });

  /**
   * Get specific news article by ID
   * GET /api/news/:id
   */
  public getArticleById = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;

    if (!id) {
      res.status(400).json({
        success: false,
        data: null,
        error: 'Article ID is required',
        timestamp: new Date().toISOString(),
      });
      return;
    }

    logger.info('Fetching article by ID', {
      articleId: id,
      requestId: res.getHeader('x-request-id'),
    });

    const startTime = Date.now();

    // Get article from news service
    const article = await newsService.getArticleById(id);

    if (!article) {
      logger.warn('Article not found', {
        articleId: id,
        requestId: res.getHeader('x-request-id'),
      });

      const response: ArticleResponse = {
        success: false,
        data: null as never,
        error: 'Article not found',
        message: `Article with ID "${id}" does not exist`,
        timestamp: new Date().toISOString(),
      };

      res.status(404).json(response);
      return;
    }

    const duration = Date.now() - startTime;

    logger.info('Article fetched successfully', {
      articleId: id,
      articleTitle: article.title,
      duration: `${duration}ms`,
      requestId: res.getHeader('x-request-id'),
    });

    const response: ArticleResponse = {
      success: true,
      data: article,
      message: 'Article retrieved successfully',
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(response);
  });

  /**
   * Get trending news articles
   * GET /api/news/trending
   */
  public getTrendingNews = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const limit = parseInt(req.query.limit as string) || 10;

    logger.info('Fetching trending news articles', {
      limit,
      requestId: res.getHeader('x-request-id'),
    });

    const startTime = Date.now();

    // Get trending articles from news service
    const articles = await newsService.getTrendingArticles(limit);

    const duration = Date.now() - startTime;

    logger.info('Trending articles fetched successfully', {
      articlesCount: articles.length,
      duration: `${duration}ms`,
      requestId: res.getHeader('x-request-id'),
    });

    const response: ApiResponse<typeof articles> = {
      success: true,
      data: articles,
      message: 'Trending articles retrieved successfully',
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(response);
  });

  /**
   * Get featured news articles
   * GET /api/news/featured
   */
  public getFeaturedNews = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const limit = parseInt(req.query.limit as string) || 5;

    logger.info('Fetching featured news articles', {
      limit,
      requestId: res.getHeader('x-request-id'),
    });

    const startTime = Date.now();

    // Get featured articles (using trending as proxy for now)
    const articles = await newsService.getTrendingArticles(limit);

    const duration = Date.now() - startTime;

    logger.info('Featured articles fetched successfully', {
      articlesCount: articles.length,
      duration: `${duration}ms`,
      requestId: res.getHeader('x-request-id'),
    });

    const response: ApiResponse<typeof articles> = {
      success: true,
      data: articles,
      message: 'Featured articles retrieved successfully',
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(response);
  });

  /**
   * Get news categories
   * GET /api/news/categories
   */
  public getCategories = asyncHandler(async (_: Request, res: Response): Promise<void> => {
    logger.info('Fetching news categories', {
      requestId: res.getHeader('x-request-id'),
    });

    const startTime = Date.now();

    // Get categories from news service
    const categories = await newsService.getCategories();

    const duration = Date.now() - startTime;

    logger.info('News categories fetched successfully', {
      categoriesCount: categories.length,
      duration: `${duration}ms`,
      requestId: res.getHeader('x-request-id'),
    });

    const response: CategoriesResponse = {
      success: true,
      data: categories,
      message: 'News categories retrieved successfully',
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(response);
  });

  /**
   * Get articles by category
   * GET /api/news/category/:slug
   */
  public getArticlesByCategory = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { slug } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;

    logger.info('Fetching articles by category', {
      categorySlug: slug,
      page,
      limit,
      requestId: res.getHeader('x-request-id'),
    });

    const startTime = Date.now();

    // Get articles by category from news service
    const { articles, total, hasMore } = await newsService.getArticles(
      page,
      limit,
      slug
    );

    const totalPages = Math.ceil(total / limit);
    const duration = Date.now() - startTime;

    logger.info('Category articles fetched successfully', {
      categorySlug: slug,
      articlesCount: articles.length,
      total,
      duration: `${duration}ms`,
      requestId: res.getHeader('x-request-id'),
    });

    const response: NewsResponse = {
      success: true,
      data: {
        items: articles,
        total,
        page,
        limit,
        hasMore,
        totalPages,
      },
      message: `Articles for category "${slug}" retrieved successfully`,
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(response);
  });
}

// Export singleton instance
export const newsController = new NewsController();
export default newsController;
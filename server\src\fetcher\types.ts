import { z } from 'zod';

// ========================================
// Source Configuration Types
// ========================================

export interface SourceConfig {
  id: string;
  name: string;
  baseUrl: string;
  headers: Record<string, string>;
  endpoints: Record<string, string>;
  rateLimit?: {
    requests: number;
    window: number; // in ms
  };
  timeout?: number;
  retries?: number;
  enabled: boolean;
}

export interface ProxyConfig {
  protocol: 'http' | 'https' | 'socks5';
  host: string;
  port: number;
  auth?: {
    username: string;
    password: string;
  };
  enabled: boolean;
}

export interface FetcherConfig {
  sources: SourceConfig[];
  proxies: ProxyConfig[];
  cache: {
    ttl: number;
    maxSize: number;
  };
  headers: {
    userAgents: string[];
    acceptLanguages: string[];
  };
}

// ========================================
// Unified Response Format Types
// ========================================

export interface BaseArticle {
  id: string;
  title: string;
  summary: string;
  content?: string;
  imageUrl?: string;
  sourceUrl?: string;
  publishedAt: string;
  tags: string[];
  source: {
    id: string;
    name: string;
    url: string;
  };
  category?: string;
  author?: string;
  readTime?: number;
  isHot?: boolean;
  isImportant?: boolean;
  engagement?: {
    views?: number;
    likes?: number;
    shares?: number;
  };
}

export interface NewsFlashItem extends BaseArticle {
  type: 'news_flash';
  newsFlashType?: number;
  keywords?: string[];
}

export interface NewsArticleItem extends BaseArticle {
  type: 'news_article';
  brief?: string;
  fullContent?: string;
  relatedArticles?: string[];
}

export interface FeedItem extends BaseArticle {
  type: 'feed';
  important?: boolean;
  label?: string;
  wikis?: Array<{
    id: number;
    name: string;
    subscribed: boolean;
  }>;
}

export interface TwitterItem {
  id: string;
  type: 'twitter';
  author: {
    username: string;
    displayName: string;
    avatar: string;
    verified: boolean;
  };
  content: string;
  publishedAt: string;
  engagement: {
    likes: number;
    retweets: number;
    replies: number;
  };
  hashtags: string[];
  urls: string[];
  source: {
    id: string;
    name: string;
    url: string;
  };
}

export interface PriceData {
  id: string;
  symbol: string;
  name: string;
  price: number;
  change24h: number;
  changePercent24h: number;
  volume24h?: number;
  marketCap?: number;
  timestamp: string;
  source: {
    id: string;
    name: string;
  };
}

export type UnifiedItem = NewsFlashItem | NewsArticleItem | FeedItem | TwitterItem;

// ========================================
// External API Response Types
// ========================================

// ChainCatcher API Types
export interface ChainCatcherResponse {
  data: {
    items: Array<{
      id: number;
      title: string;
      description: string;
      createTime: string;
      releaseTime: string;
      tagNames: string[];
      keywords: string;
      articleType: number;
      newsFlashType: number;
      type: number;
      isHot: number;
      thumb?: string;
    }>;
    total: number;
  };
  result: number;
}

// TechFlow API Types
export interface TechFlowResponse {
  success: string;
  time: string;
  msg: string;
  content: Array<{
    nnewflash_id: string;
    stitle: string;
    sabstract: string;
    scontent: string;
    dcreate_time: string;
    is_hot: string;
    surl: string;
    ncount: string;
    times: string;
    screate_person: string;
  }>;
}

// ForesightNews API Types
export interface ForesightNewsResponse {
  code: number;
  data: {
    has_more: boolean;
    list: Array<{
      id: number;
      title: string;
      brief: string;
      content: string;
      img: string;
      tags: Array<{
        id: number;
        name: string;
        subscribed: boolean;
      }>;
      is_important: boolean;
      important_tag?: {
        id: number;
        name: string;
        subscribed: boolean;
      };
      label?: string;
      source_link: string;
      published_at: number;
      favorited: boolean;
      wikis?: Array<{
        id: number;
        name: string;
        subscribed: boolean;
      }>;
    }>;
    total: number;
  };
  msg: string;
}

// PANews API Types
export interface PANewsResponse {
  errno: number;
  msg: string;
  data: {
    flashNews: Array<{
      date: string;
      week: string;
      month: string;
      unix: number;
      list: Array<{
        id: string;
        type: number;
        publishTime: number;
        img: string;
        title: string;
        desc: string;
        readnum: number;
        tags: string[] | null;
        author: {
          id: string;
          name: string;
          img: string;
          tag: string;
          brief: string;
          follow: number;
        };
        viewPoint: any;
        collection: number;
        like: number;
        lovenum: number;
        status: number;
        topTime: number;
        apppush: number;
        ctime: number;
      }>;
    }>;
    tag: Array<{
      id: string;
      name: string;
    }>;
  };
}

// TheBlockBeats API Types
export interface TheBlockBeatsResponse {
  code: number;
  msg: string;
  data: {
    page: number;
    limit: number;
    list: Array<{
      id: number;
      article_id: number;
      content_id: number;
      type: number;
      is_show_home: number;
      is_detective: number;
      is_top: number;
      is_original: number;
      is_hot: number;
      add_time: number;
      img_url: string;
      c_img_url: string;
      url: string;
      title: string;
      lang: string;
      abstract: string;
      content: string;
      comment_count: number;
      tag_list: string[];
      collection_status: number;
    }>;
  };
}

// Odaily API Types
export interface OdailyResponse {
  code: number;
  data: {
    items: Array<{
      id: number;
      column_id: number;
      post_id: number | null;
      is_top: number;
      pin: number;
      title: string;
      catch_title: string;
      description: string;
      cover: string;
      cover_list: string[];
      news_url_type: string;
      news_url: string;
      extraction_tags: string;
      template_info: {
        template_type: string;
        template_title: string;
        template_cover: string[];
      };
      user_id: number;
      published_at: string;
      created_at: string;
      updated_at: string;
      position: number;
      news_type: number;
      pushed_at: string;
      news_url_title: string;
      extraction_tags_arr: string[];
      share_data: Record<string, any>;
      voted_type: number;
      is_favorite: boolean;
      secondary_tag: any;
      user: {
        id: number;
        name: string;
        avatar_url: string;
      };
      post: any;
    }>;
  };
}

// ========================================
// Validation Schemas
// ========================================

export const FetcherQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  category: z.string().optional(),
  sources: z.string().optional(), // comma-separated source IDs
  sortBy: z.enum(['publishedAt', 'relevance', 'popularity']).default('publishedAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  includeContent: z.coerce.boolean().default(false),
});

export const SearchQuerySchema = z.object({
  q: z.string().min(1).max(200),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(50).default(20),
  sources: z.string().optional(),
  timeRange: z.enum(['1h', '24h', '7d', '30d', 'all']).default('all'),
});

export const PricesQuerySchema = z.object({
  symbols: z.string().optional(), // comma-separated symbols
  vs_currency: z.string().default('usd'),
  limit: z.coerce.number().min(1).max(250).default(100),
  sources: z.string().optional(),
});

// ========================================
// Internal Types
// ========================================

export interface FetchResult<T> {
  success: boolean;
  data: T | null;
  error?: string;
  source: string;
  cached: boolean;
  timestamp: number;
}

export interface AggregatedResult<T> {
  items: T[];
  total: number;
  sources: string[];
  cached: boolean;
  timestamp: number;
  errors?: Array<{
    source: string;
    error: string;
  }>;
}

export interface RequestHeaders {
  'User-Agent': string;
  'Accept': string;
  'Accept-Language': string;
  'Cache-Control': string;
  'Pragma': string;
  'X-Forwarded-For'?: string;
  'Origin'?: string;
  'Referer'?: string;
  [key: string]: string | undefined;
}

export interface FetchOptions {
  timeout?: number;
  retries?: number;
  proxy?: ProxyConfig;
  headers?: Partial<RequestHeaders>;
  cache?: {
    key: string;
    ttl: number;
  };
}

// ========================================
// Utility Types
// ========================================

export type FetcherQuery = z.infer<typeof FetcherQuerySchema>;
export type SearchQuery = z.infer<typeof SearchQuerySchema>;
export type PricesQuery = z.infer<typeof PricesQuerySchema>;

export type SourceResponseType = 
  | ChainCatcherResponse
  | TechFlowResponse
  | ForesightNewsResponse
  | PANewsResponse
  | TheBlockBeatsResponse
  | OdailyResponse;

export interface SourceTransformer<T = SourceResponseType> {
  transform(response: T, sourceId: string): UnifiedItem[];
}

export interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  size: number;
  maxSize: number;
}

export interface FetcherStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  cacheStats: CacheStats;
  sourceStats: Record<string, {
    requests: number;
    successes: number;
    failures: number;
    averageResponseTime: number;
  }>;
}
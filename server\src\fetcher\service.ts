import { BlockchainFetcher } from './fetcher';
import { createFetcherConfig } from './config';
import { 
  UnifiedItem, 
  TwitterItem, 
  PriceData, 
  FetcherQuery, 
  SearchQuery, 
  PricesQuery,
  AggregatedResult 
} from './types';
import { logger } from '@/utils/logger';

export class FetcherService {
  private fetcher: BlockchainFetcher;
  private static instance: FetcherService;

  private constructor() {
    const config = createFetcherConfig();
    this.fetcher = new BlockchainFetcher(config);
    
    logger.info('🔄 FetcherService initialized with sources:', {
      sources: config.sources.filter(s => s.enabled).map(s => s.id),
      proxies: config.proxies.filter(p => p.enabled).length,
      cacheConfig: config.cache,
    });
  }

  public static getInstance(): FetcherService {
    if (!FetcherService.instance) {
      FetcherService.instance = new FetcherService();
    }
    return FetcherService.instance;
  }

  /**
   * Fetch aggregated news from multiple sources
   */
  async getNews(query: FetcherQuery): Promise<AggregatedResult<UnifiedItem>> {
    const startTime = Date.now();
    
    try {
      const sources = query.sources ? query.sources.split(',').map(s => s.trim()) : undefined;
      
      const result = await this.fetcher.fetchNews(
        sources,
        query.page,
        query.limit,
        {
          timeout: 15000,
          retries: 2,
          cache: {
            key: `news-${JSON.stringify(query)}`,
            ttl: 300, // 5 minutes
          },
        }
      );

      // Sort results if needed
      if (query.sortBy === 'publishedAt') {
        result.items.sort((a, b) => {
          const dateA = new Date(a.publishedAt).getTime();
          const dateB = new Date(b.publishedAt).getTime();
          return query.sortOrder === 'desc' ? dateB - dateA : dateA - dateB;
        });
      }

      logger.info(`📰 Fetched ${result.items.length} news items from ${result.sources.length} sources`, {
        query,
        sources: result.sources,
        cached: result.cached,
        duration: Date.now() - startTime,
      });

      return result;
    } catch (error) {
      logger.error('❌ Error fetching news:', error);
      throw error;
    }
  }

  /**
   * Fetch aggregated feed from multiple sources
   */
  async getFeed(query: FetcherQuery): Promise<AggregatedResult<UnifiedItem>> {
    const startTime = Date.now();
    
    try {
      const sources = query.sources ? query.sources.split(',').map(s => s.trim()) : undefined;
      
      const result = await this.fetcher.fetchFeed(
        sources,
        query.page,
        query.limit,
        {
          timeout: 15000,
          retries: 2,
          cache: {
            key: `feed-${JSON.stringify(query)}`,
            ttl: 300,
          },
        }
      );

      // Sort results
      if (query.sortBy === 'publishedAt') {
        result.items.sort((a, b) => {
          const dateA = new Date(a.publishedAt).getTime();
          const dateB = new Date(b.publishedAt).getTime();
          return query.sortOrder === 'desc' ? dateB - dateA : dateA - dateB;
        });
      }

      logger.info(`📋 Fetched ${result.items.length} feed items from ${result.sources.length} sources`, {
        query,
        sources: result.sources,
        cached: result.cached,
        duration: Date.now() - startTime,
      });

      return result;
    } catch (error) {
      logger.error('❌ Error fetching feed:', error);
      throw error;
    }
  }

  /**
   * Fetch flash news from multiple sources
   */
  async getFlash(query: FetcherQuery): Promise<AggregatedResult<UnifiedItem>> {
    const startTime = Date.now();
    
    try {
      const sources = query.sources ? query.sources.split(',').map(s => s.trim()) : undefined;
      
      const result = await this.fetcher.fetchFlash(
        sources,
        query.page,
        query.limit,
        {
          timeout: 15000,
          retries: 2,
          cache: {
            key: `flash-${JSON.stringify(query)}`,
            ttl: 180, // 3 minutes for flash news
          },
        }
      );

      // Sort by publishedAt descending (latest first)
      result.items.sort((a, b) => {
        const dateA = new Date(a.publishedAt).getTime();
        const dateB = new Date(b.publishedAt).getTime();
        return dateB - dateA;
      });

      logger.info(`⚡ Fetched ${result.items.length} flash items from ${result.sources.length} sources`, {
        query,
        sources: result.sources,
        cached: result.cached,
        duration: Date.now() - startTime,
      });

      return result;
    } catch (error) {
      logger.error('❌ Error fetching flash news:', error);
      throw error;
    }
  }

  /**
   * Search news across multiple sources
   */
  async searchNews(query: SearchQuery): Promise<AggregatedResult<UnifiedItem>> {
    const startTime = Date.now();
    
    try {
      const sources = query.sources ? query.sources.split(',').map(s => s.trim()) : undefined;
      
      const result = await this.fetcher.searchNews(
        query.q,
        sources,
        query.page,
        query.limit,
        {
          timeout: 20000,
          retries: 2,
          cache: {
            key: `search-${JSON.stringify(query)}`,
            ttl: 600, // 10 minutes for search results
          },
        }
      );

      // Filter by time range if specified
      if (query.timeRange !== 'all') {
        const now = Date.now();
        const timeRangeMs = {
          '1h': 60 * 60 * 1000,
          '24h': 24 * 60 * 60 * 1000,
          '7d': 7 * 24 * 60 * 60 * 1000,
          '30d': 30 * 24 * 60 * 60 * 1000,
        }[query.timeRange];

        if (timeRangeMs) {
          result.items = result.items.filter(item => {
            const itemTime = new Date(item.publishedAt).getTime();
            return now - itemTime <= timeRangeMs;
          });
          result.total = result.items.length;
        }
      }

      logger.info(`🔍 Search for "${query.q}" found ${result.items.length} items from ${result.sources.length} sources`, {
        query,
        sources: result.sources,
        cached: result.cached,
        duration: Date.now() - startTime,
      });

      return result;
    } catch (error) {
      logger.error('❌ Error searching news:', error);
      throw error;
    }
  }

  /**
   * Fetch hot tweets (placeholder implementation)
   */
  async getHotTweets(query: FetcherQuery): Promise<AggregatedResult<TwitterItem>> {
    const startTime = Date.now();
    
    try {
      const sources = query.sources ? query.sources.split(',').map(s => s.trim()) : undefined;
      
      const result = await this.fetcher.fetchHotTweets(
        sources,
        query.page,
        query.limit,
        {
          timeout: 15000,
          retries: 2,
          cache: {
            key: `hot-tweets-${JSON.stringify(query)}`,
            ttl: 300,
          },
        }
      );

      logger.info(`🐦 Fetched ${result.items.length} hot tweets from ${result.sources.length} sources`, {
        query,
        sources: result.sources,
        cached: result.cached,
        duration: Date.now() - startTime,
      });

      return result;
    } catch (error) {
      logger.error('❌ Error fetching hot tweets:', error);
      throw error;
    }
  }

  /**
   * Fetch price data (placeholder implementation)
   */
  async getPrices(query: PricesQuery): Promise<AggregatedResult<PriceData>> {
    const startTime = Date.now();
    
    try {
      const _sources = query.sources ? query.sources.split(',').map(s => s.trim()) : undefined;
      const symbols = query.symbols ? query.symbols.split(',').map(s => s.trim()) : undefined;
      
      const result = await this.fetcher.fetchPrices(
        symbols,
        {
          timeout: 15000,
          retries: 2,
          cache: {
            key: `prices-${JSON.stringify(query)}`,
            ttl: 60, // 1 minute for price data
          },
        }
      );

      logger.info(`💰 Fetched ${result.items.length} price items from ${result.sources.length} sources`, {
        query,
        sources: result.sources,
        cached: result.cached,
        duration: Date.now() - startTime,
      });

      return result;
    } catch (error) {
      logger.error('❌ Error fetching prices:', error);
      throw error;
    }
  }

  /**
   * Get fetcher statistics
   */
  getStats() {
    return this.fetcher.getStats();
  }

  /**
   * Clear all caches
   */
  clearCache(): void {
    this.fetcher.clearCache();
    logger.info('🗑️ Fetcher cache cleared');
  }

  /**
   * Get available sources
   */
  getAvailableSources(): Array<{ id: string; name: string; enabled: boolean }> {
    const config = createFetcherConfig();
    return config.sources.map(source => ({
      id: source.id,
      name: source.name,
      enabled: source.enabled,
    }));
  }

  /**
   * Update fetcher configuration
   */
  updateConfig(config: any): void {
    this.fetcher.updateConfig(config);
    logger.info('⚙️ Fetcher configuration updated');
  }
}

// Export singleton instance
export const fetcherService = FetcherService.getInstance();
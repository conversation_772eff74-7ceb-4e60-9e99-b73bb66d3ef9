import { Router } from 'express';
import { dataScheduler } from './scheduler';
import { FetcherQuerySchema, SearchQuerySchema } from '../fetcher/types';
import { logger } from '@/utils/logger';

const router: Router = Router();

/**
 * 获取聚合数据
 */
router.get('/data', async (req, res) => {
  try {
    const query = FetcherQuerySchema.parse(req.query);
    const result = dataScheduler.getPagedData(query.page, query.limit, req.query.type as string);
    
    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('❌ Error getting aggregated data:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * 搜索数据
 */
router.get('/search', async (req, res) => {
  try {
    const query = SearchQuerySchema.parse(req.query);
    const result = dataScheduler.searchData(query.q, query.page, query.limit);
    
    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('❌ Error searching data:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * 获取统计信息
 */
router.get('/stats', async (req, res) => {
  try {
    const stats = dataScheduler.getStats();
    
    res.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('❌ Error getting stats:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * 启动数据收集
 */
router.post('/start', async (_req, res) => {
  try {
    dataScheduler.start();
    
    res.json({
      success: true,
      message: 'Data collection started',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('❌ Error starting data collection:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * 停止数据收集
 */
router.post('/stop', async (_req, res) => {
  try {
    dataScheduler.stop();
    
    res.json({
      success: true,
      message: 'Data collection stopped',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('❌ Error stopping data collection:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * 更新配置
 */
router.post('/config', async (req, res) => {
  try {
    dataScheduler.updateConfig(req.body);
    
    res.json({
      success: true,
      message: 'Configuration updated',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('❌ Error updating configuration:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * 清除缓存
 */
router.post('/clear-cache', async (_req, res) => {
  try {
    dataScheduler.clearCache();
    
    res.json({
      success: true,
      message: 'Cache cleared',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('❌ Error clearing cache:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * 获取新闻数据
 */
router.get('/news', async (req, res) => {
  try {
    const query = FetcherQuerySchema.parse(req.query);
    const result = dataScheduler.getPagedData(query.page, query.limit, 'news_flash');
    
    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('❌ Error getting news data:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * 获取快讯数据
 */
router.get('/flash', async (req, res) => {
  try {
    const query = FetcherQuerySchema.parse(req.query);
    const result = dataScheduler.getPagedData(query.page, query.limit, 'news_flash');
    
    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('❌ Error getting flash data:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * 获取订阅源数据
 */
router.get('/feed', async (req, res) => {
  try {
    const query = FetcherQuerySchema.parse(req.query);
    const result = dataScheduler.getPagedData(query.page, query.limit, 'feed');
    
    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('❌ Error getting feed data:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export default router;
{"compilerOptions": {"target": "ES2022", "module": "CommonJS", "moduleResolution": "node", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "pretty": true, "typeRoots": ["./node_modules/@types"], "types": ["node", "jest"], "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/types": ["./types"], "@/services": ["./services"], "@/controllers": ["./controllers"], "@/middlewares": ["./middlewares"], "@/utils": ["./utils"], "@/routes": ["./routes"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"require": ["tsconfig-paths/register"], "transpileOnly": true, "files": true}}
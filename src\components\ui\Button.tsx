import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../common/ThemeProvider';
import { FONT_SIZES, FONT_WEIGHTS, SPACING, BORDER_RADIUS, SHADOWS } from '../../constants';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  icon?: keyof typeof Ionicons.glyphMap;
  iconPosition?: 'left' | 'right';
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  style,
  textStyle,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors, variant, size);

  const handlePress = () => {
    if (!disabled && !loading) {
      onPress();
    }
  };

  const renderIcon = () => {
    if (loading) {
      return (
        <ActivityIndicator
          size="small"
          color={getTextColor()}
          style={[
            iconPosition === 'left' ? styles.iconLeft : styles.iconRight,
          ]}
        />
      );
    }

    if (icon) {
      return (
        <Ionicons
          name={icon}
          size={getIconSize()}
          color={getTextColor()}
          style={[
            iconPosition === 'left' ? styles.iconLeft : styles.iconRight,
          ]}
        />
      );
    }

    return null;
  };

  const getTextColor = () => {
    if (disabled) {
      return colors.textTertiary;
    }

    switch (variant) {
      case 'primary':
        return '#FFFFFF';
      case 'secondary':
        return '#FFFFFF';
      case 'outline':
        return colors.primary;
      case 'ghost':
        return colors.primary;
      default:
        return colors.text;
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 16;
      case 'md':
        return 20;
      case 'lg':
        return 24;
      default:
        return 20;
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.button,
        disabled && styles.disabled,
        style,
      ]}
      onPress={handlePress}
      disabled={disabled || loading}
      activeOpacity={0.7}
    >
      {iconPosition === 'left' && renderIcon()}
      <Text style={[styles.text, textStyle]}>
        {title}
      </Text>
      {iconPosition === 'right' && renderIcon()}
    </TouchableOpacity>
  );
};

const createStyles = (
  colors: any,
  variant: ButtonProps['variant'],
  size: ButtonProps['size']
) => {
  const getButtonStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: BORDER_RADIUS.md,
      ...SHADOWS.sm,
    };

    // Size styles
    switch (size) {
      case 'sm':
        baseStyle.paddingHorizontal = SPACING.md;
        baseStyle.paddingVertical = SPACING.sm;
        baseStyle.minHeight = 36;
        break;
      case 'md':
        baseStyle.paddingHorizontal = SPACING.lg;
        baseStyle.paddingVertical = SPACING.md;
        baseStyle.minHeight = 44;
        break;
      case 'lg':
        baseStyle.paddingHorizontal = SPACING.xl;
        baseStyle.paddingVertical = SPACING.lg;
        baseStyle.minHeight = 52;
        break;
    }

    // Variant styles
    switch (variant) {
      case 'primary':
        baseStyle.backgroundColor = colors.primary;
        break;
      case 'secondary':
        baseStyle.backgroundColor = colors.secondary;
        break;
      case 'outline':
        baseStyle.backgroundColor = 'transparent';
        baseStyle.borderWidth = 1;
        baseStyle.borderColor = colors.primary;
        break;
      case 'ghost':
        baseStyle.backgroundColor = 'transparent';
        break;
    }

    return baseStyle;
  };

  const getTextStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      fontWeight: FONT_WEIGHTS.semibold,
      textAlign: 'center',
    };

    switch (size) {
      case 'sm':
        baseStyle.fontSize = FONT_SIZES.sm;
        break;
      case 'md':
        baseStyle.fontSize = FONT_SIZES.base;
        break;
      case 'lg':
        baseStyle.fontSize = FONT_SIZES.lg;
        break;
    }

    return baseStyle;
  };

  return StyleSheet.create({
    button: getButtonStyle(),
    text: {
      ...getTextStyle(),
      color: variant === 'primary' || variant === 'secondary' ? '#FFFFFF' : colors.primary,
    },
    disabled: {
      opacity: 0.5,
      backgroundColor: colors.border,
    },
    iconLeft: {
      marginRight: SPACING.sm,
    },
    iconRight: {
      marginLeft: SPACING.sm,
    },
  });
};

export default Button;

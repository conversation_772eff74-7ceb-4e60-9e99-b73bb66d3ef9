import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAppStore } from '../../store';
import { COLORS, SPACING, FONT_SIZES } from '../../constants';

interface EmptyStateProps {
  icon?: keyof typeof Ionicons.glyphMap;
  title: string;
  description?: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  icon = 'document-text-outline',
  title,
  description,
}) => {
  const { theme } = useAppStore();
  const colors = COLORS[theme];
  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      <Ionicons
        name={icon}
        size={64}
        color={colors.textSecondary}
        style={styles.icon}
      />
      <Text style={styles.title}>{title}</Text>
      {description && <Text style={styles.description}>{description}</Text>}
    </View>
  );
};

const createStyles = (colors: typeof COLORS.light) =>
  StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: SPACING.xl,
    },
    icon: {
      marginBottom: SPACING.md,
      opacity: 0.6,
    },
    title: {
      fontSize: FONT_SIZES.lg,
      fontWeight: '600',
      color: colors.text,
      textAlign: 'center',
      marginBottom: SPACING.sm,
    },
    description: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      textAlign: 'center',
      lineHeight: 22,
    },
  });

export default EmptyState;

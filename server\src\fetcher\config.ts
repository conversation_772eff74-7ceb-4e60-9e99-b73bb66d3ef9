import { SourceConfig, ProxyConfig, FetcherConfig } from './types';

// ========================================
// Source Configurations
// ========================================

export const SOURCE_CONFIGS: SourceConfig[] = [
  {
    id: 'chaincatcher',
    name: 'ChainCatcher',
    baseUrl: 'https://www.chaincatcher.com',
    headers: {
      'accept': 'application/json, text/plain, */*',
      'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'cache-control': 'no-cache',
      'content-type': 'application/json',
      'language': 'zh-CN',
      'pragma': 'no-cache',
      'priority': 'u=1, i',
      'ref': 'https://www.chaincatcher.com/news',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-origin',
      'Origin': 'https://www.chaincatcher.com',
      'Referer': 'https://www.chaincatcher.com/news',
    },
    endpoints: {
      news: '/pc/content/page?channel=PC&cversion=1.0.0&requestid=PC',
      flash: '/pc/content/page?channel=PC&cversion=1.0.0&requestid=PC',
    },
    rateLimit: {
      requests: 60,
      window: 60000, // 1 minute
    },
    timeout: 10000,
    retries: 3,
    enabled: true,
  },
  {
    id: 'techflow',
    name: 'TechFlow',
    baseUrl: 'https://www.techflowpost.com',
    headers: {
      'accept': '*/*',
      'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'cache-control': 'no-cache',
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
      'pragma': 'no-cache',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-origin',
      'x-requested-with': 'XMLHttpRequest',
      'Origin': 'https://www.techflowpost.com',
      'Referer': 'https://www.techflowpost.com/newsletter/index.html',
    },
    endpoints: {
      news: '/ashx/newflash_index.ashx',
      flash: '/ashx/newflash_index.ashx',
    },
    rateLimit: {
      requests: 60,
      window: 60000,
    },
    timeout: 10000,
    retries: 3,
    enabled: true,
  },
  {
    id: 'foresightnews',
    name: 'Foresight News',
    baseUrl: 'https://api.foresightnews.pro',
    headers: {
      'accept': 'application/json, text/plain, */*',
      'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'cache-control': 'no-cache',
      'pragma': 'no-cache',
      'priority': 'u=1, i',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-site',
      'x-requested-with': 'XMLHttpRequest',
      'Origin': 'https://foresightnews.pro',
      'Referer': 'https://foresightnews.pro/',
    },
    endpoints: {
      news: '/v1/news',
      feed: '/v2/feed',
      dayNews: '/v1/dayNews',
    },
    rateLimit: {
      requests: 100,
      window: 60000,
    },
    timeout: 10000,
    retries: 3,
    enabled: true,
  },
  {
    id: 'panews',
    name: 'PANews',
    baseUrl: 'https://api.panewslab.com',
    headers: {
      'accept': '*/*',
      'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'cache-control': 'no-cache',
      'pragma': 'no-cache',
      'priority': 'u=1, i',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-site',
      'Origin': 'https://www.panewslab.com',
      'Referer': 'https://www.panewslab.com/',
    },
    endpoints: {
      news: '/webapi/flashnews',
      flash: '/webapi/flashnews',
    },
    rateLimit: {
      requests: 60,
      window: 60000,
    },
    timeout: 10000,
    retries: 3,
    enabled: true,
  },
  {
    id: 'theblockbeats',
    name: 'TheBlockBeats',
    baseUrl: 'https://api.blockbeats.cn',
    headers: {
      'accept': 'application/json, text/plain, */*',
      'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'cache-control': 'no-cache',
      'lang': 'cn',
      'pragma': 'no-cache',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'cross-site',
      'token': '',
      'Origin': 'https://www.theblockbeats.info',
      'Referer': 'https://www.theblockbeats.info/',
    },
    endpoints: {
      news: '/v2/newsflash/list',
      flash: '/v2/newsflash/list',
    },
    rateLimit: {
      requests: 60,
      window: 60000,
    },
    timeout: 10000,
    retries: 3,
    enabled: true,
  },
  {
    id: 'odaily',
    name: 'Odaily',
    baseUrl: 'https://www.odaily.news',
    headers: {
      'accept': 'application/json, text/plain, */*',
      'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'cache-control': 'no-cache',
      'pragma': 'no-cache',
      'priority': 'u=1, i',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-origin',
      'Origin': 'https://www.odaily.news',
      'Referer': 'https://www.odaily.news/newsflash',
    },
    endpoints: {
      news: '/api/pp/api/info-flow/newsflash_columns/newsflashes',
      flash: '/api/pp/api/info-flow/newsflash_columns/newsflashes',
    },
    rateLimit: {
      requests: 60,
      window: 60000,
    },
    timeout: 10000,
    retries: 3,
    enabled: true,
  },
];

// ========================================
// Default Proxy Configurations
// ========================================

export const DEFAULT_PROXY_CONFIGS: ProxyConfig[] = [
  // Add your proxy configurations here
  // Examples:
  // {
  //   protocol: 'http',
  //   host: '127.0.0.1',
  //   port: 8080,
  //   enabled: false,
  // },
  // {
  //   protocol: 'socks5',
  //   host: '127.0.0.1',
  //   port: 1080,
  //   auth: {
  //     username: 'user',
  //     password: 'pass',
  //   },
  //   enabled: false,
  // },
];

// ========================================
// Default Fetcher Configuration
// ========================================

export const DEFAULT_FETCHER_CONFIG: FetcherConfig = {
  sources: SOURCE_CONFIGS,
  proxies: DEFAULT_PROXY_CONFIGS,
  cache: {
    ttl: 300, // 5 minutes
    maxSize: 1000,
  },
  headers: {
    userAgents: [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:132.0) Gecko/20100101 Firefox/132.0',
    ],
    acceptLanguages: [
      'zh-CN,zh;q=0.9,en;q=0.8',
      'en-US,en;q=0.9,zh;q=0.8',
      'zh-TW,zh;q=0.9,en;q=0.8',
    ],
  },
};

// ========================================
// Environment-based Configuration
// ========================================

export function createFetcherConfig(): FetcherConfig {
  const config = { ...DEFAULT_FETCHER_CONFIG };

  // Override with environment variables
  if (process.env.FETCHER_CACHE_TTL) {
    config.cache.ttl = parseInt(process.env.FETCHER_CACHE_TTL, 10);
  }

  if (process.env.FETCHER_CACHE_MAX_SIZE) {
    config.cache.maxSize = parseInt(process.env.FETCHER_CACHE_MAX_SIZE, 10);
  }

  // Add proxies from environment
  if (process.env.HTTP_PROXY) {
    const proxyUrl = new URL(process.env.HTTP_PROXY);
    config.proxies.push({
      protocol: proxyUrl.protocol.slice(0, -1) as 'http' | 'https',
      host: proxyUrl.hostname,
      port: parseInt(proxyUrl.port, 10),
      auth: proxyUrl.username && proxyUrl.password ? {
        username: proxyUrl.username,
        password: proxyUrl.password,
      } : undefined,
      enabled: true,
    });
  }

  if (process.env.HTTPS_PROXY) {
    const proxyUrl = new URL(process.env.HTTPS_PROXY);
    config.proxies.push({
      protocol: proxyUrl.protocol.slice(0, -1) as 'http' | 'https',
      host: proxyUrl.hostname,
      port: parseInt(proxyUrl.port, 10),
      auth: proxyUrl.username && proxyUrl.password ? {
        username: proxyUrl.username,
        password: proxyUrl.password,
      } : undefined,
      enabled: true,
    });
  }

  if (process.env.SOCKS_PROXY) {
    const proxyUrl = new URL(process.env.SOCKS_PROXY);
    config.proxies.push({
      protocol: 'socks5',
      host: proxyUrl.hostname,
      port: parseInt(proxyUrl.port, 10),
      auth: proxyUrl.username && proxyUrl.password ? {
        username: proxyUrl.username,
        password: proxyUrl.password,
      } : undefined,
      enabled: true,
    });
  }

  // Disable sources from environment
  if (process.env.DISABLED_SOURCES) {
    const disabledSources = process.env.DISABLED_SOURCES.split(',').map(s => s.trim());
    config.sources.forEach(source => {
      if (disabledSources.includes(source.id)) {
        source.enabled = false;
      }
    });
  }

  return config;
}
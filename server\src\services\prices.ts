import axios, { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { logger, logHelpers } from '@/utils/logger';
import { cacheManager, cacheKeys } from '@/utils/cache';
import {
  CoinGeckoPrice,
  CryptoCurrency,
  CryptoPriceData,
  ServiceOptions,
  ExternalApiConfig,
} from '@/types';
import { handleExternalApiError } from '@/middlewares/error';

// Extended interface for axios config with metadata
interface AxiosConfigWithMetadata extends InternalAxiosRequestConfig {
  metadata?: {
    startTime: number;
  };
}

/**
 * CoinGecko API service for cryptocurrency price data
 */
class PricesService {
  private readonly apiClient: AxiosInstance;
  private readonly config: ExternalApiConfig;

  constructor() {
    this.config = {
      baseUrl: process.env.COINGECKO_API_BASE_URL || 'https://api.coingecko.com/api/v3',
      apiKey: process.env.COINGECKO_API_KEY || undefined,
      timeout: 10000,
      retries: 3,
      rateLimit: {
        requests: 100,
        window: 60 * 1000, // 1 minute for free tier
      },
    };

    this.apiClient = axios.create({
      baseURL: this.config.baseUrl,
      timeout: this.config.timeout,
      headers: {
        'User-Agent': 'ChainMix-Backend/1.0.0',
        ...(this.config.apiKey && { 'x-cg-demo-api-key': this.config.apiKey }),
      },
    });

    this.setupInterceptors();
  }

  /**
   * Setup axios interceptors for logging and error handling
   */
  private setupInterceptors(): void {
    // Request interceptor
    this.apiClient.interceptors.request.use(
      (config: AxiosConfigWithMetadata) => {
        config.metadata = { startTime: Date.now() };
        logger.debug('CoinGecko request', {
          method: config.method,
          url: config.url,
          params: config.params,
        });
        return config;
      },
      (error) => {
        logger.error('CoinGecko request error', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.apiClient.interceptors.response.use(
      (response: AxiosResponse) => {
        const config = response.config as AxiosConfigWithMetadata;
        const duration = Date.now() - (config.metadata?.startTime || Date.now());
        logHelpers.logExternalApiCall(
          'CoinGecko',
          response.config.url || '',
          response.config.method?.toUpperCase() || 'GET',
          response.status,
          duration
        );
        return response;
      },
      (error) => {
        const duration = Date.now() - (error.config?.metadata?.startTime || Date.now());
        const statusCode = error.response?.status || 0;
        logHelpers.logExternalApiCall(
          'CoinGecko',
          error.config?.url || '',
          error.config?.method?.toUpperCase() || 'GET',
          statusCode,
          duration
        );
        return Promise.reject(error);
      }
    );
  }

  /**
   * Get cryptocurrency prices with pagination
   */
  public async getCryptocurrencies(
    page: number = 1,
    limit: number = 100,
    currency: string = 'usd',
    order: string = 'market_cap_desc',
    options: ServiceOptions = {}
  ): Promise<{ currencies: CryptoCurrency[]; hasMore: boolean }> {
    try {
      const cacheKey = cacheKeys.prices.coins(page, limit, currency);
      
      // Check cache first
      const cachedResult = cacheManager.get<{ currencies: CryptoCurrency[]; hasMore: boolean }>(cacheKey);
      if (cachedResult && !options.cacheKey) {
        logHelpers.logExternalApiCall('CoinGecko', '/coins/markets', 'GET', 200, 0, true);
        return cachedResult;
      }

      const response = await this.apiClient.get<CoinGeckoPrice[]>('/coins/markets', {
        params: {
          vs_currency: currency,
          order,
          per_page: Math.min(limit, 250), // CoinGecko max is 250
          page,
          sparkline: false,
          price_change_percentage: '24h',
        },
      });

      const currencies = response.data.map(this.transformCoinGeckoData);
      
      const result = {
        currencies,
        hasMore: response.data.length === limit, // If we got full page, there might be more
      };

      // Cache the result
      cacheManager.set(cacheKey, result, options.cacheTTL || 300); // 5 minutes default

      return result;
    } catch (error) {
      throw handleExternalApiError(error, 'CoinGecko');
    }
  }

  /**
   * Get specific cryptocurrency data
   */
  public async getCryptocurrency(
    id: string,
    currency: string = 'usd',
    options: ServiceOptions = {}
  ): Promise<CryptoCurrency> {
    try {
      const cacheKey = cacheKeys.prices.coin(id, currency);
      
      // Check cache first
      const cachedResult = cacheManager.get<CryptoCurrency>(cacheKey);
      if (cachedResult && !options.cacheKey) {
        logHelpers.logExternalApiCall('CoinGecko', `/coins/${id}`, 'GET', 200, 0, true);
        return cachedResult;
      }

      const response = await this.apiClient.get<CoinGeckoPrice[]>(`/coins/markets`, {
        params: {
          ids: id,
          vs_currency: currency,
          sparkline: false,
          price_change_percentage: '24h',
        },
      });

      if (!response.data || !Array.isArray(response.data) || response.data.length === 0) {
        throw new Error(`Cryptocurrency with id '${id}' not found`);
      }

      const firstItem = response.data[0];
      if (!firstItem) {
        throw new Error(`Cryptocurrency with id '${id}' not found`);
      }

      const currency_data = this.transformCoinGeckoData(firstItem);

      // Cache the result
      cacheManager.set(cacheKey, currency_data, options.cacheTTL || 300); // 5 minutes

      return currency_data;
    } catch (error) {
      throw handleExternalApiError(error, 'CoinGecko');
    }
  }

  /**
   * Get price history for a cryptocurrency
   */
  public async getPriceHistory(
    id: string,
    days: string = '7',
    currency: string = 'usd',
    options: ServiceOptions = {}
  ): Promise<CryptoPriceData> {
    try {
      const cacheKey = cacheKeys.prices.history(id, days, currency);
      
      // Check cache first
      const cachedResult = cacheManager.get<CryptoPriceData>(cacheKey);
      if (cachedResult && !options.cacheKey) {
        logHelpers.logExternalApiCall('CoinGecko', `/coins/${id}/market_chart`, 'GET', 200, 0, true);
        return cachedResult;
      }

      // Get both current data and price history
      const [currentDataResponse, historyResponse] = await Promise.all([
        this.apiClient.get<CoinGeckoPrice[]>('/coins/markets', {
          params: {
            ids: id,
            vs_currency: currency,
            sparkline: false,
          },
        }),
        this.apiClient.get<{
          prices: [number, number][];
          market_caps: [number, number][];
          total_volumes: [number, number][];
        }>(`/coins/${id}/market_chart`, {
          params: {
            vs_currency: currency,
            days,
            interval: this.getIntervalForDays(days),
          },
        }),
      ]);

      if (!currentDataResponse.data || !Array.isArray(currentDataResponse.data) || currentDataResponse.data.length === 0) {
        throw new Error(`Cryptocurrency with id '${id}' not found`);
      }

      const firstItem = currentDataResponse.data[0];
      if (!firstItem) {
        throw new Error(`Cryptocurrency with id '${id}' not found`);
      }

      const currentData = this.transformCoinGeckoData(firstItem);
      const priceHistory = historyResponse.data.prices.map(([timestamp, price]) => ({
        timestamp,
        price,
      }));

      const result: CryptoPriceData = {
        currency: currentData,
        priceHistory,
        timeframe: this.mapDaysToTimeframe(days),
      };

      // Cache the result with longer TTL for longer periods
      const cacheTTL = this.getCacheTTLForDays(days);
      cacheManager.set(cacheKey, result, options.cacheTTL || cacheTTL);

      return result;
    } catch (error) {
      throw handleExternalApiError(error, 'CoinGecko');
    }
  }

  /**
   * Get trending cryptocurrencies
   */
  public async getTrendingCryptocurrencies(
    options: ServiceOptions = {}
  ): Promise<CryptoCurrency[]> {
    try {
      const cacheKey = cacheKeys.prices.trending();
      
      // Check cache first
      const cachedResult = cacheManager.get<CryptoCurrency[]>(cacheKey);
      if (cachedResult && !options.cacheKey) {
        logHelpers.logExternalApiCall('CoinGecko', '/search/trending', 'GET', 200, 0, true);
        return cachedResult;
      }

      const response = await this.apiClient.get<{
        coins: Array<{
          item: {
            id: string;
            name: string;
            symbol: string;
            market_cap_rank: number;
            thumb: string;
          };
        }>;
      }>('/search/trending');

      // Get detailed data for trending coins
      const trendingIds = response.data.coins.map(coin => coin.item.id).join(',');
      
      const detailedResponse = await this.apiClient.get<CoinGeckoPrice[]>('/coins/markets', {
        params: {
          ids: trendingIds,
          vs_currency: 'usd',
          sparkline: false,
          price_change_percentage: '24h',
        },
      });

      const currencies = detailedResponse.data.map(this.transformCoinGeckoData);

      // Cache the result
      cacheManager.set(cacheKey, currencies, options.cacheTTL || 600); // 10 minutes

      return currencies;
    } catch (error) {
      throw handleExternalApiError(error, 'CoinGecko');
    }
  }

  /**
   * Get top cryptocurrencies by market cap
   */
  public async getTopCryptocurrencies(
    limit: number = 10,
    currency: string = 'usd',
    options: ServiceOptions = {}
  ): Promise<CryptoCurrency[]> {
    const { currencies } = await this.getCryptocurrencies(1, limit, currency, 'market_cap_desc', options);
    return currencies;
  }

  /**
   * Transform CoinGecko data to our format
   */
  private transformCoinGeckoData = (data: CoinGeckoPrice): CryptoCurrency => ({
    id: data.id,
    symbol: data.symbol.toUpperCase(),
    name: data.name,
    image: data.image,
    currentPrice: data.current_price,
    marketCap: data.market_cap,
    marketCapRank: data.market_cap_rank,
    fullyDilutedValuation: data.fully_diluted_valuation,
    totalVolume: data.total_volume,
    high24h: data.high_24h,
    low24h: data.low_24h,
    priceChange24h: data.price_change_24h,
    priceChangePercentage24h: data.price_change_percentage_24h,
    marketCapChange24h: data.market_cap_change_24h,
    marketCapChangePercentage24h: data.market_cap_change_percentage_24h,
    circulatingSupply: data.circulating_supply,
    totalSupply: data.total_supply,
    maxSupply: data.max_supply,
    ath: data.ath,
    athChangePercentage: data.ath_change_percentage,
    athDate: data.ath_date,
    atl: data.atl,
    atlChangePercentage: data.atl_change_percentage,
    atlDate: data.atl_date,
    lastUpdated: data.last_updated,
  });

  /**
   * Get appropriate interval for the given days
   */
  private getIntervalForDays(days: string): string {
    const daysNum = parseInt(days, 10);
    if (daysNum <= 1) return 'minutely';
    if (daysNum <= 90) return 'hourly';
    return 'daily';
  }

  /**
   * Map days to timeframe enum
   */
  private mapDaysToTimeframe(days: string): '1h' | '24h' | '7d' | '30d' | '1y' {
    switch (days) {
      case '1': return '24h';
      case '7': return '7d';
      case '30': return '30d';
      case '365': return '1y';
      default: return '7d';
    }
  }

  /**
   * Get cache TTL based on time period
   */
  private getCacheTTLForDays(days: string): number {
    const daysNum = parseInt(days, 10);
    if (daysNum <= 1) return 60; // 1 minute for short periods
    if (daysNum <= 7) return 300; // 5 minutes for week
    if (daysNum <= 30) return 600; // 10 minutes for month
    return 1800; // 30 minutes for longer periods
  }
}

// Export singleton instance
export const pricesService = new PricesService();
export default pricesService;
chainMix 是一个基于 React Native + Expo 技术栈开发的跨平台移动应用，融合AI智能分析，为用户提供最新的区块链和加密货币资讯。当前目录就是项目工程目录。
我有个后端服务在 @server 是基于 Node.js 聚合几个 API 作为提供服务的 API。
请帮我阅读前后端代码，然后帮我解决以下问题：
1. 核对前端页面和后端 API，查看前端页面模块是否合理。比如：资讯、快讯、文章、推特帖子、AI助手、发现等页面，是否合理、是否足够？查看后端 API 是否有对应实现。
2. 对接前后端 API，核对字段，请求方法、端口等。
3. 检查前后端字段是否合理，字段是否对齐。
4. 增加、或者修复你认为 P0 级别的任务。


----------------------------------------------------------------------------------

通过pnpm dev:web 可以在本地启动web服务，通过cd server && pnpm dev 可以启动后端API服务。你可以通过工具 playwright 查看页面。
0. 请阅读前后端源代码。
1. 我已经启动了前后端的服务。
2. 目前我已经有一版初步的前端UI，后端API代码。
3. 目前正在对接前后端API中  
    3.1 请帮我继续对接前后端API，包括但不局限于字段、接口、请求方法、端口等。
    3.2 请帮我实现所有涉及的细节。
4. 请查看页面 http://localhost:8081/ 并分析。  
    4.1 查看页面对接是否正确，还缺少什么功能？
    4.2 页面UI可以如何改善？
    4.3 是否缺少某些模块？
    4.4 是否缺少某些后端实现？
    4.5 是否缺少某些字段？
    4.6 尽可能每个路由都浏览，查看是否有上述问题？


----------------------------------------------------------------------------------

通过pnpm dev:web 可以在本地启动web服务，通过cd server && pnpm dev 可以启动后端API服务。你可以通过工具 playwright 查看页面。
以下需求特别针对前端页面的。
请查看页面 http://localhost:8081/ 并分析。

1. 访问每个路由页面并截图分析。  
2. 查看每个页面实现是否正确，合理，是否缺少某些字段？
3. 页面布局是否合理，如何改善？
4. 是否缺少某些模块？是否缺少某些后端实现？
5. 尽可能每个路由都浏览并截图，查看是否有上述问题。


----------------------------------------------------------------------------------




----------------------------------------------------------------------------------



----------------------------------------------------------------------------------




----------------------------------------------------------------------------------



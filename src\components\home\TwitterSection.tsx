import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../common/ThemeProvider';
import { SPACING, FONT_SIZES, BORDER_RADIUS, FONT_WEIGHTS } from '../../constants';
import TouchableScale from '../ui/TouchableScale';

interface TwitterPost {
  id: string;
  author: string;
  handle: string;
  content: string;
  timestamp: string;
  likes: number;
  retweets: number;
  verified: boolean;
}

interface TwitterSectionProps {
  posts: TwitterPost[];
  onPostPress: (postId: string) => void;
  onSeeAllPress: () => void;
}

const TwitterSection: React.FC<TwitterSectionProps> = ({
  posts,
  onPostPress,
  onSeeAllPress,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  const renderTwitterPost = (post: TwitterPost) => (
    <TouchableScale
      key={post.id}
      style={styles.twitterCard}
      onPress={() => onPostPress(post.id)}
    >
      <View style={styles.twitterHeader}>
        <View style={styles.authorInfo}>
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>
              {post.author.charAt(0).toUpperCase()}
            </Text>
          </View>
          <View style={styles.authorDetails}>
            <View style={styles.authorName}>
              <Text style={styles.authorNameText}>{post.author}</Text>
              {post.verified && (
                <Ionicons name="checkmark-circle" size={14} color={colors.primary} />
              )}
            </View>
            <Text style={styles.authorHandle}>@{post.handle}</Text>
          </View>
        </View>
        <Text style={styles.timestamp}>{post.timestamp}</Text>
      </View>

      <Text style={styles.twitterContent} numberOfLines={3} ellipsizeMode="tail">
        {post.content}
      </Text>

      <View style={styles.twitterActions}>
        <View style={styles.actionItem}>
          <Ionicons name="heart-outline" size={16} color={colors.textSecondary} />
          <Text style={styles.actionText}>{post.likes}</Text>
        </View>
        <View style={styles.actionItem}>
          <Ionicons name="repeat-outline" size={16} color={colors.textSecondary} />
          <Text style={styles.actionText}>{post.retweets}</Text>
        </View>
        <View style={styles.actionItem}>
          <Ionicons name="share-outline" size={16} color={colors.textSecondary} />
        </View>
      </View>
    </TouchableScale>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Ionicons name="logo-twitter" size={20} color="#1DA1F2" />
          <Text style={styles.sectionTitle}>推特观点</Text>
        </View>
        <TouchableOpacity onPress={onSeeAllPress} style={styles.seeAllButton}>
          <Text style={styles.seeAllText}>查看全部</Text>
          <Ionicons name="chevron-forward" size={16} color={colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        decelerationRate="fast"
        snapToInterval={316} // 卡片宽度(300) + 右边距(16)
        snapToAlignment="center"
        pagingEnabled={false}
        scrollEventThrottle={16}
        bounces={false}
        bouncesZoom={false}
      >
        {posts.map(renderTwitterPost)}
      </ScrollView>
    </View>
  );
};

// 模拟推特数据
export const mockTwitterPosts: TwitterPost[] = [
  {
    id: '1',
    author: 'Vitalik Buterin',
    handle: 'VitalikButerin',
    content: 'Ethereum的未来发展方向将更加注重可扩展性和可持续性。Layer 2解决方案的成熟将为整个生态系统带来革命性的变化。我们正在见证区块链技术的真正潜力被释放。随着更多的开发者和用户加入这个生态系统，我们将看到前所未有的创新和应用场景。这不仅仅是技术的进步，更是整个数字经济的重大变革。',
    timestamp: '2小时前',
    likes: 1234,
    retweets: 567,
    verified: true,
  },
  {
    id: '2',
    author: 'CZ',
    handle: 'cz_binance',
    content: 'DeFi的发展速度令人惊叹。我们看到越来越多的传统金融机构开始拥抱去中心化金融。这是金融行业的一个重要转折点。去中心化金融正在重新定义传统银行业务，从借贷到交易，从保险到资产管理，每一个领域都在经历深刻的变革。这种变革不仅提高了效率，还降低了成本，为全球用户提供了更好的金融服务。',
    timestamp: '4小时前',
    likes: 892,
    retweets: 234,
    verified: true,
  },
  {
    id: '3',
    author: 'Michael Saylor',
    handle: 'michael_saylor',
    content: 'Bitcoin不仅仅是数字黄金，它是人类历史上最重要的货币创新。机构投资者的持续涌入证明了这一点。',
    timestamp: '6小时前',
    likes: 2156,
    retweets: 789,
    verified: true,
  },
  {
    id: '4',
    author: 'Cathie Wood',
    handle: 'CathieDWood',
    content: 'NFT和Web3技术正在重新定义数字所有权的概念。我们正处于一个全新数字经济时代的开端。',
    timestamp: '8小时前',
    likes: 1567,
    retweets: 445,
    verified: true,
  },
];

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      marginVertical: SPACING.lg,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: SPACING.md,
      marginBottom: SPACING.md,
    },
    headerLeft: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    sectionTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.text,
      marginLeft: SPACING.sm,
    },
    seeAllButton: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    seeAllText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
      marginRight: SPACING.xs,
    },
    scrollContent: {
      paddingLeft: SPACING.md,
      paddingRight: SPACING.md,
      alignItems: 'center', // 垂直居中对齐
    },
    twitterCard: {
      width: 300,
      height: 200, // 设置固定高度，确保所有卡片统一尺寸
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.lg,
      padding: SPACING.md,
      marginRight: SPACING.md,
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
      justifyContent: 'space-between', // 确保内容分布均匀
    },
    twitterHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: SPACING.md,
    },
    authorInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    avatar: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: SPACING.sm,
    },
    avatarText: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#FFFFFF',
    },
    authorDetails: {
      flex: 1,
    },
    authorName: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 2,
    },
    authorNameText: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
      marginRight: SPACING.xs,
    },
    authorHandle: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    timestamp: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    twitterContent: {
      fontSize: FONT_SIZES.base,
      color: colors.text,
      lineHeight: 18, // 减少行高以确保3行限制正常工作
      marginBottom: SPACING.md,
      // 移除flex和minHeight，让numberOfLines正常工作
    },
    twitterActions: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingTop: SPACING.sm,
      borderTopWidth: 1,
      borderTopColor: colors.borderLight,
    },
    actionItem: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    actionText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginLeft: SPACING.xs,
      fontWeight: FONT_WEIGHTS.medium,
    },
  });

export default TwitterSection;

import { Router } from 'express';
import { fetcherController } from './controller';
import { validateRequest } from '@/middlewares/validation';
import { FetcherQuerySchema, SearchQuerySchema, PricesQuerySchema } from './types';
import rateLimit from 'express-rate-limit';

const router: Router = Router();

// Rate limiting for different endpoints
const defaultLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const searchLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // limit search requests
  message: {
    error: 'Too many search requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const priceLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 60, // limit price requests to 60 per minute
  message: {
    error: 'Too many price requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// ========================================
// Main API Routes
// ========================================

/**
 * GET /api/news
 * Get aggregated news from multiple sources
 * 
 * Query parameters:
 * - page: number (default: 1)
 * - limit: number (default: 20, max: 100)
 * - category: string (optional)
 * - sources: string (optional, comma-separated source IDs)
 * - sortBy: 'publishedAt' | 'relevance' | 'popularity' (default: 'publishedAt')
 * - sortOrder: 'asc' | 'desc' (default: 'desc')
 * - includeContent: boolean (default: false)
 */
router.get('/news', 
  defaultLimiter,
  validateRequest({ query: FetcherQuerySchema }),
  fetcherController.getNews.bind(fetcherController)
);

/**
 * GET /api/feed
 * Get aggregated feed from multiple sources
 * Same query parameters as /api/news
 */
router.get('/feed',
  defaultLimiter,
  validateRequest({ query: FetcherQuerySchema }),
  fetcherController.getFeed.bind(fetcherController)
);

/**
 * GET /api/flash
 * Get flash news from multiple sources
 * Same query parameters as /api/news
 */
router.get('/flash',
  defaultLimiter,
  validateRequest({ query: FetcherQuerySchema }),
  fetcherController.getFlash.bind(fetcherController)
);

/**
 * GET /api/search
 * Search news across multiple sources
 * 
 * Query parameters:
 * - q: string (required, search query)
 * - page: number (default: 1)
 * - limit: number (default: 20, max: 50)
 * - sources: string (optional, comma-separated source IDs)
 * - timeRange: '1h' | '24h' | '7d' | '30d' | 'all' (default: 'all')
 */
router.get('/search',
  searchLimiter,
  validateRequest({ query: SearchQuerySchema }),
  fetcherController.searchNews.bind(fetcherController)
);

/**
 * GET /api/hot-tweets
 * Get hot tweets (placeholder implementation)
 * Same query parameters as /api/news
 */
router.get('/hot-tweets',
  defaultLimiter,
  validateRequest({ query: FetcherQuerySchema }),
  fetcherController.getHotTweets.bind(fetcherController)
);

/**
 * GET /api/prices
 * Get cryptocurrency price data
 * 
 * Query parameters:
 * - symbols: string (optional, comma-separated symbols)
 * - vs_currency: string (default: 'usd')
 * - limit: number (default: 100, max: 250)
 * - sources: string (optional, comma-separated source IDs)
 */
router.get('/prices',
  priceLimiter,
  validateRequest({ query: PricesQuerySchema }),
  fetcherController.getPrices.bind(fetcherController)
);

// ========================================
// Utility Routes
// ========================================

/**
 * GET /api/sources
 * Get available sources
 */
router.get('/sources',
  defaultLimiter,
  fetcherController.getSources.bind(fetcherController)
);

/**
 * GET /api/stats
 * Get fetcher statistics
 */
router.get('/stats',
  defaultLimiter,
  fetcherController.getStats.bind(fetcherController)
);

/**
 * POST /api/cache/clear
 * Clear fetcher cache
 */
router.post('/cache/clear',
  rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // limit cache clear requests
    message: {
      error: 'Too many cache clear requests from this IP, please try again later.',
    },
  }),
  fetcherController.clearCache.bind(fetcherController)
);

/**
 * GET /api/health
 * Health check for fetcher
 */
router.get('/health',
  fetcherController.healthCheck.bind(fetcherController)
);

// ========================================
// API Documentation Route
// ========================================

/**
 * GET /api/
 * API documentation
 */
router.get('/', (_, res) => {
  res.json({
    name: 'ChainMix Fetcher API',
    version: '1.0.0',
    description: 'Aggregated blockchain news and information API',
    endpoints: {
      news: {
        url: 'GET /api/news',
        description: 'Get aggregated news from multiple sources',
        parameters: {
          page: 'number (default: 1)',
          limit: 'number (default: 20, max: 100)',
          category: 'string (optional)',
          sources: 'string (optional, comma-separated source IDs)',
          sortBy: 'publishedAt | relevance | popularity (default: publishedAt)',
          sortOrder: 'asc | desc (default: desc)',
          includeContent: 'boolean (default: false)',
        },
      },
      feed: {
        url: 'GET /api/feed',
        description: 'Get aggregated feed from multiple sources',
        parameters: 'Same as /api/news',
      },
      flash: {
        url: 'GET /api/flash',
        description: 'Get flash news from multiple sources',
        parameters: 'Same as /api/news',
      },
      search: {
        url: 'GET /api/search',
        description: 'Search news across multiple sources',
        parameters: {
          q: 'string (required, search query)',
          page: 'number (default: 1)',
          limit: 'number (default: 20, max: 50)',
          sources: 'string (optional, comma-separated source IDs)',
          timeRange: '1h | 24h | 7d | 30d | all (default: all)',
        },
      },
      'hot-tweets': {
        url: 'GET /api/hot-tweets',
        description: 'Get hot tweets (placeholder implementation)',
        parameters: 'Same as /api/news',
      },
      prices: {
        url: 'GET /api/prices',
        description: 'Get cryptocurrency price data',
        parameters: {
          symbols: 'string (optional, comma-separated symbols)',
          vs_currency: 'string (default: usd)',
          limit: 'number (default: 100, max: 250)',
          sources: 'string (optional, comma-separated source IDs)',
        },
      },
      sources: {
        url: 'GET /api/sources',
        description: 'Get available sources',
      },
      stats: {
        url: 'GET /api/stats',
        description: 'Get fetcher statistics',
      },
      'cache/clear': {
        url: 'POST /api/cache/clear',
        description: 'Clear fetcher cache',
      },
      health: {
        url: 'GET /api/health',
        description: 'Health check for fetcher',
      },
    },
    examples: {
      'Get latest news': 'GET /api/news?page=1&limit=10',
      'Get flash news': 'GET /api/flash?page=1&limit=20',
      'Search news': 'GET /api/search?q=bitcoin&timeRange=24h',
      'Get news from specific sources': 'GET /api/news?sources=chaincatcher,techflow',
      'Get feed': 'GET /api/feed?page=1&limit=30',
    },
    rateLimits: {
      default: '100 requests per 15 minutes',
      search: '50 requests per 15 minutes',
      prices: '60 requests per minute',
      cacheOperations: '5 requests per 15 minutes',
    },
    responseHeaders: {
      'X-Sources': 'Comma-separated list of sources used',
      'X-Cached': 'Whether the response was cached',
      'X-Request-Time': 'Request timestamp',
      'X-Source-Errors': 'Number of source errors (if any)',
      'X-Search-Query': 'Search query (for search endpoints)',
    },
  });
});

export default router;
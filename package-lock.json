{"name": "chainmix", "version": "1.0.0", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "chainmix", "version": "1.0.0", "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@tamagui/animations-react-native": "^1.129.4", "@tamagui/config": "^1.129.4", "@tamagui/core": "^1.129.4", "@tamagui/font-inter": "^1.129.4", "@tamagui/theme-base": "^1.129.4", "axios": "^1.10.0", "expo": "~53.0.13", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-web": "^0.20.0", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eas-cli": "^16.13.2", "typescript": "~5.8.3"}}, "node_modules/@0no-co/graphql.web": {"version": "1.1.2", "license": "MIT", "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0"}, "peerDependenciesMeta": {"graphql": {"optional": true}}}, "node_modules/@ampproject/remapping": {"version": "2.3.0", "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/code-frame": {"version": "7.10.4", "license": "MIT", "dependencies": {"@babel/highlight": "^7.10.4"}}, "node_modules/@babel/compat-data": {"version": "7.27.7", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.27.7", "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.5", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.27.3", "@babel/helpers": "^7.27.6", "@babel/parser": "^7.27.7", "@babel/template": "^7.27.2", "@babel/traverse": "^7.27.7", "@babel/types": "^7.27.7", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/core/node_modules/@babel/code-frame": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/generator": {"version": "7.27.5", "license": "MIT", "dependencies": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-annotate-as-pure": {"version": "7.27.3", "license": "MIT", "dependencies": {"@babel/types": "^7.27.3"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.27.2", "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets/node_modules/lru-cache": {"version": "5.1.1", "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/@babel/helper-compilation-targets/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-compilation-targets/node_modules/yallist": {"version": "3.1.1", "license": "ISC"}, "node_modules/@babel/helper-create-class-features-plugin": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/traverse": "^7.27.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-create-class-features-plugin/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-create-regexp-features-plugin": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "regexpu-core": "^6.2.0", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-create-regexp-features-plugin/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-define-polyfill-provider": {"version": "0.6.5", "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "debug": "^4.4.1", "lodash.debounce": "^4.0.8", "resolve": "^1.22.10"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/@babel/helper-member-expression-to-functions": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.27.3", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-optimise-call-expression": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-remap-async-to-generator": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-wrap-function": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-replace-supers": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-skip-transparent-expression-wrappers": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-wrap-function": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/template": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.27.6", "license": "MIT", "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.27.6"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.25.9", "chalk": "^2.4.2", "js-tokens": "^4.0.0", "picocolors": "^1.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight/node_modules/ansi-styles": {"version": "3.2.1", "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/chalk": {"version": "2.4.2", "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/color-convert": {"version": "1.9.3", "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/@babel/highlight/node_modules/color-name": {"version": "1.1.3", "license": "MIT"}, "node_modules/@babel/highlight/node_modules/escape-string-regexp": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/@babel/highlight/node_modules/has-flag": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/supports-color": {"version": "5.5.0", "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/parser": {"version": "7.27.7", "license": "MIT", "dependencies": {"@babel/types": "^7.27.7"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-proposal-decorators": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-syntax-decorators": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-export-default-from": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-async-generators": {"version": "7.8.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-bigint": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-class-properties": {"version": "7.12.13", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-class-static-block": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-decorators": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-dynamic-import": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-export-default-from": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-flow": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-attributes": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-meta": {"version": "7.10.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-json-strings": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-jsx": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-logical-assignment-operators": {"version": "7.10.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-private-property-in-object": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-top-level-await": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-typescript": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-arrow-functions": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-async-generator-functions": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-remap-async-to-generator": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-async-to-generator": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-remap-async-to-generator": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-block-scoping": {"version": "7.27.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-class-properties": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-classes": {"version": "7.27.7", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.3", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/traverse": "^7.27.7", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-computed-properties": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/template": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-destructuring": {"version": "7.27.7", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.27.7"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-export-namespace-from": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-flow-strip-types": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-syntax-flow": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-for-of": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-function-name": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-literals": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-logical-assignment-operators": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-commonjs": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-named-capturing-groups-regex": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-nullish-coalescing-operator": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-numeric-separator": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-object-rest-spread": {"version": "7.27.7", "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.27.7", "@babel/plugin-transform-parameters": "^7.27.7", "@babel/traverse": "^7.27.7"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-optional-catch-binding": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-optional-chaining": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-parameters": {"version": "7.27.7", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-private-methods": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-private-property-in-object": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-display-name": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-syntax-jsx": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx-development": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/plugin-transform-react-jsx": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx-self": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx-source": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-pure-annotations": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-regenerator": {"version": "7.27.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-runtime": {"version": "7.27.4", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "babel-plugin-polyfill-corejs2": "^0.4.10", "babel-plugin-polyfill-corejs3": "^0.11.0", "babel-plugin-polyfill-regenerator": "^0.6.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-runtime/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/plugin-transform-shorthand-properties": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-spread": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-sticky-regex": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-template-literals": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-typescript": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/plugin-syntax-typescript": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-regex": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/preset-react": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-option": "^7.27.1", "@babel/plugin-transform-react-display-name": "^7.27.1", "@babel/plugin-transform-react-jsx": "^7.27.1", "@babel/plugin-transform-react-jsx-development": "^7.27.1", "@babel/plugin-transform-react-pure-annotations": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/preset-typescript": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-option": "^7.27.1", "@babel/plugin-syntax-jsx": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-typescript": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/runtime": {"version": "7.27.6", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.27.2", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template/node_modules/@babel/code-frame": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.27.7", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.5", "@babel/parser": "^7.27.7", "@babel/template": "^7.27.2", "@babel/types": "^7.27.7", "debug": "^4.3.1", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse--for-generate-function-map": {"name": "@babel/traverse", "version": "7.27.7", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.5", "@babel/parser": "^7.27.7", "@babel/template": "^7.27.2", "@babel/types": "^7.27.7", "debug": "^4.3.1", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse--for-generate-function-map/node_modules/@babel/code-frame": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse/node_modules/@babel/code-frame": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.27.7", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@cspotcode/source-map-support": {"version": "0.8.1", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "0.3.9"}, "engines": {"node": ">=12"}}, "node_modules/@cspotcode/source-map-support/node_modules/@jridgewell/trace-mapping": {"version": "0.3.9", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.0.3", "@jridgewell/sourcemap-codec": "^1.4.10"}}, "node_modules/@egjs/hammerjs": {"version": "2.0.17", "license": "MIT", "dependencies": {"@types/hammerjs": "^2.0.36"}, "engines": {"node": ">=0.8.0"}}, "node_modules/@emotion/is-prop-valid": {"version": "0.8.8", "license": "MIT", "optional": true, "dependencies": {"@emotion/memoize": "0.7.4"}}, "node_modules/@emotion/memoize": {"version": "0.7.4", "license": "MIT", "optional": true}, "node_modules/@expo/apple-utils": {"version": "2.1.12", "dev": true, "license": "MIT", "bin": {"apple-utils": "bin.js"}}, "node_modules/@expo/bunyan": {"version": "4.0.1", "dev": true, "engines": ["node >=0.10.0"], "license": "MIT", "dependencies": {"uuid": "^8.0.0"}}, "node_modules/@expo/cli": {"version": "0.24.15", "license": "MIT", "dependencies": {"@0no-co/graphql.web": "^1.0.8", "@babel/runtime": "^7.20.0", "@expo/code-signing-certificates": "^0.0.5", "@expo/config": "~11.0.10", "@expo/config-plugins": "~10.0.3", "@expo/devcert": "^1.1.2", "@expo/env": "~1.0.5", "@expo/image-utils": "^0.7.4", "@expo/json-file": "^9.1.4", "@expo/metro-config": "~0.20.15", "@expo/osascript": "^2.2.4", "@expo/package-manager": "^1.8.4", "@expo/plist": "^0.3.4", "@expo/prebuild-config": "^9.0.7", "@expo/spawn-async": "^1.7.2", "@expo/ws-tunnel": "^1.0.1", "@expo/xcpretty": "^4.3.0", "@react-native/dev-middleware": "0.79.4", "@urql/core": "^5.0.6", "@urql/exchange-retry": "^1.3.0", "accepts": "^1.3.8", "arg": "^5.0.2", "better-opn": "~3.0.2", "bplist-creator": "0.1.0", "bplist-parser": "^0.3.1", "chalk": "^4.0.0", "ci-info": "^3.3.0", "compression": "^1.7.4", "connect": "^3.7.0", "debug": "^4.3.4", "env-editor": "^0.4.1", "freeport-async": "^2.0.0", "getenv": "^2.0.0", "glob": "^10.4.2", "lan-network": "^0.1.6", "minimatch": "^9.0.0", "node-forge": "^1.3.1", "npm-package-arg": "^11.0.0", "ora": "^3.4.0", "picomatch": "^3.0.1", "pretty-bytes": "^5.6.0", "pretty-format": "^29.7.0", "progress": "^2.0.3", "prompts": "^2.3.2", "qrcode-terminal": "0.11.0", "require-from-string": "^2.0.2", "requireg": "^0.2.2", "resolve": "^1.22.2", "resolve-from": "^5.0.0", "resolve.exports": "^2.0.3", "semver": "^7.6.0", "send": "^0.19.0", "slugify": "^1.3.4", "source-map-support": "~0.5.21", "stacktrace-parser": "^0.1.10", "structured-headers": "^0.4.1", "tar": "^7.4.3", "terminal-link": "^2.1.1", "undici": "^6.18.2", "wrap-ansi": "^7.0.0", "ws": "^8.12.1"}, "bin": {"expo-internal": "build/bin/cli"}}, "node_modules/@expo/cli/node_modules/encodeurl": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/@expo/cli/node_modules/getenv": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@expo/cli/node_modules/minimatch": {"version": "9.0.5", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@expo/cli/node_modules/picomatch": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/@expo/cli/node_modules/send": {"version": "0.19.1", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/@expo/cli/node_modules/send/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/@expo/cli/node_modules/send/node_modules/debug/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/@expo/cli/node_modules/ws": {"version": "8.18.2", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/@expo/code-signing-certificates": {"version": "0.0.5", "license": "MIT", "dependencies": {"node-forge": "^1.2.1", "nullthrows": "^1.1.1"}}, "node_modules/@expo/config": {"version": "11.0.10", "license": "MIT", "dependencies": {"@babel/code-frame": "~7.10.4", "@expo/config-plugins": "~10.0.2", "@expo/config-types": "^53.0.4", "@expo/json-file": "^9.1.4", "deepmerge": "^4.3.1", "getenv": "^1.0.0", "glob": "^10.4.2", "require-from-string": "^2.0.2", "resolve-from": "^5.0.0", "resolve-workspace-root": "^2.0.0", "semver": "^7.6.0", "slugify": "^1.3.4", "sucrase": "3.35.0"}}, "node_modules/@expo/config-plugins": {"version": "10.0.3", "license": "MIT", "dependencies": {"@expo/config-types": "^53.0.4", "@expo/json-file": "~9.1.4", "@expo/plist": "^0.3.4", "@expo/sdk-runtime-versions": "^1.0.0", "chalk": "^4.1.2", "debug": "^4.3.5", "getenv": "^2.0.0", "glob": "^10.4.2", "resolve-from": "^5.0.0", "semver": "^7.5.4", "slash": "^3.0.0", "slugify": "^1.6.6", "xcode": "^3.0.1", "xml2js": "0.6.0"}}, "node_modules/@expo/config-plugins/node_modules/getenv": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@expo/config-types": {"version": "53.0.4", "license": "MIT"}, "node_modules/@expo/devcert": {"version": "1.2.0", "license": "MIT", "dependencies": {"@expo/sudo-prompt": "^9.3.1", "debug": "^3.1.0", "glob": "^10.4.2"}}, "node_modules/@expo/devcert/node_modules/debug": {"version": "3.2.7", "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/@expo/eas-build-job": {"version": "1.0.173", "dev": true, "license": "MIT", "dependencies": {"@expo/logger": "1.0.117", "joi": "^17.13.1", "semver": "^7.6.2", "zod": "^3.23.8"}}, "node_modules/@expo/eas-json": {"version": "16.13.2", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "7.23.5", "@expo/eas-build-job": "1.0.173", "chalk": "4.1.2", "env-string": "1.0.1", "fs-extra": "11.2.0", "golden-fleece": "1.0.9", "joi": "17.11.0", "log-symbols": "4.1.0", "semver": "7.5.2", "terminal-link": "2.1.1", "tslib": "2.4.1"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@expo/eas-json/node_modules/@babel/code-frame": {"version": "7.23.5", "dev": true, "license": "MIT", "dependencies": {"@babel/highlight": "^7.23.4", "chalk": "^2.4.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@expo/eas-json/node_modules/@babel/code-frame/node_modules/chalk": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/@expo/eas-json/node_modules/ansi-styles": {"version": "3.2.1", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/@expo/eas-json/node_modules/color-convert": {"version": "1.9.3", "dev": true, "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/@expo/eas-json/node_modules/color-name": {"version": "1.1.3", "dev": true, "license": "MIT"}, "node_modules/@expo/eas-json/node_modules/escape-string-regexp": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/@expo/eas-json/node_modules/has-flag": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@expo/eas-json/node_modules/joi": {"version": "17.11.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}}, "node_modules/@expo/eas-json/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@expo/eas-json/node_modules/semver": {"version": "7.5.2", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@expo/eas-json/node_modules/supports-color": {"version": "5.5.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/@expo/eas-json/node_modules/tslib": {"version": "2.4.1", "dev": true, "license": "0BSD"}, "node_modules/@expo/env": {"version": "1.0.5", "license": "MIT", "dependencies": {"chalk": "^4.0.0", "debug": "^4.3.4", "dotenv": "~16.4.5", "dotenv-expand": "~11.0.6", "getenv": "^1.0.0"}}, "node_modules/@expo/fingerprint": {"version": "0.13.1", "license": "MIT", "dependencies": {"@expo/spawn-async": "^1.7.2", "arg": "^5.0.2", "chalk": "^4.1.2", "debug": "^4.3.4", "find-up": "^5.0.0", "getenv": "^2.0.0", "glob": "^10.4.2", "ignore": "^5.3.1", "minimatch": "^9.0.0", "p-limit": "^3.1.0", "resolve-from": "^5.0.0", "semver": "^7.6.0"}, "bin": {"fingerprint": "bin/cli.js"}}, "node_modules/@expo/fingerprint/node_modules/getenv": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@expo/fingerprint/node_modules/ignore": {"version": "5.3.2", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/@expo/fingerprint/node_modules/minimatch": {"version": "9.0.5", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@expo/image-utils": {"version": "0.7.4", "license": "MIT", "dependencies": {"@expo/spawn-async": "^1.7.2", "chalk": "^4.0.0", "getenv": "^1.0.0", "jimp-compact": "0.16.1", "parse-png": "^2.1.0", "resolve-from": "^5.0.0", "semver": "^7.6.0", "temp-dir": "~2.0.0", "unique-string": "~2.0.0"}}, "node_modules/@expo/json-file": {"version": "9.1.4", "license": "MIT", "dependencies": {"@babel/code-frame": "~7.10.4", "json5": "^2.2.3"}}, "node_modules/@expo/logger": {"version": "1.0.117", "dev": true, "license": "BUSL-1.1", "dependencies": {"@types/bunyan": "^1.8.11", "bunyan": "^1.8.15"}}, "node_modules/@expo/metro-config": {"version": "0.20.15", "license": "MIT", "dependencies": {"@babel/core": "^7.20.0", "@babel/generator": "^7.20.5", "@babel/parser": "^7.20.0", "@babel/types": "^7.20.0", "@expo/config": "~11.0.10", "@expo/env": "~1.0.5", "@expo/json-file": "~9.1.4", "@expo/spawn-async": "^1.7.2", "chalk": "^4.1.0", "debug": "^4.3.2", "dotenv": "~16.4.5", "dotenv-expand": "~11.0.6", "getenv": "^2.0.0", "glob": "^10.4.2", "jsc-safe-url": "^0.2.4", "lightningcss": "~1.27.0", "minimatch": "^9.0.0", "postcss": "~8.4.32", "resolve-from": "^5.0.0"}}, "node_modules/@expo/metro-config/node_modules/getenv": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@expo/metro-config/node_modules/minimatch": {"version": "9.0.5", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@expo/metro-runtime": {"version": "5.0.4", "license": "MIT", "peerDependencies": {"react-native": "*"}}, "node_modules/@expo/multipart-body-parser": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"multipasta": "^0.2.5"}}, "node_modules/@expo/osascript": {"version": "2.2.4", "license": "MIT", "dependencies": {"@expo/spawn-async": "^1.7.2", "exec-async": "^2.2.0"}, "engines": {"node": ">=12"}}, "node_modules/@expo/package-manager": {"version": "1.8.4", "license": "MIT", "dependencies": {"@expo/json-file": "^9.1.4", "@expo/spawn-async": "^1.7.2", "chalk": "^4.0.0", "npm-package-arg": "^11.0.0", "ora": "^3.4.0", "resolve-workspace-root": "^2.0.0"}}, "node_modules/@expo/pkcs12": {"version": "0.1.3", "dev": true, "license": "MIT", "dependencies": {"node-forge": "^1.2.1"}}, "node_modules/@expo/plist": {"version": "0.3.4", "license": "MIT", "dependencies": {"@xmldom/xmldom": "^0.8.8", "base64-js": "^1.2.3", "xmlbuilder": "^15.1.1"}}, "node_modules/@expo/plugin-help": {"version": "5.1.23", "dev": true, "license": "MIT", "dependencies": {"@oclif/core": "^2.11.1"}, "engines": {"node": ">=12.0.0"}}, "node_modules/@expo/plugin-warn-if-update-available": {"version": "2.5.1", "dev": true, "license": "MIT", "dependencies": {"@oclif/core": "^2.11.1", "chalk": "^4.1.0", "debug": "^4.3.4", "ejs": "^3.1.7", "fs-extra": "^10.1.0", "http-call": "^5.2.2", "semver": "^7.3.7", "tslib": "^2.4.0"}, "engines": {"node": ">=12.0.0"}}, "node_modules/@expo/plugin-warn-if-update-available/node_modules/fs-extra": {"version": "10.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@expo/plugin-warn-if-update-available/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@expo/plugin-warn-if-update-available/node_modules/semver": {"version": "7.5.4", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@expo/plugin-warn-if-update-available/node_modules/tslib": {"version": "2.6.2", "dev": true, "license": "0BSD"}, "node_modules/@expo/prebuild-config": {"version": "9.0.8", "license": "MIT", "dependencies": {"@expo/config": "~11.0.10", "@expo/config-plugins": "~10.0.3", "@expo/config-types": "^53.0.4", "@expo/image-utils": "^0.7.4", "@expo/json-file": "^9.1.4", "@react-native/normalize-colors": "0.79.4", "debug": "^4.3.1", "resolve-from": "^5.0.0", "semver": "^7.6.0", "xml2js": "0.6.0"}}, "node_modules/@expo/results": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/@expo/rudder-sdk-node": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"@expo/bunyan": "^4.0.0", "@segment/loosely-validate-event": "^2.0.0", "fetch-retry": "^4.1.1", "md5": "^2.2.1", "node-fetch": "^2.6.1", "remove-trailing-slash": "^0.1.0", "uuid": "^8.3.2"}, "engines": {"node": ">=12"}}, "node_modules/@expo/sdk-runtime-versions": {"version": "1.0.0", "license": "MIT"}, "node_modules/@expo/spawn-async": {"version": "1.7.2", "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3"}, "engines": {"node": ">=12"}}, "node_modules/@expo/steps": {"version": "1.0.173", "dev": true, "license": "BUSL-1.1", "dependencies": {"@expo/eas-build-job": "1.0.173", "@expo/logger": "1.0.117", "@expo/spawn-async": "^1.7.2", "arg": "^5.0.2", "fs-extra": "^11.2.0", "joi": "^17.13.1", "jsep": "^1.3.8", "lodash.clonedeep": "^4.5.0", "lodash.get": "^4.4.2", "this-file": "^2.0.3", "uuid": "^9.0.1", "yaml": "^2.4.3"}, "engines": {"node": ">=18"}}, "node_modules/@expo/steps/node_modules/uuid": {"version": "9.0.1", "dev": true, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/@expo/sudo-prompt": {"version": "9.3.2", "license": "MIT"}, "node_modules/@expo/timeago.js": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/@expo/vector-icons": {"version": "14.1.0", "license": "MIT", "peerDependencies": {"expo-font": "*", "react": "*", "react-native": "*"}}, "node_modules/@expo/ws-tunnel": {"version": "1.0.6", "license": "MIT"}, "node_modules/@expo/xcpretty": {"version": "4.3.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/code-frame": "7.10.4", "chalk": "^4.1.0", "find-up": "^5.0.0", "js-yaml": "^4.1.0"}, "bin": {"excpretty": "build/cli.js"}}, "node_modules/@expo/xcpretty/node_modules/argparse": {"version": "2.0.1", "license": "Python-2.0"}, "node_modules/@expo/xcpretty/node_modules/js-yaml": {"version": "4.1.0", "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/@hapi/hoek": {"version": "9.3.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@hapi/topo": {"version": "5.1.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.0.0"}}, "node_modules/@isaacs/cliui": {"version": "8.0.2", "license": "ISC", "dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@isaacs/cliui/node_modules/ansi-regex": {"version": "6.1.0", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/ansi-styles": {"version": "6.2.1", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/emoji-regex": {"version": "9.2.2", "license": "MIT"}, "node_modules/@isaacs/cliui/node_modules/string-width": {"version": "5.1.2", "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@isaacs/cliui/node_modules/strip-ansi": {"version": "7.1.0", "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/wrap-ansi": {"version": "8.1.0", "license": "MIT", "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/@isaacs/fs-minipass": {"version": "4.0.1", "license": "ISC", "dependencies": {"minipass": "^7.0.4"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@isaacs/ttlcache": {"version": "1.4.1", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/@istanbuljs/load-nyc-config": {"version": "1.1.0", "license": "ISC", "dependencies": {"camelcase": "^5.3.1", "find-up": "^4.1.0", "get-package-type": "^0.1.0", "js-yaml": "^3.13.1", "resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@istanbuljs/load-nyc-config/node_modules/camelcase": {"version": "5.3.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@istanbuljs/load-nyc-config/node_modules/find-up": {"version": "4.1.0", "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@istanbuljs/schema": {"version": "0.1.3", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@jest/create-cache-key-function": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/types": "^29.6.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/environment": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/fake-timers": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "jest-mock": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/fake-timers": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@sinonjs/fake-timers": "^10.0.2", "@types/node": "*", "jest-message-util": "^29.7.0", "jest-mock": "^29.7.0", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/schemas": {"version": "29.6.3", "license": "MIT", "dependencies": {"@sinclair/typebox": "^0.27.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/transform": {"version": "29.7.0", "license": "MIT", "dependencies": {"@babel/core": "^7.11.6", "@jest/types": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.18", "babel-plugin-istanbul": "^6.1.1", "chalk": "^4.0.0", "convert-source-map": "^2.0.0", "fast-json-stable-stringify": "^2.1.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "micromatch": "^4.0.4", "pirates": "^4.0.4", "slash": "^3.0.0", "write-file-atomic": "^4.0.2"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/transform/node_modules/write-file-atomic": {"version": "4.0.2", "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/@jest/types": {"version": "29.6.3", "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.8", "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/source-map": {"version": "0.3.6", "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@motionone/animation": {"version": "10.18.0", "license": "MIT", "dependencies": {"@motionone/easing": "^10.18.0", "@motionone/types": "^10.17.1", "@motionone/utils": "^10.18.0", "tslib": "^2.3.1"}}, "node_modules/@motionone/dom": {"version": "10.12.0", "license": "MIT", "dependencies": {"@motionone/animation": "^10.12.0", "@motionone/generators": "^10.12.0", "@motionone/types": "^10.12.0", "@motionone/utils": "^10.12.0", "hey-listen": "^1.0.8", "tslib": "^2.3.1"}}, "node_modules/@motionone/easing": {"version": "10.18.0", "license": "MIT", "dependencies": {"@motionone/utils": "^10.18.0", "tslib": "^2.3.1"}}, "node_modules/@motionone/generators": {"version": "10.18.0", "license": "MIT", "dependencies": {"@motionone/types": "^10.17.1", "@motionone/utils": "^10.18.0", "tslib": "^2.3.1"}}, "node_modules/@motionone/types": {"version": "10.17.1", "license": "MIT"}, "node_modules/@motionone/utils": {"version": "10.18.0", "license": "MIT", "dependencies": {"@motionone/types": "^10.17.1", "hey-listen": "^1.0.8", "tslib": "^2.3.1"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@oclif/core": {"version": "2.16.0", "dev": true, "license": "MIT", "dependencies": {"@types/cli-progress": "^3.11.0", "ansi-escapes": "^4.3.2", "ansi-styles": "^4.3.0", "cardinal": "^2.1.1", "chalk": "^4.1.2", "clean-stack": "^3.0.1", "cli-progress": "^3.12.0", "debug": "^4.3.4", "ejs": "^3.1.8", "get-package-type": "^0.1.0", "globby": "^11.1.0", "hyperlinker": "^1.0.0", "indent-string": "^4.0.0", "is-wsl": "^2.2.0", "js-yaml": "^3.14.1", "natural-orderby": "^2.0.3", "object-treeify": "^1.1.33", "password-prompt": "^1.1.2", "slice-ansi": "^4.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "supports-color": "^8.1.1", "supports-hyperlinks": "^2.2.0", "ts-node": "^10.9.1", "tslib": "^2.5.0", "widest-line": "^3.1.0", "wordwrap": "^1.0.0", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@oclif/core/node_modules/tslib": {"version": "2.6.2", "dev": true, "license": "0BSD"}, "node_modules/@oclif/linewrap": {"version": "1.0.0", "dev": true, "license": "ISC"}, "node_modules/@oclif/plugin-autocomplete": {"version": "2.3.10", "dev": true, "license": "MIT", "dependencies": {"@oclif/core": "^2.15.0", "chalk": "^4.1.0", "debug": "^4.3.4"}, "engines": {"node": ">=12.0.0"}}, "node_modules/@oclif/screen": {"version": "3.0.8", "dev": true, "license": "MIT", "engines": {"node": ">=12.0.0"}}, "node_modules/@pkgjs/parseargs": {"version": "0.11.0", "license": "MIT", "optional": true, "engines": {"node": ">=14"}}, "node_modules/@react-native-async-storage/async-storage": {"version": "2.1.2", "license": "MIT", "dependencies": {"merge-options": "^3.0.4"}, "peerDependencies": {"react-native": "^0.0.0-0 || >=0.65 <1.0"}}, "node_modules/@react-native-community/netinfo": {"version": "11.4.1", "resolved": "https://registry.npmjs.org/@react-native-community/netinfo/-/netinfo-11.4.1.tgz", "integrity": "sha512-B0BYAkghz3Q2V09BF88RA601XursIEA111tnc2JOaN7axJWmNefmfjZqw/KdSxKZp7CZUuPpjBmz/WCR9uaHYg==", "peerDependencies": {"react-native": ">=0.59"}}, "node_modules/@react-native/assets-registry": {"version": "0.79.4", "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/@react-native/babel-plugin-codegen": {"version": "0.79.4", "license": "MIT", "dependencies": {"@babel/traverse": "^7.25.3", "@react-native/codegen": "0.79.4"}, "engines": {"node": ">=18"}}, "node_modules/@react-native/babel-preset": {"version": "0.79.4", "license": "MIT", "dependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-export-default-from": "^7.24.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-export-default-from": "^7.24.7", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-transform-arrow-functions": "^7.24.7", "@babel/plugin-transform-async-generator-functions": "^7.25.4", "@babel/plugin-transform-async-to-generator": "^7.24.7", "@babel/plugin-transform-block-scoping": "^7.25.0", "@babel/plugin-transform-class-properties": "^7.25.4", "@babel/plugin-transform-classes": "^7.25.4", "@babel/plugin-transform-computed-properties": "^7.24.7", "@babel/plugin-transform-destructuring": "^7.24.8", "@babel/plugin-transform-flow-strip-types": "^7.25.2", "@babel/plugin-transform-for-of": "^7.24.7", "@babel/plugin-transform-function-name": "^7.25.1", "@babel/plugin-transform-literals": "^7.25.2", "@babel/plugin-transform-logical-assignment-operators": "^7.24.7", "@babel/plugin-transform-modules-commonjs": "^7.24.8", "@babel/plugin-transform-named-capturing-groups-regex": "^7.24.7", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.7", "@babel/plugin-transform-numeric-separator": "^7.24.7", "@babel/plugin-transform-object-rest-spread": "^7.24.7", "@babel/plugin-transform-optional-catch-binding": "^7.24.7", "@babel/plugin-transform-optional-chaining": "^7.24.8", "@babel/plugin-transform-parameters": "^7.24.7", "@babel/plugin-transform-private-methods": "^7.24.7", "@babel/plugin-transform-private-property-in-object": "^7.24.7", "@babel/plugin-transform-react-display-name": "^7.24.7", "@babel/plugin-transform-react-jsx": "^7.25.2", "@babel/plugin-transform-react-jsx-self": "^7.24.7", "@babel/plugin-transform-react-jsx-source": "^7.24.7", "@babel/plugin-transform-regenerator": "^7.24.7", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/plugin-transform-shorthand-properties": "^7.24.7", "@babel/plugin-transform-spread": "^7.24.7", "@babel/plugin-transform-sticky-regex": "^7.24.7", "@babel/plugin-transform-typescript": "^7.25.2", "@babel/plugin-transform-unicode-regex": "^7.24.7", "@babel/template": "^7.25.0", "@react-native/babel-plugin-codegen": "0.79.4", "babel-plugin-syntax-hermes-parser": "0.25.1", "babel-plugin-transform-flow-enums": "^0.0.2", "react-refresh": "^0.14.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@babel/core": "*"}}, "node_modules/@react-native/codegen": {"version": "0.79.4", "license": "MIT", "dependencies": {"glob": "^7.1.1", "hermes-parser": "0.25.1", "invariant": "^2.2.4", "nullthrows": "^1.1.1", "yargs": "^17.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@babel/core": "*"}}, "node_modules/@react-native/codegen/node_modules/glob": {"version": "7.2.3", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@react-native/community-cli-plugin": {"version": "0.79.4", "license": "MIT", "dependencies": {"@react-native/dev-middleware": "0.79.4", "chalk": "^4.0.0", "debug": "^2.2.0", "invariant": "^2.2.4", "metro": "^0.82.0", "metro-config": "^0.82.0", "metro-core": "^0.82.0", "semver": "^7.1.3"}, "engines": {"node": ">=18"}, "peerDependencies": {"@react-native-community/cli": "*"}, "peerDependenciesMeta": {"@react-native-community/cli": {"optional": true}}}, "node_modules/@react-native/community-cli-plugin/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/@react-native/community-cli-plugin/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/@react-native/debugger-frontend": {"version": "0.79.4", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=18"}}, "node_modules/@react-native/dev-middleware": {"version": "0.79.4", "license": "MIT", "dependencies": {"@isaacs/ttlcache": "^1.4.1", "@react-native/debugger-frontend": "0.79.4", "chrome-launcher": "^0.15.2", "chromium-edge-launcher": "^0.2.0", "connect": "^3.6.5", "debug": "^2.2.0", "invariant": "^2.2.4", "nullthrows": "^1.1.1", "open": "^7.0.3", "serve-static": "^1.16.2", "ws": "^6.2.3"}, "engines": {"node": ">=18"}}, "node_modules/@react-native/dev-middleware/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/@react-native/dev-middleware/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/@react-native/gradle-plugin": {"version": "0.79.4", "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/@react-native/js-polyfills": {"version": "0.79.4", "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/@react-native/normalize-color": {"version": "2.1.0", "license": "MIT"}, "node_modules/@react-native/normalize-colors": {"version": "0.79.4", "license": "MIT"}, "node_modules/@react-native/virtualized-lists": {"version": "0.79.4", "license": "MIT", "dependencies": {"invariant": "^2.2.4", "nullthrows": "^1.1.1"}, "engines": {"node": ">=18"}, "peerDependencies": {"@types/react": "^19.0.0", "react": "*", "react-native": "*"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@react-navigation/bottom-tabs": {"version": "7.4.2", "license": "MIT", "dependencies": {"@react-navigation/elements": "^2.5.2", "color": "^4.2.3"}, "peerDependencies": {"@react-navigation/native": "^7.1.14", "react": ">= 18.2.0", "react-native": "*", "react-native-safe-area-context": ">= 4.0.0", "react-native-screens": ">= 4.0.0"}}, "node_modules/@react-navigation/core": {"version": "7.12.1", "license": "MIT", "dependencies": {"@react-navigation/routers": "^7.4.1", "escape-string-regexp": "^4.0.0", "nanoid": "^3.3.11", "query-string": "^7.1.3", "react-is": "^19.1.0", "use-latest-callback": "^0.2.4", "use-sync-external-store": "^1.5.0"}, "peerDependencies": {"react": ">= 18.2.0"}}, "node_modules/@react-navigation/core/node_modules/react-is": {"version": "19.1.0", "license": "MIT"}, "node_modules/@react-navigation/elements": {"version": "2.5.2", "license": "MIT", "dependencies": {"color": "^4.2.3", "use-latest-callback": "^0.2.4", "use-sync-external-store": "^1.5.0"}, "peerDependencies": {"@react-native-masked-view/masked-view": ">= 0.2.0", "@react-navigation/native": "^7.1.14", "react": ">= 18.2.0", "react-native": "*", "react-native-safe-area-context": ">= 4.0.0"}, "peerDependenciesMeta": {"@react-native-masked-view/masked-view": {"optional": true}}}, "node_modules/@react-navigation/native": {"version": "7.1.14", "license": "MIT", "dependencies": {"@react-navigation/core": "^7.12.1", "escape-string-regexp": "^4.0.0", "fast-deep-equal": "^3.1.3", "nanoid": "^3.3.11", "use-latest-callback": "^0.2.4"}, "peerDependencies": {"react": ">= 18.2.0", "react-native": "*"}}, "node_modules/@react-navigation/native-stack": {"version": "7.3.21", "license": "MIT", "dependencies": {"@react-navigation/elements": "^2.5.2", "warn-once": "^0.1.1"}, "peerDependencies": {"@react-navigation/native": "^7.1.14", "react": ">= 18.2.0", "react-native": "*", "react-native-safe-area-context": ">= 4.0.0", "react-native-screens": ">= 4.0.0"}}, "node_modules/@react-navigation/routers": {"version": "7.4.1", "license": "MIT", "dependencies": {"nanoid": "^3.3.11"}}, "node_modules/@segment/ajv-human-errors": {"version": "2.15.0", "dev": true, "license": "MIT", "peerDependencies": {"ajv": "^8.0.0"}}, "node_modules/@segment/loosely-validate-event": {"version": "2.0.0", "dev": true, "dependencies": {"component-type": "^1.2.1", "join-component": "^1.1.0"}}, "node_modules/@sideway/address": {"version": "4.1.5", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.0.0"}}, "node_modules/@sideway/formula": {"version": "3.0.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@sideway/pinpoint": {"version": "2.0.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@sinclair/typebox": {"version": "0.27.8", "license": "MIT"}, "node_modules/@sinonjs/commons": {"version": "3.0.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"type-detect": "4.0.8"}}, "node_modules/@sinonjs/fake-timers": {"version": "10.3.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sinonjs/commons": "^3.0.0"}}, "node_modules/@swc/core": {"version": "1.12.7", "dev": true, "hasInstallScript": true, "license": "Apache-2.0", "optional": true, "peer": true, "dependencies": {"@swc/counter": "^0.1.3", "@swc/types": "^0.1.23"}, "engines": {"node": ">=10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/swc"}, "optionalDependencies": {"@swc/core-darwin-arm64": "1.12.7", "@swc/core-darwin-x64": "1.12.7", "@swc/core-linux-arm-gnueabihf": "1.12.7", "@swc/core-linux-arm64-gnu": "1.12.7", "@swc/core-linux-arm64-musl": "1.12.7", "@swc/core-linux-x64-gnu": "1.12.7", "@swc/core-linux-x64-musl": "1.12.7", "@swc/core-win32-arm64-msvc": "1.12.7", "@swc/core-win32-ia32-msvc": "1.12.7", "@swc/core-win32-x64-msvc": "1.12.7"}, "peerDependencies": {"@swc/helpers": ">=0.5.17"}, "peerDependenciesMeta": {"@swc/helpers": {"optional": true}}}, "node_modules/@swc/core-win32-x64-msvc": {"version": "1.12.7", "cpu": ["x64"], "dev": true, "license": "Apache-2.0 AND MIT", "optional": true, "os": ["win32"], "peer": true, "engines": {"node": ">=10"}}, "node_modules/@swc/counter": {"version": "0.1.3", "dev": true, "license": "Apache-2.0", "optional": true, "peer": true}, "node_modules/@swc/helpers": {"version": "0.5.17", "dev": true, "license": "Apache-2.0", "optional": true, "peer": true, "dependencies": {"tslib": "^2.8.0"}}, "node_modules/@swc/types": {"version": "0.1.23", "dev": true, "license": "Apache-2.0", "optional": true, "peer": true, "dependencies": {"@swc/counter": "^0.1.3"}}, "node_modules/@tamagui/animations-css": {"version": "1.129.4", "license": "MIT", "dependencies": {"@tamagui/constants": "1.129.4", "@tamagui/cubic-bezier-animator": "1.129.4", "@tamagui/use-presence": "1.129.4", "@tamagui/web": "1.129.4"}, "peerDependencies": {"react": "*", "react-dom": "*"}}, "node_modules/@tamagui/animations-moti": {"version": "1.129.4", "license": "MIT", "dependencies": {"@tamagui/use-presence": "1.129.4", "@tamagui/web": "1.129.4", "moti": "^0.30.0"}, "peerDependencies": {"react": "*"}}, "node_modules/@tamagui/animations-react-native": {"version": "1.129.4", "license": "MIT", "dependencies": {"@tamagui/constants": "1.129.4", "@tamagui/use-presence": "1.129.4", "@tamagui/web": "1.129.4"}, "peerDependencies": {"react": "*"}}, "node_modules/@tamagui/colors": {"version": "1.129.4"}, "node_modules/@tamagui/compose-refs": {"version": "1.129.4", "peerDependencies": {"react": "*"}}, "node_modules/@tamagui/config": {"version": "1.129.4", "dependencies": {"@tamagui/animations-css": "1.129.4", "@tamagui/animations-moti": "1.129.4", "@tamagui/animations-react-native": "1.129.4", "@tamagui/colors": "1.129.4", "@tamagui/font-inter": "1.129.4", "@tamagui/font-silkscreen": "1.129.4", "@tamagui/react-native-media-driver": "1.129.4", "@tamagui/shorthands": "1.129.4", "@tamagui/themes": "1.129.4", "@tamagui/web": "1.129.4"}}, "node_modules/@tamagui/constants": {"version": "1.129.4", "peerDependencies": {"react": "*"}}, "node_modules/@tamagui/core": {"version": "1.129.4", "license": "MIT", "dependencies": {"@tamagui/react-native-media-driver": "1.129.4", "@tamagui/react-native-use-pressable": "1.129.4", "@tamagui/react-native-use-responder-events": "1.129.4", "@tamagui/use-event": "1.129.4", "@tamagui/web": "1.129.4"}}, "node_modules/@tamagui/create-theme": {"version": "1.129.4", "dependencies": {"@tamagui/web": "1.129.4"}}, "node_modules/@tamagui/cubic-bezier-animator": {"version": "1.129.4"}, "node_modules/@tamagui/font-inter": {"version": "1.129.4", "dependencies": {"@tamagui/core": "1.129.4"}}, "node_modules/@tamagui/font-silkscreen": {"version": "1.129.4", "dependencies": {"@tamagui/core": "1.129.4"}}, "node_modules/@tamagui/helpers": {"version": "1.129.4", "dependencies": {"@tamagui/constants": "1.129.4", "@tamagui/simple-hash": "1.129.4"}}, "node_modules/@tamagui/normalize-css-color": {"version": "1.129.4", "dependencies": {"@react-native/normalize-color": "^2.1.0"}}, "node_modules/@tamagui/react-native-media-driver": {"version": "1.129.4", "license": "MIT", "dependencies": {"@tamagui/web": "1.129.4"}, "peerDependencies": {"react-native": "*"}}, "node_modules/@tamagui/react-native-use-pressable": {"version": "1.129.4", "peerDependencies": {"react": "*"}}, "node_modules/@tamagui/react-native-use-responder-events": {"version": "1.129.4", "peerDependencies": {"react": "*"}}, "node_modules/@tamagui/shorthands": {"version": "1.129.4", "dependencies": {"@tamagui/web": "1.129.4"}}, "node_modules/@tamagui/simple-hash": {"version": "1.129.4"}, "node_modules/@tamagui/theme-base": {"version": "1.129.4"}, "node_modules/@tamagui/theme-builder": {"version": "1.129.4", "dependencies": {"@tamagui/create-theme": "1.129.4", "color2k": "^2.0.2"}}, "node_modules/@tamagui/themes": {"version": "1.129.4", "dependencies": {"@tamagui/colors": "1.129.4", "@tamagui/create-theme": "1.129.4", "@tamagui/theme-builder": "1.129.4", "@tamagui/web": "1.129.4", "color2k": "^2.0.2"}}, "node_modules/@tamagui/timer": {"version": "1.129.4"}, "node_modules/@tamagui/types": {"version": "1.129.4"}, "node_modules/@tamagui/use-did-finish-ssr": {"version": "1.129.4", "peerDependencies": {"react": "*"}}, "node_modules/@tamagui/use-event": {"version": "1.129.4", "dependencies": {"@tamagui/constants": "1.129.4"}, "peerDependencies": {"react": "*"}}, "node_modules/@tamagui/use-force-update": {"version": "1.129.4", "peerDependencies": {"react": "*"}}, "node_modules/@tamagui/use-presence": {"version": "1.129.4", "dependencies": {"@tamagui/web": "1.129.4"}, "peerDependencies": {"react": "*"}}, "node_modules/@tamagui/web": {"version": "1.129.4", "license": "MIT", "dependencies": {"@tamagui/compose-refs": "1.129.4", "@tamagui/constants": "1.129.4", "@tamagui/helpers": "1.129.4", "@tamagui/normalize-css-color": "1.129.4", "@tamagui/timer": "1.129.4", "@tamagui/types": "1.129.4", "@tamagui/use-did-finish-ssr": "1.129.4", "@tamagui/use-event": "1.129.4", "@tamagui/use-force-update": "1.129.4"}, "peerDependencies": {"react": "*", "react-dom": "*"}}, "node_modules/@tsconfig/node10": {"version": "1.0.11", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node12": {"version": "1.0.11", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node14": {"version": "1.0.3", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node16": {"version": "1.0.4", "dev": true, "license": "MIT"}, "node_modules/@types/babel__core": {"version": "7.20.5", "license": "MIT", "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "node_modules/@types/babel__generator": {"version": "7.27.0", "license": "MIT", "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@types/babel__template": {"version": "7.4.4", "license": "MIT", "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@types/babel__traverse": {"version": "7.20.7", "license": "MIT", "dependencies": {"@babel/types": "^7.20.7"}}, "node_modules/@types/bunyan": {"version": "1.8.11", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/cli-progress": {"version": "3.11.6", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/graceful-fs": {"version": "4.1.9", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/hammerjs": {"version": "2.0.46", "license": "MIT"}, "node_modules/@types/istanbul-lib-coverage": {"version": "2.0.6", "license": "MIT"}, "node_modules/@types/istanbul-lib-report": {"version": "3.0.3", "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "*"}}, "node_modules/@types/istanbul-reports": {"version": "3.0.4", "license": "MIT", "dependencies": {"@types/istanbul-lib-report": "*"}}, "node_modules/@types/node": {"version": "24.0.6", "license": "MIT", "dependencies": {"undici-types": "~7.8.0"}}, "node_modules/@types/react": {"version": "19.0.14", "devOptional": true, "license": "MIT", "dependencies": {"csstype": "^3.0.2"}}, "node_modules/@types/stack-utils": {"version": "2.0.3", "license": "MIT"}, "node_modules/@types/yargs": {"version": "17.0.33", "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/@types/yargs-parser": {"version": "21.0.3", "license": "MIT"}, "node_modules/@urql/core": {"version": "5.1.2", "license": "MIT", "dependencies": {"@0no-co/graphql.web": "^1.0.13", "wonka": "^6.3.2"}}, "node_modules/@urql/exchange-retry": {"version": "1.3.2", "license": "MIT", "dependencies": {"@urql/core": "^5.1.2", "wonka": "^6.3.2"}, "peerDependencies": {"@urql/core": "^5.0.0"}}, "node_modules/@xmldom/xmldom": {"version": "0.8.10", "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/abort-controller": {"version": "3.0.0", "license": "MIT", "dependencies": {"event-target-shim": "^5.0.0"}, "engines": {"node": ">=6.5"}}, "node_modules/accepts": {"version": "1.3.8", "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "8.15.0", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-walk": {"version": "8.3.4", "dev": true, "license": "MIT", "dependencies": {"acorn": "^8.11.0"}, "engines": {"node": ">=0.4.0"}}, "node_modules/agent-base": {"version": "7.1.3", "license": "MIT", "engines": {"node": ">= 14"}}, "node_modules/ajv": {"version": "8.11.0", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ajv-formats": {"version": "2.1.1", "dev": true, "license": "MIT", "dependencies": {"ajv": "^8.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "node_modules/anser": {"version": "1.4.10", "license": "MIT"}, "node_modules/ansi-escapes": {"version": "4.3.2", "license": "MIT", "dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-escapes/node_modules/type-fest": {"version": "0.21.3", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/ansicolors": {"version": "0.3.2", "dev": true, "license": "MIT"}, "node_modules/any-promise": {"version": "1.3.0", "license": "MIT"}, "node_modules/anymatch": {"version": "3.1.3", "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/arg": {"version": "5.0.2", "license": "MIT"}, "node_modules/argparse": {"version": "1.0.10", "license": "MIT", "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/array-union": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/asap": {"version": "2.0.6", "license": "MIT"}, "node_modules/asn1": {"version": "0.2.6", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": "~2.1.0"}}, "node_modules/astral-regex": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/async": {"version": "3.2.6", "dev": true, "license": "MIT"}, "node_modules/async-limiter": {"version": "1.0.1", "license": "MIT"}, "node_modules/asynckit": {"version": "0.4.0", "license": "MIT"}, "node_modules/at-least-node": {"version": "1.0.0", "dev": true, "license": "ISC", "engines": {"node": ">= 4.0.0"}}, "node_modules/axios": {"version": "1.10.0", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/b4a": {"version": "1.6.7", "dev": true, "license": "Apache-2.0"}, "node_modules/babel-jest": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/transform": "^29.7.0", "@types/babel__core": "^7.1.14", "babel-plugin-istanbul": "^6.1.1", "babel-preset-jest": "^29.6.3", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@babel/core": "^7.8.0"}}, "node_modules/babel-plugin-istanbul": {"version": "6.1.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^5.0.4", "test-exclude": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/babel-plugin-jest-hoist": {"version": "29.6.3", "license": "MIT", "dependencies": {"@babel/template": "^7.3.3", "@babel/types": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/babel-plugin-polyfill-corejs2": {"version": "0.4.14", "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.7", "@babel/helper-define-polyfill-provider": "^0.6.5", "semver": "^6.3.1"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/babel-plugin-polyfill-corejs2/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/babel-plugin-polyfill-corejs3": {"version": "0.11.1", "license": "MIT", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.3", "core-js-compat": "^3.40.0"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/babel-plugin-polyfill-regenerator": {"version": "0.6.5", "license": "MIT", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/babel-plugin-react-native-web": {"version": "0.19.13", "license": "MIT"}, "node_modules/babel-plugin-syntax-hermes-parser": {"version": "0.25.1", "license": "MIT", "dependencies": {"hermes-parser": "0.25.1"}}, "node_modules/babel-plugin-transform-flow-enums": {"version": "0.0.2", "license": "MIT", "dependencies": {"@babel/plugin-syntax-flow": "^7.12.1"}}, "node_modules/babel-preset-current-node-syntax": {"version": "1.1.0", "license": "MIT", "dependencies": {"@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/plugin-syntax-import-attributes": "^7.24.7", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-top-level-await": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/babel-preset-expo": {"version": "13.2.1", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.25.9", "@babel/plugin-proposal-decorators": "^7.12.9", "@babel/plugin-proposal-export-default-from": "^7.24.7", "@babel/plugin-syntax-export-default-from": "^7.24.7", "@babel/plugin-transform-export-namespace-from": "^7.25.9", "@babel/plugin-transform-flow-strip-types": "^7.25.2", "@babel/plugin-transform-modules-commonjs": "^7.24.8", "@babel/plugin-transform-object-rest-spread": "^7.24.7", "@babel/plugin-transform-parameters": "^7.24.7", "@babel/plugin-transform-private-methods": "^7.24.7", "@babel/plugin-transform-private-property-in-object": "^7.24.7", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/preset-react": "^7.22.15", "@babel/preset-typescript": "^7.23.0", "@react-native/babel-preset": "0.79.4", "babel-plugin-react-native-web": "~0.19.13", "babel-plugin-syntax-hermes-parser": "^0.25.1", "babel-plugin-transform-flow-enums": "^0.0.2", "debug": "^4.3.4", "react-refresh": "^0.14.2", "resolve-from": "^5.0.0"}, "peerDependencies": {"babel-plugin-react-compiler": "^19.0.0-beta-e993439-20250405"}, "peerDependenciesMeta": {"babel-plugin-react-compiler": {"optional": true}}}, "node_modules/babel-preset-jest": {"version": "29.6.3", "license": "MIT", "dependencies": {"babel-plugin-jest-hoist": "^29.6.3", "babel-preset-current-node-syntax": "^1.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "license": "MIT"}, "node_modules/bare-events": {"version": "2.5.4", "dev": true, "license": "Apache-2.0", "optional": true}, "node_modules/base64-js": {"version": "1.5.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/better-opn": {"version": "3.0.2", "license": "MIT", "dependencies": {"open": "^8.0.4"}, "engines": {"node": ">=12.0.0"}}, "node_modules/better-opn/node_modules/open": {"version": "8.4.2", "license": "MIT", "dependencies": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/big-integer": {"version": "1.6.52", "license": "Unlicense", "engines": {"node": ">=0.6"}}, "node_modules/boolbase": {"version": "1.0.0", "license": "ISC"}, "node_modules/bplist-creator": {"version": "0.1.0", "license": "MIT", "dependencies": {"stream-buffers": "2.2.x"}}, "node_modules/bplist-parser": {"version": "0.3.2", "license": "MIT", "dependencies": {"big-integer": "1.6.x"}, "engines": {"node": ">= 5.10.0"}}, "node_modules/brace-expansion": {"version": "2.0.2", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/braces": {"version": "3.0.3", "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.25.1", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001726", "electron-to-chromium": "^1.5.173", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/bser": {"version": "2.1.1", "license": "Apache-2.0", "dependencies": {"node-int64": "^0.4.0"}}, "node_modules/buffer": {"version": "5.7.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/buffer-equal-constant-time": {"version": "1.0.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/buffer-from": {"version": "1.1.2", "license": "MIT"}, "node_modules/bunyan": {"version": "1.8.15", "dev": true, "engines": ["node >=0.10.0"], "license": "MIT", "bin": {"bunyan": "bin/bunyan"}, "optionalDependencies": {"dtrace-provider": "~0.8", "moment": "^2.19.3", "mv": "~2", "safe-json-stringify": "~1"}}, "node_modules/bytes": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/caller-callsite": {"version": "2.0.0", "license": "MIT", "dependencies": {"callsites": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/caller-path": {"version": "2.0.0", "license": "MIT", "dependencies": {"caller-callsite": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/callsites": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/camelcase": {"version": "6.3.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/caniuse-lite": {"version": "1.0.30001726", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/cardinal": {"version": "2.1.1", "dev": true, "license": "MIT", "dependencies": {"ansicolors": "~0.3.2", "redeyed": "~2.1.0"}, "bin": {"cdl": "bin/cdl.js"}}, "node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chalk/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/charenc": {"version": "0.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": "*"}}, "node_modules/chownr": {"version": "3.0.0", "license": "BlueOak-1.0.0", "engines": {"node": ">=18"}}, "node_modules/chrome-launcher": {"version": "0.15.2", "license": "Apache-2.0", "dependencies": {"@types/node": "*", "escape-string-regexp": "^4.0.0", "is-wsl": "^2.2.0", "lighthouse-logger": "^1.0.0"}, "bin": {"print-chrome-path": "bin/print-chrome-path.js"}, "engines": {"node": ">=12.13.0"}}, "node_modules/chromium-edge-launcher": {"version": "0.2.0", "license": "Apache-2.0", "dependencies": {"@types/node": "*", "escape-string-regexp": "^4.0.0", "is-wsl": "^2.2.0", "lighthouse-logger": "^1.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2"}}, "node_modules/ci-info": {"version": "3.9.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/clean-stack": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"escape-string-regexp": "4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-cursor": {"version": "2.1.0", "license": "MIT", "dependencies": {"restore-cursor": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/cli-progress": {"version": "3.12.0", "dev": true, "license": "MIT", "dependencies": {"string-width": "^4.2.3"}, "engines": {"node": ">=4"}}, "node_modules/cli-spinners": {"version": "2.9.2", "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cliui": {"version": "8.0.1", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/clone": {"version": "1.0.4", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/color": {"version": "4.2.3", "license": "MIT", "dependencies": {"color-convert": "^2.0.1", "color-string": "^1.9.0"}, "engines": {"node": ">=12.5.0"}}, "node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/color-string": {"version": "1.9.1", "license": "MIT", "dependencies": {"color-name": "^1.0.0", "simple-swizzle": "^0.2.2"}}, "node_modules/color2k": {"version": "2.0.3", "license": "MIT"}, "node_modules/combined-stream": {"version": "1.0.8", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "2.20.3", "license": "MIT"}, "node_modules/component-type": {"version": "1.2.2", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/compressible": {"version": "2.0.18", "license": "MIT", "dependencies": {"mime-db": ">= 1.43.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/compressible/node_modules/mime-db": {"version": "1.54.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/compression": {"version": "1.8.0", "license": "MIT", "dependencies": {"bytes": "3.1.2", "compressible": "~2.0.18", "debug": "2.6.9", "negotiator": "~0.6.4", "on-headers": "~1.0.2", "safe-buffer": "5.2.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/compression/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/compression/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/compression/node_modules/negotiator": {"version": "0.6.4", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/concat-map": {"version": "0.0.1", "license": "MIT"}, "node_modules/connect": {"version": "3.7.0", "license": "MIT", "dependencies": {"debug": "2.6.9", "finalhandler": "1.1.2", "parseurl": "~1.3.3", "utils-merge": "1.0.1"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/connect/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/connect/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/content-type": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/convert-source-map": {"version": "2.0.0", "license": "MIT"}, "node_modules/core-js-compat": {"version": "3.43.0", "license": "MIT", "dependencies": {"browserslist": "^4.25.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/cosmiconfig": {"version": "5.2.1", "license": "MIT", "dependencies": {"import-fresh": "^2.0.0", "is-directory": "^0.3.1", "js-yaml": "^3.13.1", "parse-json": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/create-require": {"version": "1.1.1", "dev": true, "license": "MIT"}, "node_modules/cross-fetch": {"version": "3.2.0", "license": "MIT", "dependencies": {"node-fetch": "^2.7.0"}}, "node_modules/cross-fetch/node_modules/node-fetch": {"version": "2.7.0", "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/cross-spawn": {"version": "7.0.6", "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/crypt": {"version": "0.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": "*"}}, "node_modules/crypto-random-string": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/css-in-js-utils": {"version": "3.1.0", "license": "MIT", "dependencies": {"hyphenate-style-name": "^1.0.3"}}, "node_modules/css-select": {"version": "5.1.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0", "css-what": "^6.1.0", "domhandler": "^5.0.2", "domutils": "^3.0.1", "nth-check": "^2.0.1"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/css-tree": {"version": "1.1.3", "license": "MIT", "dependencies": {"mdn-data": "2.0.14", "source-map": "^0.6.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/css-tree/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/css-what": {"version": "6.1.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">= 6"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/csstype": {"version": "3.1.3", "devOptional": true, "license": "MIT"}, "node_modules/dateformat": {"version": "4.6.3", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/debug": {"version": "4.4.1", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decode-uri-component": {"version": "0.2.2", "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/deep-extend": {"version": "0.6.0", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/deepmerge": {"version": "4.3.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/defaults": {"version": "1.0.4", "license": "MIT", "dependencies": {"clone": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/define-lazy-prop": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/delayed-stream": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/depd": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/destroy": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/detect-libc": {"version": "1.0.3", "license": "Apache-2.0", "bin": {"detect-libc": "bin/detect-libc.js"}, "engines": {"node": ">=0.10"}}, "node_modules/diff": {"version": "4.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.3.1"}}, "node_modules/dir-glob": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/dom-serializer": {"version": "2.0.0", "license": "MIT", "dependencies": {"domelementtype": "^2.3.0", "domhandler": "^5.0.2", "entities": "^4.2.0"}, "funding": {"url": "https://github.com/cheeriojs/dom-serializer?sponsor=1"}}, "node_modules/domelementtype": {"version": "2.3.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/domhandler": {"version": "5.0.3", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"domelementtype": "^2.3.0"}, "engines": {"node": ">= 4"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}}, "node_modules/domino": {"version": "2.1.6", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/domutils": {"version": "3.2.2", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dom-serializer": "^2.0.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "node_modules/dotenv": {"version": "16.4.7", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/dotenv-expand": {"version": "11.0.7", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dotenv": "^16.4.5"}, "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/dtrace-provider": {"version": "0.8.8", "dev": true, "hasInstallScript": true, "license": "BSD-2-<PERSON><PERSON>", "optional": true, "dependencies": {"nan": "^2.14.0"}, "engines": {"node": ">=0.10"}}, "node_modules/eas-cli": {"version": "16.13.2", "dev": true, "license": "MIT", "dependencies": {"@expo/apple-utils": "2.1.12", "@expo/code-signing-certificates": "0.0.5", "@expo/config": "10.0.6", "@expo/config-plugins": "9.0.12", "@expo/eas-build-job": "1.0.173", "@expo/eas-json": "16.13.2", "@expo/env": "^1.0.0", "@expo/json-file": "8.3.3", "@expo/logger": "1.0.117", "@expo/multipart-body-parser": "2.0.0", "@expo/osascript": "2.1.4", "@expo/package-manager": "1.7.0", "@expo/pkcs12": "0.1.3", "@expo/plist": "0.2.0", "@expo/plugin-help": "5.1.23", "@expo/plugin-warn-if-update-available": "2.5.1", "@expo/prebuild-config": "8.0.17", "@expo/results": "1.0.0", "@expo/rudder-sdk-node": "1.1.1", "@expo/spawn-async": "1.7.2", "@expo/steps": "1.0.173", "@expo/timeago.js": "1.0.0", "@oclif/core": "^1.26.2", "@oclif/plugin-autocomplete": "^2.3.10", "@segment/ajv-human-errors": "^2.1.2", "@urql/core": "4.0.11", "@urql/exchange-retry": "1.2.0", "ajv": "8.11.0", "ajv-formats": "2.1.1", "better-opn": "3.0.2", "bplist-parser": "^0.3.0", "chalk": "4.1.2", "cli-progress": "3.12.0", "dateformat": "4.6.3", "diff": "7.0.0", "dotenv": "16.3.1", "env-paths": "2.2.0", "envinfo": "7.11.0", "fast-deep-equal": "3.1.3", "fast-glob": "3.3.2", "figures": "3.2.0", "form-data": "4.0.0", "fs-extra": "11.2.0", "getenv": "1.0.0", "gradle-to-js": "2.0.1", "graphql": "16.8.1", "graphql-tag": "2.12.6", "https-proxy-agent": "5.0.1", "ignore": "5.3.0", "indent-string": "4.0.0", "jks-js": "1.1.0", "joi": "17.11.0", "jsonwebtoken": "9.0.0", "keychain": "1.5.0", "log-symbols": "4.1.0", "mime": "3.0.0", "minimatch": "5.1.2", "minizlib": "3.0.1", "nanoid": "3.3.8", "node-fetch": "2.6.7", "node-forge": "1.3.1", "node-stream-zip": "1.15.0", "nullthrows": "1.1.1", "ora": "5.1.0", "pkg-dir": "4.2.0", "pngjs": "7.0.0", "promise-limit": "2.7.0", "promise-retry": "2.0.1", "prompts": "2.4.2", "qrcode-terminal": "0.12.0", "resolve-from": "5.0.0", "semver": "7.5.4", "set-interval-async": "3.0.3", "slash": "3.0.0", "tar": "6.2.1", "tar-stream": "3.1.7", "terminal-link": "2.1.1", "tslib": "2.6.2", "turndown": "7.1.2", "untildify": "4.0.0", "uuid": "9.0.1", "wrap-ansi": "7.0.0", "yaml": "2.6.0", "zod": "^3.23.8"}, "bin": {"eas": "bin/run"}, "engines": {"node": ">=18.0.0"}}, "node_modules/eas-cli/node_modules/@expo/config": {"version": "10.0.6", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "~7.10.4", "@expo/config-plugins": "~9.0.10", "@expo/config-types": "^52.0.0", "@expo/json-file": "^9.0.0", "deepmerge": "^4.3.1", "getenv": "^1.0.0", "glob": "^10.4.2", "require-from-string": "^2.0.2", "resolve-from": "^5.0.0", "resolve-workspace-root": "^2.0.0", "semver": "^7.6.0", "slugify": "^1.3.4", "sucrase": "3.35.0"}}, "node_modules/eas-cli/node_modules/@expo/config-plugins": {"version": "9.0.12", "dev": true, "license": "MIT", "dependencies": {"@expo/config-types": "^52.0.0", "@expo/json-file": "~9.0.0", "@expo/plist": "^0.2.0", "@expo/sdk-runtime-versions": "^1.0.0", "chalk": "^4.1.2", "debug": "^4.3.5", "getenv": "^1.0.0", "glob": "^10.4.2", "resolve-from": "^5.0.0", "semver": "^7.5.4", "slash": "^3.0.0", "slugify": "^1.6.6", "xcode": "^3.0.1", "xml2js": "0.6.0"}}, "node_modules/eas-cli/node_modules/@expo/config-plugins/node_modules/@expo/json-file": {"version": "9.0.2", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "~7.10.4", "json5": "^2.2.3", "write-file-atomic": "^2.3.0"}}, "node_modules/eas-cli/node_modules/@expo/config-types": {"version": "52.0.5", "dev": true, "license": "MIT"}, "node_modules/eas-cli/node_modules/@expo/config/node_modules/@expo/json-file": {"version": "9.1.4", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "~7.10.4", "json5": "^2.2.3"}}, "node_modules/eas-cli/node_modules/@expo/config/node_modules/semver": {"version": "7.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/eas-cli/node_modules/@expo/image-utils": {"version": "0.6.5", "dev": true, "license": "MIT", "dependencies": {"@expo/spawn-async": "^1.7.2", "chalk": "^4.0.0", "fs-extra": "9.0.0", "getenv": "^1.0.0", "jimp-compact": "0.16.1", "parse-png": "^2.1.0", "resolve-from": "^5.0.0", "semver": "^7.6.0", "temp-dir": "~2.0.0", "unique-string": "~2.0.0"}}, "node_modules/eas-cli/node_modules/@expo/image-utils/node_modules/fs-extra": {"version": "9.0.0", "dev": true, "license": "MIT", "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^1.0.0"}, "engines": {"node": ">=10"}}, "node_modules/eas-cli/node_modules/@expo/image-utils/node_modules/semver": {"version": "7.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/eas-cli/node_modules/@expo/image-utils/node_modules/universalify": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/eas-cli/node_modules/@expo/json-file": {"version": "8.3.3", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "~7.10.4", "json5": "^2.2.2", "write-file-atomic": "^2.3.0"}}, "node_modules/eas-cli/node_modules/@expo/osascript": {"version": "2.1.4", "dev": true, "license": "MIT", "dependencies": {"@expo/spawn-async": "^1.7.2", "exec-async": "^2.2.0"}, "engines": {"node": ">=12"}}, "node_modules/eas-cli/node_modules/@expo/package-manager": {"version": "1.7.0", "dev": true, "license": "MIT", "dependencies": {"@expo/json-file": "^9.0.0", "@expo/spawn-async": "^1.7.2", "ansi-regex": "^5.0.0", "chalk": "^4.0.0", "find-up": "^5.0.0", "js-yaml": "^3.13.1", "micromatch": "^4.0.8", "npm-package-arg": "^11.0.0", "ora": "^3.4.0", "resolve-workspace-root": "^2.0.0", "split": "^1.0.1", "sudo-prompt": "9.1.1"}}, "node_modules/eas-cli/node_modules/@expo/package-manager/node_modules/@expo/json-file": {"version": "9.1.4", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "~7.10.4", "json5": "^2.2.3"}}, "node_modules/eas-cli/node_modules/@expo/package-manager/node_modules/ansi-styles": {"version": "3.2.1", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/eas-cli/node_modules/@expo/package-manager/node_modules/log-symbols": {"version": "2.2.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^2.0.1"}, "engines": {"node": ">=4"}}, "node_modules/eas-cli/node_modules/@expo/package-manager/node_modules/log-symbols/node_modules/chalk": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/eas-cli/node_modules/@expo/package-manager/node_modules/ora": {"version": "3.4.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^2.4.2", "cli-cursor": "^2.1.0", "cli-spinners": "^2.0.0", "log-symbols": "^2.2.0", "strip-ansi": "^5.2.0", "wcwidth": "^1.0.1"}, "engines": {"node": ">=6"}}, "node_modules/eas-cli/node_modules/@expo/package-manager/node_modules/ora/node_modules/chalk": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/eas-cli/node_modules/@expo/package-manager/node_modules/strip-ansi": {"version": "5.2.0", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^4.1.0"}, "engines": {"node": ">=6"}}, "node_modules/eas-cli/node_modules/@expo/package-manager/node_modules/strip-ansi/node_modules/ansi-regex": {"version": "4.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/eas-cli/node_modules/@expo/package-manager/node_modules/supports-color": {"version": "5.5.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/eas-cli/node_modules/@expo/plist": {"version": "0.2.0", "dev": true, "license": "MIT", "dependencies": {"@xmldom/xmldom": "~0.7.7", "base64-js": "^1.2.3", "xmlbuilder": "^14.0.0"}}, "node_modules/eas-cli/node_modules/@expo/prebuild-config": {"version": "8.0.17", "dev": true, "license": "MIT", "dependencies": {"@expo/config": "~10.0.4", "@expo/config-plugins": "~9.0.0", "@expo/config-types": "^52.0.0", "@expo/image-utils": "^0.6.0", "@expo/json-file": "^9.0.0", "@react-native/normalize-colors": "0.76.2", "debug": "^4.3.1", "fs-extra": "^9.0.0", "resolve-from": "^5.0.0", "semver": "^7.6.0", "xml2js": "0.6.0"}}, "node_modules/eas-cli/node_modules/@expo/prebuild-config/node_modules/@expo/json-file": {"version": "9.1.4", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "~7.10.4", "json5": "^2.2.3"}}, "node_modules/eas-cli/node_modules/@expo/prebuild-config/node_modules/fs-extra": {"version": "9.1.0", "dev": true, "license": "MIT", "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/eas-cli/node_modules/@expo/prebuild-config/node_modules/semver": {"version": "7.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/eas-cli/node_modules/@oclif/core": {"version": "1.26.2", "dev": true, "license": "MIT", "dependencies": {"@oclif/linewrap": "^1.0.0", "@oclif/screen": "^3.0.4", "ansi-escapes": "^4.3.2", "ansi-styles": "^4.3.0", "cardinal": "^2.1.1", "chalk": "^4.1.2", "clean-stack": "^3.0.1", "cli-progress": "^3.10.0", "debug": "^4.3.4", "ejs": "^3.1.6", "fs-extra": "^9.1.0", "get-package-type": "^0.1.0", "globby": "^11.1.0", "hyperlinker": "^1.0.0", "indent-string": "^4.0.0", "is-wsl": "^2.2.0", "js-yaml": "^3.14.1", "natural-orderby": "^2.0.3", "object-treeify": "^1.1.33", "password-prompt": "^1.1.2", "semver": "^7.3.7", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "supports-color": "^8.1.1", "supports-hyperlinks": "^2.2.0", "tslib": "^2.4.1", "widest-line": "^3.1.0", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/eas-cli/node_modules/@oclif/core/node_modules/fs-extra": {"version": "9.1.0", "dev": true, "license": "MIT", "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/eas-cli/node_modules/@react-native/normalize-colors": {"version": "0.76.2", "dev": true, "license": "MIT"}, "node_modules/eas-cli/node_modules/@urql/core": {"version": "4.0.11", "dev": true, "license": "MIT", "dependencies": {"@0no-co/graphql.web": "^1.0.1", "wonka": "^6.3.2"}}, "node_modules/eas-cli/node_modules/@urql/exchange-retry": {"version": "1.2.0", "dev": true, "license": "MIT", "dependencies": {"@urql/core": ">=4.0.0", "wonka": "^6.3.2"}}, "node_modules/eas-cli/node_modules/@xmldom/xmldom": {"version": "0.7.13", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/eas-cli/node_modules/agent-base": {"version": "6.0.2", "dev": true, "license": "MIT", "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/eas-cli/node_modules/chownr": {"version": "2.0.0", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/eas-cli/node_modules/color-convert": {"version": "1.9.3", "dev": true, "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/eas-cli/node_modules/color-name": {"version": "1.1.3", "dev": true, "license": "MIT"}, "node_modules/eas-cli/node_modules/diff": {"version": "7.0.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.3.1"}}, "node_modules/eas-cli/node_modules/dotenv": {"version": "16.3.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/motdotla/dotenv?sponsor=1"}}, "node_modules/eas-cli/node_modules/escape-string-regexp": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/eas-cli/node_modules/has-flag": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/eas-cli/node_modules/https-proxy-agent": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/eas-cli/node_modules/joi": {"version": "17.11.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}}, "node_modules/eas-cli/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/eas-cli/node_modules/mime": {"version": "3.0.0", "dev": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=10.0.0"}}, "node_modules/eas-cli/node_modules/mimic-fn": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/eas-cli/node_modules/minimatch": {"version": "5.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/eas-cli/node_modules/minizlib": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"minipass": "^7.0.4", "rimraf": "^5.0.5"}, "engines": {"node": ">= 18"}}, "node_modules/eas-cli/node_modules/nanoid": {"version": "3.3.8", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/eas-cli/node_modules/onetime": {"version": "5.1.2", "dev": true, "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eas-cli/node_modules/ora": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.0", "cli-cursor": "^3.1.0", "cli-spinners": "^2.4.0", "is-interactive": "^1.0.0", "log-symbols": "^4.0.0", "mute-stream": "0.0.8", "strip-ansi": "^6.0.0", "wcwidth": "^1.0.1"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eas-cli/node_modules/ora/node_modules/cli-cursor": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"restore-cursor": "^3.1.0"}, "engines": {"node": ">=8"}}, "node_modules/eas-cli/node_modules/pngjs": {"version": "7.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=14.19.0"}}, "node_modules/eas-cli/node_modules/qrcode-terminal": {"version": "0.12.0", "dev": true, "bin": {"qrcode-terminal": "bin/qrcode-terminal.js"}}, "node_modules/eas-cli/node_modules/restore-cursor": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=8"}}, "node_modules/eas-cli/node_modules/rimraf": {"version": "5.0.10", "dev": true, "license": "ISC", "dependencies": {"glob": "^10.3.7"}, "bin": {"rimraf": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/eas-cli/node_modules/semver": {"version": "7.5.4", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/eas-cli/node_modules/tar": {"version": "6.2.1", "dev": true, "license": "ISC", "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/eas-cli/node_modules/tar/node_modules/minipass": {"version": "5.0.0", "dev": true, "license": "ISC", "engines": {"node": ">=8"}}, "node_modules/eas-cli/node_modules/tar/node_modules/minizlib": {"version": "2.1.2", "dev": true, "license": "MIT", "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/eas-cli/node_modules/tar/node_modules/minizlib/node_modules/minipass": {"version": "3.3.6", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/eas-cli/node_modules/tslib": {"version": "2.6.2", "dev": true, "license": "0BSD"}, "node_modules/eas-cli/node_modules/uuid": {"version": "9.0.1", "dev": true, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/eas-cli/node_modules/xmlbuilder": {"version": "14.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8.0"}}, "node_modules/eastasianwidth": {"version": "0.2.0", "license": "MIT"}, "node_modules/ecdsa-sig-formatter": {"version": "1.0.11", "dev": true, "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}}, "node_modules/ee-first": {"version": "1.1.1", "license": "MIT"}, "node_modules/ejs": {"version": "3.1.10", "dev": true, "license": "Apache-2.0", "dependencies": {"jake": "^10.8.5"}, "bin": {"ejs": "bin/cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/electron-to-chromium": {"version": "1.5.177", "license": "ISC"}, "node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "node_modules/encodeurl": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/entities": {"version": "4.5.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/env-editor": {"version": "0.4.2", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/env-paths": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/env-string": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/envinfo": {"version": "7.11.0", "dev": true, "license": "MIT", "bin": {"envinfo": "dist/cli.js"}, "engines": {"node": ">=4"}}, "node_modules/err-code": {"version": "2.0.3", "dev": true, "license": "MIT"}, "node_modules/error-ex": {"version": "1.3.2", "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/error-stack-parser": {"version": "2.1.4", "license": "MIT", "dependencies": {"stackframe": "^1.3.4"}}, "node_modules/escalade": {"version": "3.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/esprima": {"version": "4.0.1", "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/etag": {"version": "1.8.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/event-target-shim": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/exec-async": {"version": "2.2.0", "license": "MIT"}, "node_modules/expo": {"version": "53.0.13", "license": "MIT", "dependencies": {"@babel/runtime": "^7.20.0", "@expo/cli": "0.24.15", "@expo/config": "~11.0.10", "@expo/config-plugins": "~10.0.3", "@expo/fingerprint": "0.13.1", "@expo/metro-config": "0.20.15", "@expo/vector-icons": "^14.0.0", "babel-preset-expo": "~13.2.1", "expo-asset": "~11.1.5", "expo-constants": "~17.1.6", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-keep-awake": "~14.1.4", "expo-modules-autolinking": "2.1.12", "expo-modules-core": "2.4.0", "react-native-edge-to-edge": "1.6.0", "whatwg-url-without-unicode": "8.0.0-3"}, "bin": {"expo": "bin/cli", "expo-modules-autolinking": "bin/autolinking", "fingerprint": "bin/fingerprint"}, "peerDependencies": {"@expo/dom-webview": "*", "@expo/metro-runtime": "*", "react": "*", "react-native": "*", "react-native-webview": "*"}, "peerDependenciesMeta": {"@expo/dom-webview": {"optional": true}, "@expo/metro-runtime": {"optional": true}, "react-native-webview": {"optional": true}}}, "node_modules/expo-asset": {"version": "11.1.5", "license": "MIT", "dependencies": {"@expo/image-utils": "^0.7.4", "expo-constants": "~17.1.5"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*"}}, "node_modules/expo-constants": {"version": "17.1.6", "license": "MIT", "dependencies": {"@expo/config": "~11.0.9", "@expo/env": "~1.0.5"}, "peerDependencies": {"expo": "*", "react-native": "*"}}, "node_modules/expo-file-system": {"version": "18.1.10", "license": "MIT", "peerDependencies": {"expo": "*", "react-native": "*"}}, "node_modules/expo-font": {"version": "13.3.1", "license": "MIT", "dependencies": {"fontfaceobserver": "^2.1.0"}, "peerDependencies": {"expo": "*", "react": "*"}}, "node_modules/expo-keep-awake": {"version": "14.1.4", "license": "MIT", "peerDependencies": {"expo": "*", "react": "*"}}, "node_modules/expo-modules-autolinking": {"version": "2.1.12", "license": "MIT", "dependencies": {"@expo/spawn-async": "^1.7.2", "chalk": "^4.1.0", "commander": "^7.2.0", "find-up": "^5.0.0", "glob": "^10.4.2", "require-from-string": "^2.0.2", "resolve-from": "^5.0.0"}, "bin": {"expo-modules-autolinking": "bin/expo-modules-autolinking.js"}}, "node_modules/expo-modules-autolinking/node_modules/commander": {"version": "7.2.0", "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/expo-modules-core": {"version": "2.4.0", "license": "MIT", "dependencies": {"invariant": "^2.2.4"}}, "node_modules/expo-status-bar": {"version": "2.2.3", "license": "MIT", "dependencies": {"react-native-edge-to-edge": "1.6.0", "react-native-is-edge-to-edge": "^1.1.6"}, "peerDependencies": {"react": "*", "react-native": "*"}}, "node_modules/exponential-backoff": {"version": "3.1.2", "license": "Apache-2.0"}, "node_modules/fast-deep-equal": {"version": "3.1.3", "license": "MIT"}, "node_modules/fast-fifo": {"version": "1.3.2", "dev": true, "license": "MIT"}, "node_modules/fast-glob": {"version": "3.3.2", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "license": "MIT"}, "node_modules/fastq": {"version": "1.19.1", "dev": true, "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fb-watchman": {"version": "2.0.2", "license": "Apache-2.0", "dependencies": {"bser": "2.1.1"}}, "node_modules/fbjs": {"version": "3.0.5", "license": "MIT", "dependencies": {"cross-fetch": "^3.1.5", "fbjs-css-vars": "^1.0.0", "loose-envify": "^1.0.0", "object-assign": "^4.1.0", "promise": "^7.1.1", "setimmediate": "^1.0.5", "ua-parser-js": "^1.0.35"}}, "node_modules/fbjs-css-vars": {"version": "1.0.2", "license": "MIT"}, "node_modules/fbjs/node_modules/promise": {"version": "7.3.1", "license": "MIT", "dependencies": {"asap": "~2.0.3"}}, "node_modules/fetch-retry": {"version": "4.1.1", "dev": true, "license": "MIT"}, "node_modules/figures": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"escape-string-regexp": "^1.0.5"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/figures/node_modules/escape-string-regexp": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/filelist": {"version": "1.0.4", "dev": true, "license": "Apache-2.0", "dependencies": {"minimatch": "^5.0.1"}}, "node_modules/filelist/node_modules/minimatch": {"version": "5.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/fill-range": {"version": "7.1.1", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/filter-obj": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/finalhandler": {"version": "1.1.2", "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/finalhandler/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/finalhandler/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/finalhandler/node_modules/on-finished": {"version": "2.3.0", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/finalhandler/node_modules/statuses": {"version": "1.5.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/find-up": {"version": "5.0.0", "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/find-up/node_modules/locate-path": {"version": "6.0.0", "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/find-up/node_modules/p-locate": {"version": "5.0.0", "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flow-enums-runtime": {"version": "0.0.6", "license": "MIT"}, "node_modules/follow-redirects": {"version": "1.15.9", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/fontfaceobserver": {"version": "2.3.0", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/foreground-child": {"version": "3.3.1", "license": "ISC", "dependencies": {"cross-spawn": "^7.0.6", "signal-exit": "^4.0.1"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/foreground-child/node_modules/signal-exit": {"version": "4.1.0", "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/form-data": {"version": "4.0.0", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/framer-motion": {"version": "6.5.1", "license": "MIT", "dependencies": {"@motionone/dom": "10.12.0", "framesync": "6.0.1", "hey-listen": "^1.0.8", "popmotion": "11.0.3", "style-value-types": "5.0.0", "tslib": "^2.1.0"}, "optionalDependencies": {"@emotion/is-prop-valid": "^0.8.2"}, "peerDependencies": {"react": ">=16.8 || ^17.0.0 || ^18.0.0", "react-dom": ">=16.8 || ^17.0.0 || ^18.0.0"}}, "node_modules/framesync": {"version": "6.0.1", "license": "MIT", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/freeport-async": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/fresh": {"version": "0.5.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fs-extra": {"version": "11.2.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/fs-minipass": {"version": "2.1.0", "dev": true, "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/fs-minipass/node_modules/minipass": {"version": "3.3.6", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/fs.realpath": {"version": "1.0.0", "license": "ISC"}, "node_modules/function-bind": {"version": "1.1.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/get-caller-file": {"version": "2.0.5", "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-package-type": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=8.0.0"}}, "node_modules/getenv": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/glob": {"version": "10.4.5", "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "5.1.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/glob/node_modules/minimatch": {"version": "9.0.5", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/globals": {"version": "11.12.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/globby": {"version": "11.1.0", "dev": true, "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/golden-fleece": {"version": "1.0.9", "dev": true}, "node_modules/graceful-fs": {"version": "4.2.11", "license": "ISC"}, "node_modules/gradle-to-js": {"version": "2.0.1", "dev": true, "license": "Apache-2.0", "dependencies": {"lodash.merge": "^4.6.2"}, "bin": {"gradle-to-js": "cli.js"}}, "node_modules/graphql": {"version": "16.8.1", "devOptional": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.16.0 || ^16.0.0 || >=17.0.0"}}, "node_modules/graphql-tag": {"version": "2.12.6", "dev": true, "license": "MIT", "dependencies": {"tslib": "^2.1.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"graphql": "^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/graphql-tag/node_modules/tslib": {"version": "2.6.2", "dev": true, "license": "0BSD"}, "node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/hasown": {"version": "2.0.2", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/hermes-estree": {"version": "0.25.1", "license": "MIT"}, "node_modules/hermes-parser": {"version": "0.25.1", "license": "MIT", "dependencies": {"hermes-estree": "0.25.1"}}, "node_modules/hey-listen": {"version": "1.0.8", "license": "MIT"}, "node_modules/hoist-non-react-statics": {"version": "3.3.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"react-is": "^16.7.0"}}, "node_modules/hoist-non-react-statics/node_modules/react-is": {"version": "16.13.1", "license": "MIT"}, "node_modules/hosted-git-info": {"version": "7.0.2", "license": "ISC", "dependencies": {"lru-cache": "^10.0.1"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/http-call": {"version": "5.3.0", "dev": true, "license": "ISC", "dependencies": {"content-type": "^1.0.4", "debug": "^4.1.1", "is-retry-allowed": "^1.1.0", "is-stream": "^2.0.0", "parse-json": "^4.0.0", "tunnel-agent": "^0.6.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/http-errors": {"version": "2.0.0", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/https-proxy-agent": {"version": "7.0.6", "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "4"}, "engines": {"node": ">= 14"}}, "node_modules/hyperlinker": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/hyphenate-style-name": {"version": "1.1.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ieee754": {"version": "1.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ignore": {"version": "5.3.0", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/image-size": {"version": "1.2.1", "license": "MIT", "dependencies": {"queue": "6.0.2"}, "bin": {"image-size": "bin/image-size.js"}, "engines": {"node": ">=16.x"}}, "node_modules/import-fresh": {"version": "2.0.0", "license": "MIT", "dependencies": {"caller-path": "^2.0.0", "resolve-from": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/import-fresh/node_modules/resolve-from": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/imurmurhash": {"version": "0.1.4", "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/indent-string": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/inflight": {"version": "1.0.6", "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "license": "ISC"}, "node_modules/ini": {"version": "1.3.8", "license": "ISC"}, "node_modules/inline-style-prefixer": {"version": "7.0.1", "license": "MIT", "dependencies": {"css-in-js-utils": "^3.1.0"}}, "node_modules/invariant": {"version": "2.2.4", "license": "MIT", "dependencies": {"loose-envify": "^1.0.0"}}, "node_modules/is-arrayish": {"version": "0.2.1", "license": "MIT"}, "node_modules/is-buffer": {"version": "1.1.6", "dev": true, "license": "MIT"}, "node_modules/is-core-module": {"version": "2.16.1", "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-directory": {"version": "0.3.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-docker": {"version": "2.2.1", "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-extglob": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-glob": {"version": "4.0.3", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-interactive": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-number": {"version": "7.0.0", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-plain-obj": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-retry-allowed": {"version": "1.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-stream": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-unicode-supported": {"version": "0.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-wsl": {"version": "2.2.0", "license": "MIT", "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/isexe": {"version": "2.0.0", "license": "ISC"}, "node_modules/istanbul-lib-coverage": {"version": "3.2.2", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-instrument": {"version": "5.2.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0", "semver": "^6.3.0"}, "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-instrument/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/jackspeak": {"version": "3.4.3", "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/jake": {"version": "10.9.2", "dev": true, "license": "Apache-2.0", "dependencies": {"async": "^3.2.3", "chalk": "^4.0.2", "filelist": "^1.0.4", "minimatch": "^3.1.2"}, "bin": {"jake": "bin/cli.js"}, "engines": {"node": ">=10"}}, "node_modules/jest-environment-node": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/environment": "^29.7.0", "@jest/fake-timers": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "jest-mock": "^29.7.0", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-get-type": {"version": "29.6.3", "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-haste-map": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/graceful-fs": "^4.1.3", "@types/node": "*", "anymatch": "^3.0.3", "fb-watchman": "^2.0.0", "graceful-fs": "^4.2.9", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "jest-worker": "^29.7.0", "micromatch": "^4.0.4", "walker": "^1.0.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "optionalDependencies": {"fsevents": "^2.3.2"}}, "node_modules/jest-message-util": {"version": "29.7.0", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.12.13", "@jest/types": "^29.6.3", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "micromatch": "^4.0.4", "pretty-format": "^29.7.0", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-message-util/node_modules/@babel/code-frame": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/jest-mock": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-regex-util": {"version": "29.6.3", "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-util": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-validate": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "camelcase": "^6.2.0", "chalk": "^4.0.0", "jest-get-type": "^29.6.3", "leven": "^3.1.0", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-worker": {"version": "29.7.0", "license": "MIT", "dependencies": {"@types/node": "*", "jest-util": "^29.7.0", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jimp-compact": {"version": "0.16.1", "license": "MIT"}, "node_modules/jks-js": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"node-forge": "^1.3.1", "node-int64": "^0.4.0", "node-rsa": "^1.1.1"}}, "node_modules/joi": {"version": "17.13.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.3.0", "@hapi/topo": "^5.1.0", "@sideway/address": "^4.1.5", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}}, "node_modules/join-component": {"version": "1.1.0", "dev": true, "license": "MIT"}, "node_modules/js-tokens": {"version": "4.0.0", "license": "MIT"}, "node_modules/js-yaml": {"version": "3.14.1", "license": "MIT", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsc-safe-url": {"version": "0.2.4", "license": "0BSD"}, "node_modules/jsep": {"version": "1.4.0", "dev": true, "license": "MIT", "engines": {"node": ">= 10.16.0"}}, "node_modules/jsesc": {"version": "3.1.0", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/json-parse-better-errors": {"version": "1.0.2", "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/json5": {"version": "2.2.3", "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jsonwebtoken": {"version": "9.0.0", "dev": true, "license": "MIT", "dependencies": {"jws": "^3.2.2", "lodash": "^4.17.21", "ms": "^2.1.1", "semver": "^7.3.8"}, "engines": {"node": ">=12", "npm": ">=6"}}, "node_modules/jsonwebtoken/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/jsonwebtoken/node_modules/semver": {"version": "7.5.4", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/jwa": {"version": "1.4.2", "dev": true, "license": "MIT", "dependencies": {"buffer-equal-constant-time": "^1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}}, "node_modules/jws": {"version": "3.2.2", "dev": true, "license": "MIT", "dependencies": {"jwa": "^1.4.1", "safe-buffer": "^5.0.1"}}, "node_modules/keychain": {"version": "1.5.0", "dev": true, "license": "MIT"}, "node_modules/kleur": {"version": "3.0.3", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/lan-network": {"version": "0.1.7", "license": "MIT", "bin": {"lan-network": "dist/lan-network-cli.js"}}, "node_modules/leven": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/lighthouse-logger": {"version": "1.4.2", "license": "Apache-2.0", "dependencies": {"debug": "^2.6.9", "marky": "^1.2.2"}}, "node_modules/lighthouse-logger/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/lighthouse-logger/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/lightningcss": {"version": "1.27.0", "license": "MPL-2.0", "dependencies": {"detect-libc": "^1.0.3"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "optionalDependencies": {"lightningcss-darwin-arm64": "1.27.0", "lightningcss-darwin-x64": "1.27.0", "lightningcss-freebsd-x64": "1.27.0", "lightningcss-linux-arm-gnueabihf": "1.27.0", "lightningcss-linux-arm64-gnu": "1.27.0", "lightningcss-linux-arm64-musl": "1.27.0", "lightningcss-linux-x64-gnu": "1.27.0", "lightningcss-linux-x64-musl": "1.27.0", "lightningcss-win32-arm64-msvc": "1.27.0", "lightningcss-win32-x64-msvc": "1.27.0"}}, "node_modules/lightningcss-win32-x64-msvc": {"version": "1.27.0", "cpu": ["x64"], "license": "MPL-2.0", "optional": true, "os": ["win32"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lines-and-columns": {"version": "1.2.4", "license": "MIT"}, "node_modules/locate-path": {"version": "5.0.0", "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/lodash": {"version": "4.17.21", "dev": true, "license": "MIT"}, "node_modules/lodash.clonedeep": {"version": "4.5.0", "dev": true, "license": "MIT"}, "node_modules/lodash.debounce": {"version": "4.0.8", "license": "MIT"}, "node_modules/lodash.get": {"version": "4.4.2", "dev": true, "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "dev": true, "license": "MIT"}, "node_modules/lodash.throttle": {"version": "4.1.1", "license": "MIT"}, "node_modules/log-symbols": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/loose-envify": {"version": "1.4.0", "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/lru-cache": {"version": "10.4.3", "license": "ISC"}, "node_modules/make-error": {"version": "1.3.6", "dev": true, "license": "ISC"}, "node_modules/makeerror": {"version": "1.0.12", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tmpl": "1.0.5"}}, "node_modules/marky": {"version": "1.3.0", "license": "Apache-2.0"}, "node_modules/md5": {"version": "2.3.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"charenc": "0.0.2", "crypt": "0.0.2", "is-buffer": "~1.1.6"}}, "node_modules/mdn-data": {"version": "2.0.14", "license": "CC0-1.0"}, "node_modules/memoize-one": {"version": "5.2.1", "license": "MIT"}, "node_modules/merge-options": {"version": "3.0.4", "license": "MIT", "dependencies": {"is-plain-obj": "^2.1.0"}, "engines": {"node": ">=10"}}, "node_modules/merge-stream": {"version": "2.0.0", "license": "MIT"}, "node_modules/merge2": {"version": "1.4.1", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/metro": {"version": "0.82.4", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.24.7", "@babel/core": "^7.25.2", "@babel/generator": "^7.25.0", "@babel/parser": "^7.25.3", "@babel/template": "^7.25.0", "@babel/traverse": "^7.25.3", "@babel/types": "^7.25.2", "accepts": "^1.3.7", "chalk": "^4.0.0", "ci-info": "^2.0.0", "connect": "^3.6.5", "debug": "^4.4.0", "error-stack-parser": "^2.0.6", "flow-enums-runtime": "^0.0.6", "graceful-fs": "^4.2.4", "hermes-parser": "0.28.1", "image-size": "^1.0.2", "invariant": "^2.2.4", "jest-worker": "^29.7.0", "jsc-safe-url": "^0.2.2", "lodash.throttle": "^4.1.1", "metro-babel-transformer": "0.82.4", "metro-cache": "0.82.4", "metro-cache-key": "0.82.4", "metro-config": "0.82.4", "metro-core": "0.82.4", "metro-file-map": "0.82.4", "metro-resolver": "0.82.4", "metro-runtime": "0.82.4", "metro-source-map": "0.82.4", "metro-symbolicate": "0.82.4", "metro-transform-plugins": "0.82.4", "metro-transform-worker": "0.82.4", "mime-types": "^2.1.27", "nullthrows": "^1.1.1", "serialize-error": "^2.1.0", "source-map": "^0.5.6", "throat": "^5.0.0", "ws": "^7.5.10", "yargs": "^17.6.2"}, "bin": {"metro": "src/cli.js"}, "engines": {"node": ">=18.18"}}, "node_modules/metro-babel-transformer": {"version": "0.82.4", "license": "MIT", "dependencies": {"@babel/core": "^7.25.2", "flow-enums-runtime": "^0.0.6", "hermes-parser": "0.28.1", "nullthrows": "^1.1.1"}, "engines": {"node": ">=18.18"}}, "node_modules/metro-babel-transformer/node_modules/hermes-estree": {"version": "0.28.1", "license": "MIT"}, "node_modules/metro-babel-transformer/node_modules/hermes-parser": {"version": "0.28.1", "license": "MIT", "dependencies": {"hermes-estree": "0.28.1"}}, "node_modules/metro-cache": {"version": "0.82.4", "license": "MIT", "dependencies": {"exponential-backoff": "^3.1.1", "flow-enums-runtime": "^0.0.6", "https-proxy-agent": "^7.0.5", "metro-core": "0.82.4"}, "engines": {"node": ">=18.18"}}, "node_modules/metro-cache-key": {"version": "0.82.4", "license": "MIT", "dependencies": {"flow-enums-runtime": "^0.0.6"}, "engines": {"node": ">=18.18"}}, "node_modules/metro-config": {"version": "0.82.4", "license": "MIT", "dependencies": {"connect": "^3.6.5", "cosmiconfig": "^5.0.5", "flow-enums-runtime": "^0.0.6", "jest-validate": "^29.7.0", "metro": "0.82.4", "metro-cache": "0.82.4", "metro-core": "0.82.4", "metro-runtime": "0.82.4"}, "engines": {"node": ">=18.18"}}, "node_modules/metro-core": {"version": "0.82.4", "license": "MIT", "dependencies": {"flow-enums-runtime": "^0.0.6", "lodash.throttle": "^4.1.1", "metro-resolver": "0.82.4"}, "engines": {"node": ">=18.18"}}, "node_modules/metro-file-map": {"version": "0.82.4", "license": "MIT", "dependencies": {"debug": "^4.4.0", "fb-watchman": "^2.0.0", "flow-enums-runtime": "^0.0.6", "graceful-fs": "^4.2.4", "invariant": "^2.2.4", "jest-worker": "^29.7.0", "micromatch": "^4.0.4", "nullthrows": "^1.1.1", "walker": "^1.0.7"}, "engines": {"node": ">=18.18"}}, "node_modules/metro-minify-terser": {"version": "0.82.4", "license": "MIT", "dependencies": {"flow-enums-runtime": "^0.0.6", "terser": "^5.15.0"}, "engines": {"node": ">=18.18"}}, "node_modules/metro-resolver": {"version": "0.82.4", "license": "MIT", "dependencies": {"flow-enums-runtime": "^0.0.6"}, "engines": {"node": ">=18.18"}}, "node_modules/metro-runtime": {"version": "0.82.4", "license": "MIT", "dependencies": {"@babel/runtime": "^7.25.0", "flow-enums-runtime": "^0.0.6"}, "engines": {"node": ">=18.18"}}, "node_modules/metro-source-map": {"version": "0.82.4", "license": "MIT", "dependencies": {"@babel/traverse": "^7.25.3", "@babel/traverse--for-generate-function-map": "npm:@babel/traverse@^7.25.3", "@babel/types": "^7.25.2", "flow-enums-runtime": "^0.0.6", "invariant": "^2.2.4", "metro-symbolicate": "0.82.4", "nullthrows": "^1.1.1", "ob1": "0.82.4", "source-map": "^0.5.6", "vlq": "^1.0.0"}, "engines": {"node": ">=18.18"}}, "node_modules/metro-symbolicate": {"version": "0.82.4", "license": "MIT", "dependencies": {"flow-enums-runtime": "^0.0.6", "invariant": "^2.2.4", "metro-source-map": "0.82.4", "nullthrows": "^1.1.1", "source-map": "^0.5.6", "vlq": "^1.0.0"}, "bin": {"metro-symbolicate": "src/index.js"}, "engines": {"node": ">=18.18"}}, "node_modules/metro-transform-plugins": {"version": "0.82.4", "license": "MIT", "dependencies": {"@babel/core": "^7.25.2", "@babel/generator": "^7.25.0", "@babel/template": "^7.25.0", "@babel/traverse": "^7.25.3", "flow-enums-runtime": "^0.0.6", "nullthrows": "^1.1.1"}, "engines": {"node": ">=18.18"}}, "node_modules/metro-transform-worker": {"version": "0.82.4", "license": "MIT", "dependencies": {"@babel/core": "^7.25.2", "@babel/generator": "^7.25.0", "@babel/parser": "^7.25.3", "@babel/types": "^7.25.2", "flow-enums-runtime": "^0.0.6", "metro": "0.82.4", "metro-babel-transformer": "0.82.4", "metro-cache": "0.82.4", "metro-cache-key": "0.82.4", "metro-minify-terser": "0.82.4", "metro-source-map": "0.82.4", "metro-transform-plugins": "0.82.4", "nullthrows": "^1.1.1"}, "engines": {"node": ">=18.18"}}, "node_modules/metro/node_modules/@babel/code-frame": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/metro/node_modules/ci-info": {"version": "2.0.0", "license": "MIT"}, "node_modules/metro/node_modules/hermes-estree": {"version": "0.28.1", "license": "MIT"}, "node_modules/metro/node_modules/hermes-parser": {"version": "0.28.1", "license": "MIT", "dependencies": {"hermes-estree": "0.28.1"}}, "node_modules/metro/node_modules/ws": {"version": "7.5.10", "license": "MIT", "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/micromatch": {"version": "4.0.8", "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime": {"version": "1.6.0", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/minimatch": {"version": "3.1.2", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimatch/node_modules/brace-expansion": {"version": "1.1.12", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/minimist": {"version": "1.2.8", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minipass": {"version": "7.1.2", "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/minizlib": {"version": "3.0.2", "license": "MIT", "dependencies": {"minipass": "^7.1.2"}, "engines": {"node": ">= 18"}}, "node_modules/mkdirp": {"version": "1.0.4", "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/moment": {"version": "2.30.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": "*"}}, "node_modules/moti": {"version": "0.30.0", "license": "MIT", "dependencies": {"framer-motion": "^6.5.1"}, "peerDependencies": {"react-native-reanimated": "*"}}, "node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/multipasta": {"version": "0.2.5", "dev": true, "license": "MIT"}, "node_modules/mute-stream": {"version": "0.0.8", "dev": true, "license": "ISC"}, "node_modules/mv": {"version": "2.1.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"mkdirp": "~0.5.1", "ncp": "~2.0.0", "rimraf": "~2.4.0"}, "engines": {"node": ">=0.8.0"}}, "node_modules/mv/node_modules/glob": {"version": "6.0.4", "dev": true, "license": "ISC", "optional": true, "dependencies": {"inflight": "^1.0.4", "inherits": "2", "minimatch": "2 || 3", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/mv/node_modules/mkdirp": {"version": "0.5.6", "dev": true, "license": "MIT", "optional": true, "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/mv/node_modules/rimraf": {"version": "2.4.5", "dev": true, "license": "ISC", "optional": true, "dependencies": {"glob": "^6.0.1"}, "bin": {"rimraf": "bin.js"}}, "node_modules/mz": {"version": "2.7.0", "license": "MIT", "dependencies": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}}, "node_modules/nan": {"version": "2.22.2", "dev": true, "license": "MIT", "optional": true}, "node_modules/nanoid": {"version": "3.3.11", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/natural-orderby": {"version": "2.0.3", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/ncp": {"version": "2.0.0", "dev": true, "license": "MIT", "optional": true, "bin": {"ncp": "bin/ncp"}}, "node_modules/negotiator": {"version": "0.6.3", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/nested-error-stacks": {"version": "2.0.1", "license": "MIT"}, "node_modules/node-fetch": {"version": "2.6.7", "dev": true, "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/node-forge": {"version": "1.3.1", "license": "(BSD-3-<PERSON><PERSON> OR GPL-2.0)", "engines": {"node": ">= 6.13.0"}}, "node_modules/node-int64": {"version": "0.4.0", "license": "MIT"}, "node_modules/node-releases": {"version": "2.0.19", "license": "MIT"}, "node_modules/node-rsa": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"asn1": "^0.2.4"}}, "node_modules/node-stream-zip": {"version": "1.15.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/antelle"}}, "node_modules/normalize-path": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm-package-arg": {"version": "11.0.3", "license": "ISC", "dependencies": {"hosted-git-info": "^7.0.0", "proc-log": "^4.0.0", "semver": "^7.3.5", "validate-npm-package-name": "^5.0.0"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "node_modules/npm-package-arg/node_modules/lru-cache": {"version": "6.0.0", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/npm-package-arg/node_modules/semver": {"version": "7.5.4", "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/nth-check": {"version": "2.1.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0"}, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}}, "node_modules/nullthrows": {"version": "1.1.1", "license": "MIT"}, "node_modules/ob1": {"version": "0.82.4", "license": "MIT", "dependencies": {"flow-enums-runtime": "^0.0.6"}, "engines": {"node": ">=18.18"}}, "node_modules/object-assign": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-treeify": {"version": "1.1.33", "dev": true, "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/on-finished": {"version": "2.4.1", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/on-headers": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "2.0.1", "license": "MIT", "dependencies": {"mimic-fn": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/open": {"version": "7.4.2", "license": "MIT", "dependencies": {"is-docker": "^2.0.0", "is-wsl": "^2.1.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ora": {"version": "3.4.0", "license": "MIT", "dependencies": {"chalk": "^2.4.2", "cli-cursor": "^2.1.0", "cli-spinners": "^2.0.0", "log-symbols": "^2.2.0", "strip-ansi": "^5.2.0", "wcwidth": "^1.0.1"}, "engines": {"node": ">=6"}}, "node_modules/ora/node_modules/ansi-regex": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/ora/node_modules/ansi-styles": {"version": "3.2.1", "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/ora/node_modules/chalk": {"version": "2.4.2", "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/ora/node_modules/color-convert": {"version": "1.9.3", "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/ora/node_modules/color-name": {"version": "1.1.3", "license": "MIT"}, "node_modules/ora/node_modules/escape-string-regexp": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/ora/node_modules/has-flag": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ora/node_modules/log-symbols": {"version": "2.2.0", "license": "MIT", "dependencies": {"chalk": "^2.0.1"}, "engines": {"node": ">=4"}}, "node_modules/ora/node_modules/strip-ansi": {"version": "5.2.0", "license": "MIT", "dependencies": {"ansi-regex": "^4.1.0"}, "engines": {"node": ">=6"}}, "node_modules/ora/node_modules/supports-color": {"version": "5.5.0", "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/p-limit": {"version": "3.1.0", "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "4.1.0", "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/p-locate/node_modules/p-limit": {"version": "2.3.0", "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-try": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/package-json-from-dist": {"version": "1.0.1", "license": "BlueOak-1.0.0"}, "node_modules/parse-json": {"version": "4.0.0", "license": "MIT", "dependencies": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}, "engines": {"node": ">=4"}}, "node_modules/parse-png": {"version": "2.1.0", "license": "MIT", "dependencies": {"pngjs": "^3.3.0"}, "engines": {"node": ">=10"}}, "node_modules/parseurl": {"version": "1.3.3", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/password-prompt": {"version": "1.1.3", "dev": true, "license": "0BSD", "dependencies": {"ansi-escapes": "^4.3.2", "cross-spawn": "^7.0.3"}}, "node_modules/path-exists": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "license": "MIT"}, "node_modules/path-scurry": {"version": "1.11.1", "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/path-type": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/picocolors": {"version": "1.1.1", "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pirates": {"version": "4.0.7", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/pkg-dir": {"version": "4.2.0", "dev": true, "license": "MIT", "dependencies": {"find-up": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/pkg-dir/node_modules/find-up": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/plist": {"version": "3.1.0", "license": "MIT", "dependencies": {"@xmldom/xmldom": "^0.8.8", "base64-js": "^1.5.1", "xmlbuilder": "^15.1.1"}, "engines": {"node": ">=10.4.0"}}, "node_modules/pngjs": {"version": "3.4.0", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/popmotion": {"version": "11.0.3", "license": "MIT", "dependencies": {"framesync": "6.0.1", "hey-listen": "^1.0.8", "style-value-types": "5.0.0", "tslib": "^2.1.0"}}, "node_modules/postcss": {"version": "8.4.49", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/postcss-value-parser": {"version": "4.2.0", "license": "MIT"}, "node_modules/pretty-bytes": {"version": "5.6.0", "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/pretty-format": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.3", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/pretty-format/node_modules/ansi-styles": {"version": "5.2.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/proc-log": {"version": "4.2.0", "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/progress": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/promise": {"version": "8.3.0", "license": "MIT", "dependencies": {"asap": "~2.0.6"}}, "node_modules/promise-limit": {"version": "2.7.0", "dev": true, "license": "ISC"}, "node_modules/promise-retry": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"err-code": "^2.0.2", "retry": "^0.12.0"}, "engines": {"node": ">=10"}}, "node_modules/prompts": {"version": "2.4.2", "license": "MIT", "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}, "engines": {"node": ">= 6"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "license": "MIT"}, "node_modules/punycode": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/qrcode-terminal": {"version": "0.11.0", "bin": {"qrcode-terminal": "bin/qrcode-terminal.js"}}, "node_modules/query-string": {"version": "7.1.3", "license": "MIT", "dependencies": {"decode-uri-component": "^0.2.2", "filter-obj": "^1.1.0", "split-on-first": "^1.0.0", "strict-uri-encode": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/queue": {"version": "6.0.2", "license": "MIT", "dependencies": {"inherits": "~2.0.3"}}, "node_modules/queue-microtask": {"version": "1.2.3", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/range-parser": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/rc": {"version": "1.2.8", "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "dependencies": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "cli.js"}}, "node_modules/react": {"version": "19.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/react-devtools-core": {"version": "6.1.3", "license": "MIT", "dependencies": {"shell-quote": "^1.6.1", "ws": "^7"}}, "node_modules/react-devtools-core/node_modules/ws": {"version": "7.5.10", "license": "MIT", "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/react-dom": {"version": "19.0.0", "license": "MIT", "dependencies": {"scheduler": "^0.25.0"}, "peerDependencies": {"react": "^19.0.0"}}, "node_modules/react-freeze": {"version": "1.0.4", "license": "MIT", "engines": {"node": ">=10"}, "peerDependencies": {"react": ">=17.0.0"}}, "node_modules/react-is": {"version": "18.3.1", "license": "MIT"}, "node_modules/react-native": {"version": "0.79.4", "license": "MIT", "dependencies": {"@jest/create-cache-key-function": "^29.7.0", "@react-native/assets-registry": "0.79.4", "@react-native/codegen": "0.79.4", "@react-native/community-cli-plugin": "0.79.4", "@react-native/gradle-plugin": "0.79.4", "@react-native/js-polyfills": "0.79.4", "@react-native/normalize-colors": "0.79.4", "@react-native/virtualized-lists": "0.79.4", "abort-controller": "^3.0.0", "anser": "^1.4.9", "ansi-regex": "^5.0.0", "babel-jest": "^29.7.0", "babel-plugin-syntax-hermes-parser": "0.25.1", "base64-js": "^1.5.1", "chalk": "^4.0.0", "commander": "^12.0.0", "event-target-shim": "^5.0.1", "flow-enums-runtime": "^0.0.6", "glob": "^7.1.1", "invariant": "^2.2.4", "jest-environment-node": "^29.7.0", "memoize-one": "^5.0.0", "metro-runtime": "^0.82.0", "metro-source-map": "^0.82.0", "nullthrows": "^1.1.1", "pretty-format": "^29.7.0", "promise": "^8.3.0", "react-devtools-core": "^6.1.1", "react-refresh": "^0.14.0", "regenerator-runtime": "^0.13.2", "scheduler": "0.25.0", "semver": "^7.1.3", "stacktrace-parser": "^0.1.10", "whatwg-fetch": "^3.0.0", "ws": "^6.2.3", "yargs": "^17.6.2"}, "bin": {"react-native": "cli.js"}, "engines": {"node": ">=18"}, "peerDependencies": {"@types/react": "^19.0.0", "react": "^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/react-native-edge-to-edge": {"version": "1.6.0", "license": "MIT", "peerDependencies": {"react": "*", "react-native": "*"}}, "node_modules/react-native-gesture-handler": {"version": "2.24.0", "license": "MIT", "dependencies": {"@egjs/hammerjs": "^2.0.17", "hoist-non-react-statics": "^3.3.0", "invariant": "^2.2.4"}, "peerDependencies": {"react": "*", "react-native": "*"}}, "node_modules/react-native-is-edge-to-edge": {"version": "1.1.7", "license": "MIT", "peerDependencies": {"react": "*", "react-native": "*"}}, "node_modules/react-native-reanimated": {"version": "3.17.5", "license": "MIT", "dependencies": {"@babel/plugin-transform-arrow-functions": "^7.0.0-0", "@babel/plugin-transform-class-properties": "^7.0.0-0", "@babel/plugin-transform-classes": "^7.0.0-0", "@babel/plugin-transform-nullish-coalescing-operator": "^7.0.0-0", "@babel/plugin-transform-optional-chaining": "^7.0.0-0", "@babel/plugin-transform-shorthand-properties": "^7.0.0-0", "@babel/plugin-transform-template-literals": "^7.0.0-0", "@babel/plugin-transform-unicode-regex": "^7.0.0-0", "@babel/preset-typescript": "^7.16.7", "convert-source-map": "^2.0.0", "invariant": "^2.2.4", "react-native-is-edge-to-edge": "1.1.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0", "react": "*", "react-native": "*"}}, "node_modules/react-native-safe-area-context": {"version": "5.4.0", "license": "MIT", "peerDependencies": {"react": "*", "react-native": "*"}}, "node_modules/react-native-screens": {"version": "4.11.1", "license": "MIT", "dependencies": {"react-freeze": "^1.0.0", "react-native-is-edge-to-edge": "^1.1.7", "warn-once": "^0.1.0"}, "peerDependencies": {"react": "*", "react-native": "*"}}, "node_modules/react-native-svg": {"version": "15.11.2", "license": "MIT", "dependencies": {"css-select": "^5.1.0", "css-tree": "^1.1.3", "warn-once": "0.1.1"}, "peerDependencies": {"react": "*", "react-native": "*"}}, "node_modules/react-native-web": {"version": "0.20.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.6", "@react-native/normalize-colors": "^0.74.1", "fbjs": "^3.0.4", "inline-style-prefixer": "^7.0.1", "memoize-one": "^6.0.0", "nullthrows": "^1.1.1", "postcss-value-parser": "^4.2.0", "styleq": "^0.1.3"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}}, "node_modules/react-native-web/node_modules/@react-native/normalize-colors": {"version": "0.74.89", "license": "MIT"}, "node_modules/react-native-web/node_modules/memoize-one": {"version": "6.0.0", "license": "MIT"}, "node_modules/react-native/node_modules/commander": {"version": "12.1.0", "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/react-native/node_modules/glob": {"version": "7.2.3", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/react-refresh": {"version": "0.14.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/redeyed": {"version": "2.1.1", "dev": true, "license": "MIT", "dependencies": {"esprima": "~4.0.0"}}, "node_modules/regenerate": {"version": "1.4.2", "license": "MIT"}, "node_modules/regenerate-unicode-properties": {"version": "10.2.0", "license": "MIT", "dependencies": {"regenerate": "^1.4.2"}, "engines": {"node": ">=4"}}, "node_modules/regenerator-runtime": {"version": "0.13.11", "license": "MIT"}, "node_modules/regexpu-core": {"version": "6.2.0", "license": "MIT", "dependencies": {"regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.2.0", "regjsgen": "^0.8.0", "regjsparser": "^0.12.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}, "engines": {"node": ">=4"}}, "node_modules/regjsgen": {"version": "0.8.0", "license": "MIT"}, "node_modules/regjsparser": {"version": "0.12.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"jsesc": "~3.0.2"}, "bin": {"regjsparser": "bin/parser"}}, "node_modules/regjsparser/node_modules/jsesc": {"version": "3.0.2", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/remove-trailing-slash": {"version": "0.1.1", "dev": true, "license": "MIT"}, "node_modules/require-directory": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-from-string": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/requireg": {"version": "0.2.2", "dependencies": {"nested-error-stacks": "~2.0.1", "rc": "~1.2.7", "resolve": "~1.7.1"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/requireg/node_modules/resolve": {"version": "1.7.1", "license": "MIT", "dependencies": {"path-parse": "^1.0.5"}}, "node_modules/resolve": {"version": "1.22.10", "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-from": {"version": "5.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/resolve-workspace-root": {"version": "2.0.0", "license": "MIT"}, "node_modules/resolve.exports": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/restore-cursor": {"version": "2.0.0", "license": "MIT", "dependencies": {"onetime": "^2.0.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=4"}}, "node_modules/retry": {"version": "0.12.0", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/reusify": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rimraf": {"version": "3.0.2", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/rimraf/node_modules/glob": {"version": "7.2.3", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/run-parallel": {"version": "1.2.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safe-json-stringify": {"version": "1.2.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/safer-buffer": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/sax": {"version": "1.4.1", "license": "ISC"}, "node_modules/scheduler": {"version": "0.25.0", "license": "MIT"}, "node_modules/semver": {"version": "7.7.2", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/send": {"version": "0.19.0", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/send/node_modules/debug/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/serialize-error": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/serve-static": {"version": "1.16.2", "license": "MIT", "dependencies": {"encodeurl": "~2.0.0", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.19.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/serve-static/node_modules/encodeurl": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/set-interval-async": {"version": "3.0.3", "dev": true, "license": "MIT", "engines": {"node": ">= 14.0.0"}}, "node_modules/setimmediate": {"version": "1.0.5", "license": "MIT"}, "node_modules/setprototypeof": {"version": "1.2.0", "license": "ISC"}, "node_modules/shebang-command": {"version": "2.0.0", "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/shell-quote": {"version": "1.8.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "3.0.7", "license": "ISC"}, "node_modules/simple-plist": {"version": "1.3.1", "license": "MIT", "dependencies": {"bplist-creator": "0.1.0", "bplist-parser": "0.3.1", "plist": "^3.0.5"}}, "node_modules/simple-plist/node_modules/bplist-parser": {"version": "0.3.1", "license": "MIT", "dependencies": {"big-integer": "1.6.x"}, "engines": {"node": ">= 5.10.0"}}, "node_modules/simple-swizzle": {"version": "0.2.2", "license": "MIT", "dependencies": {"is-arrayish": "^0.3.1"}}, "node_modules/simple-swizzle/node_modules/is-arrayish": {"version": "0.3.2", "license": "MIT"}, "node_modules/sisteransi": {"version": "1.0.5", "license": "MIT"}, "node_modules/slash": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/slice-ansi": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/slugify": {"version": "1.6.6", "license": "MIT", "engines": {"node": ">=8.0.0"}}, "node_modules/source-map": {"version": "0.5.7", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-js": {"version": "1.2.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/source-map-support/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/split": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"through": "2"}, "engines": {"node": "*"}}, "node_modules/split-on-first": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/sprintf-js": {"version": "1.0.3", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/stack-utils": {"version": "2.0.6", "license": "MIT", "dependencies": {"escape-string-regexp": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/stack-utils/node_modules/escape-string-regexp": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/stackframe": {"version": "1.3.4", "license": "MIT"}, "node_modules/stacktrace-parser": {"version": "0.1.11", "license": "MIT", "dependencies": {"type-fest": "^0.7.1"}, "engines": {"node": ">=6"}}, "node_modules/statuses": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/stream-buffers": {"version": "2.2.0", "license": "Unlicense", "engines": {"node": ">= 0.10.0"}}, "node_modules/streamx": {"version": "2.22.1", "dev": true, "license": "MIT", "dependencies": {"fast-fifo": "^1.3.2", "text-decoder": "^1.1.0"}, "optionalDependencies": {"bare-events": "^2.2.0"}}, "node_modules/strict-uri-encode": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/string-width": {"version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string-width-cjs": {"name": "string-width", "version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi-cjs": {"name": "strip-ansi", "version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-json-comments": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/structured-headers": {"version": "0.4.1", "license": "MIT"}, "node_modules/style-value-types": {"version": "5.0.0", "license": "MIT", "dependencies": {"hey-listen": "^1.0.8", "tslib": "^2.1.0"}}, "node_modules/styleq": {"version": "0.1.3", "license": "MIT"}, "node_modules/sucrase": {"version": "3.35.0", "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.2", "commander": "^4.0.0", "glob": "^10.3.10", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/sucrase/node_modules/commander": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/sudo-prompt": {"version": "9.1.1", "dev": true, "license": "MIT"}, "node_modules/supports-color": {"version": "8.1.1", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/supports-hyperlinks": {"version": "2.3.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-hyperlinks/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/tar": {"version": "7.4.3", "license": "ISC", "dependencies": {"@isaacs/fs-minipass": "^4.0.0", "chownr": "^3.0.0", "minipass": "^7.1.2", "minizlib": "^3.0.1", "mkdirp": "^3.0.1", "yallist": "^5.0.0"}, "engines": {"node": ">=18"}}, "node_modules/tar-stream": {"version": "3.1.7", "dev": true, "license": "MIT", "dependencies": {"b4a": "^1.6.4", "fast-fifo": "^1.2.0", "streamx": "^2.15.0"}}, "node_modules/tar/node_modules/mkdirp": {"version": "3.0.1", "license": "MIT", "bin": {"mkdirp": "dist/cjs/src/bin.js"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/tar/node_modules/yallist": {"version": "5.0.0", "license": "BlueOak-1.0.0", "engines": {"node": ">=18"}}, "node_modules/temp-dir": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/terminal-link": {"version": "2.1.1", "license": "MIT", "dependencies": {"ansi-escapes": "^4.2.1", "supports-hyperlinks": "^2.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/terser": {"version": "5.43.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.14.0", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "node_modules/test-exclude": {"version": "6.0.0", "license": "ISC", "dependencies": {"@istanbuljs/schema": "^0.1.2", "glob": "^7.1.4", "minimatch": "^3.0.4"}, "engines": {"node": ">=8"}}, "node_modules/test-exclude/node_modules/glob": {"version": "7.2.3", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/text-decoder": {"version": "1.2.3", "dev": true, "license": "Apache-2.0", "dependencies": {"b4a": "^1.6.4"}}, "node_modules/thenify": {"version": "3.3.1", "license": "MIT", "dependencies": {"any-promise": "^1.0.0"}}, "node_modules/thenify-all": {"version": "1.6.0", "license": "MIT", "dependencies": {"thenify": ">= 3.1.0 < 4"}, "engines": {"node": ">=0.8"}}, "node_modules/this-file": {"version": "2.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=14.15.0"}}, "node_modules/throat": {"version": "5.0.0", "license": "MIT"}, "node_modules/through": {"version": "2.3.8", "dev": true, "license": "MIT"}, "node_modules/tmpl": {"version": "1.0.5", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/to-regex-range": {"version": "5.0.1", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/tr46": {"version": "0.0.3", "license": "MIT"}, "node_modules/ts-interface-checker": {"version": "0.1.13", "license": "Apache-2.0"}, "node_modules/ts-node": {"version": "10.9.2", "dev": true, "license": "MIT", "dependencies": {"@cspotcode/source-map-support": "^0.8.0", "@tsconfig/node10": "^1.0.7", "@tsconfig/node12": "^1.0.7", "@tsconfig/node14": "^1.0.0", "@tsconfig/node16": "^1.0.2", "acorn": "^8.4.1", "acorn-walk": "^8.1.1", "arg": "^4.1.0", "create-require": "^1.1.0", "diff": "^4.0.1", "make-error": "^1.1.1", "v8-compile-cache-lib": "^3.0.1", "yn": "3.1.1"}, "bin": {"ts-node": "dist/bin.js", "ts-node-cwd": "dist/bin-cwd.js", "ts-node-esm": "dist/bin-esm.js", "ts-node-script": "dist/bin-script.js", "ts-node-transpile-only": "dist/bin-transpile.js", "ts-script": "dist/bin-script-deprecated.js"}, "peerDependencies": {"@swc/core": ">=1.2.50", "@swc/wasm": ">=1.2.50", "@types/node": "*", "typescript": ">=2.7"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "@swc/wasm": {"optional": true}}}, "node_modules/ts-node/node_modules/arg": {"version": "4.1.3", "dev": true, "license": "MIT"}, "node_modules/tslib": {"version": "2.8.1", "license": "0BSD"}, "node_modules/tunnel-agent": {"version": "0.6.0", "dev": true, "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/turndown": {"version": "7.1.2", "dev": true, "license": "MIT", "dependencies": {"domino": "^2.1.6"}}, "node_modules/type-detect": {"version": "4.0.8", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/type-fest": {"version": "0.7.1", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=8"}}, "node_modules/typescript": {"version": "5.8.3", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/ua-parser-js": {"version": "1.0.40", "funding": [{"type": "opencollective", "url": "https://opencollective.com/ua-parser-js"}, {"type": "paypal", "url": "https://paypal.me/faisalman"}, {"type": "github", "url": "https://github.com/sponsors/faisalman"}], "license": "MIT", "bin": {"ua-parser-js": "script/cli.js"}, "engines": {"node": "*"}}, "node_modules/undici": {"version": "6.21.3", "license": "MIT", "engines": {"node": ">=18.17"}}, "node_modules/undici-types": {"version": "7.8.0", "license": "MIT"}, "node_modules/unicode-canonical-property-names-ecmascript": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unicode-match-property-ecmascript": {"version": "2.0.0", "license": "MIT", "dependencies": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/unicode-match-property-value-ecmascript": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unicode-property-aliases-ecmascript": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unique-string": {"version": "2.0.0", "license": "MIT", "dependencies": {"crypto-random-string": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/unpipe": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/untildify": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/update-browserslist-db": {"version": "1.1.3", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/uri-js": {"version": "4.4.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/use-latest-callback": {"version": "0.2.4", "license": "MIT", "peerDependencies": {"react": ">=16.8"}}, "node_modules/use-sync-external-store": {"version": "1.5.0", "license": "MIT", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/utils-merge": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "8.3.2", "dev": true, "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/v8-compile-cache-lib": {"version": "3.0.1", "dev": true, "license": "MIT"}, "node_modules/validate-npm-package-name": {"version": "5.0.1", "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/vary": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/vlq": {"version": "1.0.1", "license": "MIT"}, "node_modules/walker": {"version": "1.0.8", "license": "Apache-2.0", "dependencies": {"makeerror": "1.0.12"}}, "node_modules/warn-once": {"version": "0.1.1", "license": "MIT"}, "node_modules/wcwidth": {"version": "1.0.1", "license": "MIT", "dependencies": {"defaults": "^1.0.3"}}, "node_modules/webidl-conversions": {"version": "5.0.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "node_modules/whatwg-fetch": {"version": "3.6.20", "license": "MIT"}, "node_modules/whatwg-url": {"version": "5.0.0", "license": "MIT", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/whatwg-url-without-unicode": {"version": "8.0.0-3", "license": "MIT", "dependencies": {"buffer": "^5.4.3", "punycode": "^2.1.1", "webidl-conversions": "^5.0.0"}, "engines": {"node": ">=10"}}, "node_modules/whatwg-url/node_modules/webidl-conversions": {"version": "3.0.1", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/which": {"version": "2.0.2", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/widest-line": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"string-width": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/wonka": {"version": "6.3.5", "license": "MIT"}, "node_modules/wordwrap": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/wrap-ansi": {"version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs": {"name": "wrap-ansi", "version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "license": "ISC"}, "node_modules/write-file-atomic": {"version": "2.4.3", "dev": true, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}}, "node_modules/ws": {"version": "6.2.3", "license": "MIT", "dependencies": {"async-limiter": "~1.0.0"}}, "node_modules/xcode": {"version": "3.0.1", "license": "Apache-2.0", "dependencies": {"simple-plist": "^1.1.0", "uuid": "^7.0.3"}, "engines": {"node": ">=10.0.0"}}, "node_modules/xcode/node_modules/uuid": {"version": "7.0.3", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/xml2js": {"version": "0.6.0", "license": "MIT", "dependencies": {"sax": ">=0.6.0", "xmlbuilder": "~11.0.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/xml2js/node_modules/xmlbuilder": {"version": "11.0.1", "license": "MIT", "engines": {"node": ">=4.0"}}, "node_modules/xmlbuilder": {"version": "15.1.1", "license": "MIT", "engines": {"node": ">=8.0"}}, "node_modules/y18n": {"version": "5.0.8", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "4.0.0", "license": "ISC"}, "node_modules/yaml": {"version": "2.6.0", "dev": true, "license": "ISC", "bin": {"yaml": "bin.mjs"}, "engines": {"node": ">= 14"}}, "node_modules/yargs": {"version": "17.7.2", "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/yn": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/yocto-queue": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/zod": {"version": "3.25.67", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/colinhacks"}}, "node_modules/zustand": {"version": "5.0.6", "license": "MIT", "engines": {"node": ">=12.20.0"}, "peerDependencies": {"@types/react": ">=18.0.0", "immer": ">=9.0.6", "react": ">=18.0.0", "use-sync-external-store": ">=1.2.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "immer": {"optional": true}, "react": {"optional": true}, "use-sync-external-store": {"optional": true}}}}, "dependencies": {"@0no-co/graphql.web": {"version": "1.1.2", "requires": {}}, "@ampproject/remapping": {"version": "2.3.0", "requires": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}}, "@babel/code-frame": {"version": "7.10.4", "requires": {"@babel/highlight": "^7.10.4"}}, "@babel/compat-data": {"version": "7.27.7"}, "@babel/core": {"version": "7.27.7", "requires": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.5", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.27.3", "@babel/helpers": "^7.27.6", "@babel/parser": "^7.27.7", "@babel/template": "^7.27.2", "@babel/traverse": "^7.27.7", "@babel/types": "^7.27.7", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "semver": {"version": "6.3.1"}}}, "@babel/generator": {"version": "7.27.5", "requires": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}}, "@babel/helper-annotate-as-pure": {"version": "7.27.3", "requires": {"@babel/types": "^7.27.3"}}, "@babel/helper-compilation-targets": {"version": "7.27.2", "requires": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "dependencies": {"lru-cache": {"version": "5.1.1", "requires": {"yallist": "^3.0.2"}}, "semver": {"version": "6.3.1"}, "yallist": {"version": "3.1.1"}}}, "@babel/helper-create-class-features-plugin": {"version": "7.27.1", "requires": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/traverse": "^7.27.1", "semver": "^6.3.1"}, "dependencies": {"semver": {"version": "6.3.1"}}}, "@babel/helper-create-regexp-features-plugin": {"version": "7.27.1", "requires": {"@babel/helper-annotate-as-pure": "^7.27.1", "regexpu-core": "^6.2.0", "semver": "^6.3.1"}, "dependencies": {"semver": {"version": "6.3.1"}}}, "@babel/helper-define-polyfill-provider": {"version": "0.6.5", "requires": {"@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "debug": "^4.4.1", "lodash.debounce": "^4.0.8", "resolve": "^1.22.10"}}, "@babel/helper-member-expression-to-functions": {"version": "7.27.1", "requires": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}}, "@babel/helper-module-imports": {"version": "7.27.1", "requires": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}}, "@babel/helper-module-transforms": {"version": "7.27.3", "requires": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}}, "@babel/helper-optimise-call-expression": {"version": "7.27.1", "requires": {"@babel/types": "^7.27.1"}}, "@babel/helper-plugin-utils": {"version": "7.27.1"}, "@babel/helper-remap-async-to-generator": {"version": "7.27.1", "requires": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-wrap-function": "^7.27.1", "@babel/traverse": "^7.27.1"}}, "@babel/helper-replace-supers": {"version": "7.27.1", "requires": {"@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/traverse": "^7.27.1"}}, "@babel/helper-skip-transparent-expression-wrappers": {"version": "7.27.1", "requires": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}}, "@babel/helper-string-parser": {"version": "7.27.1"}, "@babel/helper-validator-identifier": {"version": "7.27.1"}, "@babel/helper-validator-option": {"version": "7.27.1"}, "@babel/helper-wrap-function": {"version": "7.27.1", "requires": {"@babel/template": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}}, "@babel/helpers": {"version": "7.27.6", "requires": {"@babel/template": "^7.27.2", "@babel/types": "^7.27.6"}}, "@babel/highlight": {"version": "7.25.9", "requires": {"@babel/helper-validator-identifier": "^7.25.9", "chalk": "^2.4.2", "js-tokens": "^4.0.0", "picocolors": "^1.0.0"}, "dependencies": {"ansi-styles": {"version": "3.2.1", "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.2", "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "color-convert": {"version": "1.9.3", "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3"}, "escape-string-regexp": {"version": "1.0.5"}, "has-flag": {"version": "3.0.0"}, "supports-color": {"version": "5.5.0", "requires": {"has-flag": "^3.0.0"}}}}, "@babel/parser": {"version": "7.27.7", "requires": {"@babel/types": "^7.27.7"}}, "@babel/plugin-proposal-decorators": {"version": "7.27.1", "requires": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-syntax-decorators": "^7.27.1"}}, "@babel/plugin-proposal-export-default-from": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-syntax-async-generators": {"version": "7.8.4", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-bigint": {"version": "7.8.3", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-class-properties": {"version": "7.12.13", "requires": {"@babel/helper-plugin-utils": "^7.12.13"}}, "@babel/plugin-syntax-class-static-block": {"version": "7.14.5", "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-syntax-decorators": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-syntax-dynamic-import": {"version": "7.8.3", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-export-default-from": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-syntax-flow": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-syntax-import-attributes": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-syntax-import-meta": {"version": "7.10.4", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-json-strings": {"version": "7.8.3", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-jsx": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-syntax-logical-assignment-operators": {"version": "7.10.4", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-private-property-in-object": {"version": "7.14.5", "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-syntax-top-level-await": {"version": "7.14.5", "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-syntax-typescript": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-arrow-functions": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-async-generator-functions": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-remap-async-to-generator": "^7.27.1", "@babel/traverse": "^7.27.1"}}, "@babel/plugin-transform-async-to-generator": {"version": "7.27.1", "requires": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-remap-async-to-generator": "^7.27.1"}}, "@babel/plugin-transform-block-scoping": {"version": "7.27.5", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-class-properties": {"version": "7.27.1", "requires": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-classes": {"version": "7.27.7", "requires": {"@babel/helper-annotate-as-pure": "^7.27.3", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/traverse": "^7.27.7", "globals": "^11.1.0"}}, "@babel/plugin-transform-computed-properties": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/template": "^7.27.1"}}, "@babel/plugin-transform-destructuring": {"version": "7.27.7", "requires": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.27.7"}}, "@babel/plugin-transform-export-namespace-from": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-flow-strip-types": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-syntax-flow": "^7.27.1"}}, "@babel/plugin-transform-for-of": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}}, "@babel/plugin-transform-function-name": {"version": "7.27.1", "requires": {"@babel/helper-compilation-targets": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.27.1"}}, "@babel/plugin-transform-literals": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-logical-assignment-operators": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-modules-commonjs": {"version": "7.27.1", "requires": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-named-capturing-groups-regex": {"version": "7.27.1", "requires": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-nullish-coalescing-operator": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-numeric-separator": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-object-rest-spread": {"version": "7.27.7", "requires": {"@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.27.7", "@babel/plugin-transform-parameters": "^7.27.7", "@babel/traverse": "^7.27.7"}}, "@babel/plugin-transform-optional-catch-binding": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-optional-chaining": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}}, "@babel/plugin-transform-parameters": {"version": "7.27.7", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-private-methods": {"version": "7.27.1", "requires": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-private-property-in-object": {"version": "7.27.1", "requires": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-react-display-name": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-react-jsx": {"version": "7.27.1", "requires": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-syntax-jsx": "^7.27.1", "@babel/types": "^7.27.1"}}, "@babel/plugin-transform-react-jsx-development": {"version": "7.27.1", "requires": {"@babel/plugin-transform-react-jsx": "^7.27.1"}}, "@babel/plugin-transform-react-jsx-self": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-react-jsx-source": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-react-pure-annotations": {"version": "7.27.1", "requires": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-regenerator": {"version": "7.27.5", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-runtime": {"version": "7.27.4", "requires": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "babel-plugin-polyfill-corejs2": "^0.4.10", "babel-plugin-polyfill-corejs3": "^0.11.0", "babel-plugin-polyfill-regenerator": "^0.6.1", "semver": "^6.3.1"}, "dependencies": {"semver": {"version": "6.3.1"}}}, "@babel/plugin-transform-shorthand-properties": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-spread": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}}, "@babel/plugin-transform-sticky-regex": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-template-literals": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-typescript": {"version": "7.27.1", "requires": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/plugin-syntax-typescript": "^7.27.1"}}, "@babel/plugin-transform-unicode-regex": {"version": "7.27.1", "requires": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/preset-react": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-option": "^7.27.1", "@babel/plugin-transform-react-display-name": "^7.27.1", "@babel/plugin-transform-react-jsx": "^7.27.1", "@babel/plugin-transform-react-jsx-development": "^7.27.1", "@babel/plugin-transform-react-pure-annotations": "^7.27.1"}}, "@babel/preset-typescript": {"version": "7.27.1", "requires": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-option": "^7.27.1", "@babel/plugin-syntax-jsx": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-typescript": "^7.27.1"}}, "@babel/runtime": {"version": "7.27.6"}, "@babel/template": {"version": "7.27.2", "requires": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}}}, "@babel/traverse": {"version": "7.27.7", "requires": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.5", "@babel/parser": "^7.27.7", "@babel/template": "^7.27.2", "@babel/types": "^7.27.7", "debug": "^4.3.1", "globals": "^11.1.0"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}}}, "@babel/traverse--for-generate-function-map": {"version": "npm:@babel/traverse@7.27.7", "requires": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.5", "@babel/parser": "^7.27.7", "@babel/template": "^7.27.2", "@babel/types": "^7.27.7", "debug": "^4.3.1", "globals": "^11.1.0"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}}}, "@babel/types": {"version": "7.27.7", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}, "@cspotcode/source-map-support": {"version": "0.8.1", "dev": true, "requires": {"@jridgewell/trace-mapping": "0.3.9"}, "dependencies": {"@jridgewell/trace-mapping": {"version": "0.3.9", "dev": true, "requires": {"@jridgewell/resolve-uri": "^3.0.3", "@jridgewell/sourcemap-codec": "^1.4.10"}}}}, "@egjs/hammerjs": {"version": "2.0.17", "requires": {"@types/hammerjs": "^2.0.36"}}, "@emotion/is-prop-valid": {"version": "0.8.8", "optional": true, "requires": {"@emotion/memoize": "0.7.4"}}, "@emotion/memoize": {"version": "0.7.4", "optional": true}, "@expo/apple-utils": {"version": "2.1.12", "dev": true}, "@expo/bunyan": {"version": "4.0.1", "dev": true, "requires": {"uuid": "^8.0.0"}}, "@expo/cli": {"version": "0.24.15", "requires": {"@0no-co/graphql.web": "^1.0.8", "@babel/runtime": "^7.20.0", "@expo/code-signing-certificates": "^0.0.5", "@expo/config": "~11.0.10", "@expo/config-plugins": "~10.0.3", "@expo/devcert": "^1.1.2", "@expo/env": "~1.0.5", "@expo/image-utils": "^0.7.4", "@expo/json-file": "^9.1.4", "@expo/metro-config": "~0.20.15", "@expo/osascript": "^2.2.4", "@expo/package-manager": "^1.8.4", "@expo/plist": "^0.3.4", "@expo/prebuild-config": "^9.0.7", "@expo/spawn-async": "^1.7.2", "@expo/ws-tunnel": "^1.0.1", "@expo/xcpretty": "^4.3.0", "@react-native/dev-middleware": "0.79.4", "@urql/core": "^5.0.6", "@urql/exchange-retry": "^1.3.0", "accepts": "^1.3.8", "arg": "^5.0.2", "better-opn": "~3.0.2", "bplist-creator": "0.1.0", "bplist-parser": "^0.3.1", "chalk": "^4.0.0", "ci-info": "^3.3.0", "compression": "^1.7.4", "connect": "^3.7.0", "debug": "^4.3.4", "env-editor": "^0.4.1", "freeport-async": "^2.0.0", "getenv": "^2.0.0", "glob": "^10.4.2", "lan-network": "^0.1.6", "minimatch": "^9.0.0", "node-forge": "^1.3.1", "npm-package-arg": "^11.0.0", "ora": "^3.4.0", "picomatch": "^3.0.1", "pretty-bytes": "^5.6.0", "pretty-format": "^29.7.0", "progress": "^2.0.3", "prompts": "^2.3.2", "qrcode-terminal": "0.11.0", "require-from-string": "^2.0.2", "requireg": "^0.2.2", "resolve": "^1.22.2", "resolve-from": "^5.0.0", "resolve.exports": "^2.0.3", "semver": "^7.6.0", "send": "^0.19.0", "slugify": "^1.3.4", "source-map-support": "~0.5.21", "stacktrace-parser": "^0.1.10", "structured-headers": "^0.4.1", "tar": "^7.4.3", "terminal-link": "^2.1.1", "undici": "^6.18.2", "wrap-ansi": "^7.0.0", "ws": "^8.12.1"}, "dependencies": {"encodeurl": {"version": "2.0.0"}, "getenv": {"version": "2.0.0"}, "minimatch": {"version": "9.0.5", "requires": {"brace-expansion": "^2.0.1"}}, "picomatch": {"version": "3.0.1"}, "send": {"version": "0.19.1", "requires": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "requires": {"ms": "2.0.0"}, "dependencies": {"ms": {"version": "2.0.0"}}}}}, "ws": {"version": "8.18.2", "requires": {}}}}, "@expo/code-signing-certificates": {"version": "0.0.5", "requires": {"node-forge": "^1.2.1", "nullthrows": "^1.1.1"}}, "@expo/config": {"version": "11.0.10", "requires": {"@babel/code-frame": "~7.10.4", "@expo/config-plugins": "~10.0.2", "@expo/config-types": "^53.0.4", "@expo/json-file": "^9.1.4", "deepmerge": "^4.3.1", "getenv": "^1.0.0", "glob": "^10.4.2", "require-from-string": "^2.0.2", "resolve-from": "^5.0.0", "resolve-workspace-root": "^2.0.0", "semver": "^7.6.0", "slugify": "^1.3.4", "sucrase": "3.35.0"}}, "@expo/config-plugins": {"version": "10.0.3", "requires": {"@expo/config-types": "^53.0.4", "@expo/json-file": "~9.1.4", "@expo/plist": "^0.3.4", "@expo/sdk-runtime-versions": "^1.0.0", "chalk": "^4.1.2", "debug": "^4.3.5", "getenv": "^2.0.0", "glob": "^10.4.2", "resolve-from": "^5.0.0", "semver": "^7.5.4", "slash": "^3.0.0", "slugify": "^1.6.6", "xcode": "^3.0.1", "xml2js": "0.6.0"}, "dependencies": {"getenv": {"version": "2.0.0"}}}, "@expo/config-types": {"version": "53.0.4"}, "@expo/devcert": {"version": "1.2.0", "requires": {"@expo/sudo-prompt": "^9.3.1", "debug": "^3.1.0", "glob": "^10.4.2"}, "dependencies": {"debug": {"version": "3.2.7", "requires": {"ms": "^2.1.1"}}}}, "@expo/eas-build-job": {"version": "1.0.173", "dev": true, "requires": {"@expo/logger": "1.0.117", "joi": "^17.13.1", "semver": "^7.6.2", "zod": "^3.23.8"}}, "@expo/eas-json": {"version": "16.13.2", "dev": true, "requires": {"@babel/code-frame": "7.23.5", "@expo/eas-build-job": "1.0.173", "chalk": "4.1.2", "env-string": "1.0.1", "fs-extra": "11.2.0", "golden-fleece": "1.0.9", "joi": "17.11.0", "log-symbols": "4.1.0", "semver": "7.5.2", "terminal-link": "2.1.1", "tslib": "2.4.1"}, "dependencies": {"@babel/code-frame": {"version": "7.23.5", "dev": true, "requires": {"@babel/highlight": "^7.23.4", "chalk": "^2.4.2"}, "dependencies": {"chalk": {"version": "2.4.2", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}}}, "ansi-styles": {"version": "3.2.1", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "color-convert": {"version": "1.9.3", "dev": true, "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "dev": true}, "escape-string-regexp": {"version": "1.0.5", "dev": true}, "has-flag": {"version": "3.0.0", "dev": true}, "joi": {"version": "17.11.0", "dev": true, "requires": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}}, "lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.5.2", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "supports-color": {"version": "5.5.0", "dev": true, "requires": {"has-flag": "^3.0.0"}}, "tslib": {"version": "2.4.1", "dev": true}}}, "@expo/env": {"version": "1.0.5", "requires": {"chalk": "^4.0.0", "debug": "^4.3.4", "dotenv": "~16.4.5", "dotenv-expand": "~11.0.6", "getenv": "^1.0.0"}}, "@expo/fingerprint": {"version": "0.13.1", "requires": {"@expo/spawn-async": "^1.7.2", "arg": "^5.0.2", "chalk": "^4.1.2", "debug": "^4.3.4", "find-up": "^5.0.0", "getenv": "^2.0.0", "glob": "^10.4.2", "ignore": "^5.3.1", "minimatch": "^9.0.0", "p-limit": "^3.1.0", "resolve-from": "^5.0.0", "semver": "^7.6.0"}, "dependencies": {"getenv": {"version": "2.0.0"}, "ignore": {"version": "5.3.2"}, "minimatch": {"version": "9.0.5", "requires": {"brace-expansion": "^2.0.1"}}}}, "@expo/image-utils": {"version": "0.7.4", "requires": {"@expo/spawn-async": "^1.7.2", "chalk": "^4.0.0", "getenv": "^1.0.0", "jimp-compact": "0.16.1", "parse-png": "^2.1.0", "resolve-from": "^5.0.0", "semver": "^7.6.0", "temp-dir": "~2.0.0", "unique-string": "~2.0.0"}}, "@expo/json-file": {"version": "9.1.4", "requires": {"@babel/code-frame": "~7.10.4", "json5": "^2.2.3"}}, "@expo/logger": {"version": "1.0.117", "dev": true, "requires": {"@types/bunyan": "^1.8.11", "bunyan": "^1.8.15"}}, "@expo/metro-config": {"version": "0.20.15", "requires": {"@babel/core": "^7.20.0", "@babel/generator": "^7.20.5", "@babel/parser": "^7.20.0", "@babel/types": "^7.20.0", "@expo/config": "~11.0.10", "@expo/env": "~1.0.5", "@expo/json-file": "~9.1.4", "@expo/spawn-async": "^1.7.2", "chalk": "^4.1.0", "debug": "^4.3.2", "dotenv": "~16.4.5", "dotenv-expand": "~11.0.6", "getenv": "^2.0.0", "glob": "^10.4.2", "jsc-safe-url": "^0.2.4", "lightningcss": "~1.27.0", "minimatch": "^9.0.0", "postcss": "~8.4.32", "resolve-from": "^5.0.0"}, "dependencies": {"getenv": {"version": "2.0.0"}, "minimatch": {"version": "9.0.5", "requires": {"brace-expansion": "^2.0.1"}}}}, "@expo/metro-runtime": {"version": "5.0.4", "requires": {}}, "@expo/multipart-body-parser": {"version": "2.0.0", "dev": true, "requires": {"multipasta": "^0.2.5"}}, "@expo/osascript": {"version": "2.2.4", "requires": {"@expo/spawn-async": "^1.7.2", "exec-async": "^2.2.0"}}, "@expo/package-manager": {"version": "1.8.4", "requires": {"@expo/json-file": "^9.1.4", "@expo/spawn-async": "^1.7.2", "chalk": "^4.0.0", "npm-package-arg": "^11.0.0", "ora": "^3.4.0", "resolve-workspace-root": "^2.0.0"}}, "@expo/pkcs12": {"version": "0.1.3", "dev": true, "requires": {"node-forge": "^1.2.1"}}, "@expo/plist": {"version": "0.3.4", "requires": {"@xmldom/xmldom": "^0.8.8", "base64-js": "^1.2.3", "xmlbuilder": "^15.1.1"}}, "@expo/plugin-help": {"version": "5.1.23", "dev": true, "requires": {"@oclif/core": "^2.11.1"}}, "@expo/plugin-warn-if-update-available": {"version": "2.5.1", "dev": true, "requires": {"@oclif/core": "^2.11.1", "chalk": "^4.1.0", "debug": "^4.3.4", "ejs": "^3.1.7", "fs-extra": "^10.1.0", "http-call": "^5.2.2", "semver": "^7.3.7", "tslib": "^2.4.0"}, "dependencies": {"fs-extra": {"version": "10.1.0", "dev": true, "requires": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}}, "lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.5.4", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "tslib": {"version": "2.6.2", "dev": true}}}, "@expo/prebuild-config": {"version": "9.0.8", "requires": {"@expo/config": "~11.0.10", "@expo/config-plugins": "~10.0.3", "@expo/config-types": "^53.0.4", "@expo/image-utils": "^0.7.4", "@expo/json-file": "^9.1.4", "@react-native/normalize-colors": "0.79.4", "debug": "^4.3.1", "resolve-from": "^5.0.0", "semver": "^7.6.0", "xml2js": "0.6.0"}}, "@expo/results": {"version": "1.0.0", "dev": true}, "@expo/rudder-sdk-node": {"version": "1.1.1", "dev": true, "requires": {"@expo/bunyan": "^4.0.0", "@segment/loosely-validate-event": "^2.0.0", "fetch-retry": "^4.1.1", "md5": "^2.2.1", "node-fetch": "^2.6.1", "remove-trailing-slash": "^0.1.0", "uuid": "^8.3.2"}}, "@expo/sdk-runtime-versions": {"version": "1.0.0"}, "@expo/spawn-async": {"version": "1.7.2", "requires": {"cross-spawn": "^7.0.3"}}, "@expo/steps": {"version": "1.0.173", "dev": true, "requires": {"@expo/eas-build-job": "1.0.173", "@expo/logger": "1.0.117", "@expo/spawn-async": "^1.7.2", "arg": "^5.0.2", "fs-extra": "^11.2.0", "joi": "^17.13.1", "jsep": "^1.3.8", "lodash.clonedeep": "^4.5.0", "lodash.get": "^4.4.2", "this-file": "^2.0.3", "uuid": "^9.0.1", "yaml": "^2.4.3"}, "dependencies": {"uuid": {"version": "9.0.1", "dev": true}}}, "@expo/sudo-prompt": {"version": "9.3.2"}, "@expo/timeago.js": {"version": "1.0.0", "dev": true}, "@expo/vector-icons": {"version": "14.1.0", "requires": {}}, "@expo/ws-tunnel": {"version": "1.0.6"}, "@expo/xcpretty": {"version": "4.3.2", "requires": {"@babel/code-frame": "7.10.4", "chalk": "^4.1.0", "find-up": "^5.0.0", "js-yaml": "^4.1.0"}, "dependencies": {"argparse": {"version": "2.0.1"}, "js-yaml": {"version": "4.1.0", "requires": {"argparse": "^2.0.1"}}}}, "@hapi/hoek": {"version": "9.3.0", "dev": true}, "@hapi/topo": {"version": "5.1.0", "dev": true, "requires": {"@hapi/hoek": "^9.0.0"}}, "@isaacs/cliui": {"version": "8.0.2", "requires": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "dependencies": {"ansi-regex": {"version": "6.1.0"}, "ansi-styles": {"version": "6.2.1"}, "emoji-regex": {"version": "9.2.2"}, "string-width": {"version": "5.1.2", "requires": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}}, "strip-ansi": {"version": "7.1.0", "requires": {"ansi-regex": "^6.0.1"}}, "wrap-ansi": {"version": "8.1.0", "requires": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}}}}, "@isaacs/fs-minipass": {"version": "4.0.1", "requires": {"minipass": "^7.0.4"}}, "@isaacs/ttlcache": {"version": "1.4.1"}, "@istanbuljs/load-nyc-config": {"version": "1.1.0", "requires": {"camelcase": "^5.3.1", "find-up": "^4.1.0", "get-package-type": "^0.1.0", "js-yaml": "^3.13.1", "resolve-from": "^5.0.0"}, "dependencies": {"camelcase": {"version": "5.3.1"}, "find-up": {"version": "4.1.0", "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}}}, "@istanbuljs/schema": {"version": "0.1.3"}, "@jest/create-cache-key-function": {"version": "29.7.0", "requires": {"@jest/types": "^29.6.3"}}, "@jest/environment": {"version": "29.7.0", "requires": {"@jest/fake-timers": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "jest-mock": "^29.7.0"}}, "@jest/fake-timers": {"version": "29.7.0", "requires": {"@jest/types": "^29.6.3", "@sinonjs/fake-timers": "^10.0.2", "@types/node": "*", "jest-message-util": "^29.7.0", "jest-mock": "^29.7.0", "jest-util": "^29.7.0"}}, "@jest/schemas": {"version": "29.6.3", "requires": {"@sinclair/typebox": "^0.27.8"}}, "@jest/transform": {"version": "29.7.0", "requires": {"@babel/core": "^7.11.6", "@jest/types": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.18", "babel-plugin-istanbul": "^6.1.1", "chalk": "^4.0.0", "convert-source-map": "^2.0.0", "fast-json-stable-stringify": "^2.1.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "micromatch": "^4.0.4", "pirates": "^4.0.4", "slash": "^3.0.0", "write-file-atomic": "^4.0.2"}, "dependencies": {"write-file-atomic": {"version": "4.0.2", "requires": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}}}}, "@jest/types": {"version": "29.6.3", "requires": {"@jest/schemas": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}}, "@jridgewell/gen-mapping": {"version": "0.3.8", "requires": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}}, "@jridgewell/resolve-uri": {"version": "3.1.2"}, "@jridgewell/set-array": {"version": "1.2.1"}, "@jridgewell/source-map": {"version": "0.3.6", "requires": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}}, "@jridgewell/sourcemap-codec": {"version": "1.5.0"}, "@jridgewell/trace-mapping": {"version": "0.3.25", "requires": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "@motionone/animation": {"version": "10.18.0", "requires": {"@motionone/easing": "^10.18.0", "@motionone/types": "^10.17.1", "@motionone/utils": "^10.18.0", "tslib": "^2.3.1"}}, "@motionone/dom": {"version": "10.12.0", "requires": {"@motionone/animation": "^10.12.0", "@motionone/generators": "^10.12.0", "@motionone/types": "^10.12.0", "@motionone/utils": "^10.12.0", "hey-listen": "^1.0.8", "tslib": "^2.3.1"}}, "@motionone/easing": {"version": "10.18.0", "requires": {"@motionone/utils": "^10.18.0", "tslib": "^2.3.1"}}, "@motionone/generators": {"version": "10.18.0", "requires": {"@motionone/types": "^10.17.1", "@motionone/utils": "^10.18.0", "tslib": "^2.3.1"}}, "@motionone/types": {"version": "10.17.1"}, "@motionone/utils": {"version": "10.18.0", "requires": {"@motionone/types": "^10.17.1", "hey-listen": "^1.0.8", "tslib": "^2.3.1"}}, "@nodelib/fs.scandir": {"version": "2.1.5", "dev": true, "requires": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}}, "@nodelib/fs.stat": {"version": "2.0.5", "dev": true}, "@nodelib/fs.walk": {"version": "1.2.8", "dev": true, "requires": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}}, "@oclif/core": {"version": "2.16.0", "dev": true, "requires": {"@types/cli-progress": "^3.11.0", "ansi-escapes": "^4.3.2", "ansi-styles": "^4.3.0", "cardinal": "^2.1.1", "chalk": "^4.1.2", "clean-stack": "^3.0.1", "cli-progress": "^3.12.0", "debug": "^4.3.4", "ejs": "^3.1.8", "get-package-type": "^0.1.0", "globby": "^11.1.0", "hyperlinker": "^1.0.0", "indent-string": "^4.0.0", "is-wsl": "^2.2.0", "js-yaml": "^3.14.1", "natural-orderby": "^2.0.3", "object-treeify": "^1.1.33", "password-prompt": "^1.1.2", "slice-ansi": "^4.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "supports-color": "^8.1.1", "supports-hyperlinks": "^2.2.0", "ts-node": "^10.9.1", "tslib": "^2.5.0", "widest-line": "^3.1.0", "wordwrap": "^1.0.0", "wrap-ansi": "^7.0.0"}, "dependencies": {"tslib": {"version": "2.6.2", "dev": true}}}, "@oclif/linewrap": {"version": "1.0.0", "dev": true}, "@oclif/plugin-autocomplete": {"version": "2.3.10", "dev": true, "requires": {"@oclif/core": "^2.15.0", "chalk": "^4.1.0", "debug": "^4.3.4"}}, "@oclif/screen": {"version": "3.0.8", "dev": true}, "@pkgjs/parseargs": {"version": "0.11.0", "optional": true}, "@react-native-async-storage/async-storage": {"version": "2.1.2", "requires": {"merge-options": "^3.0.4"}}, "@react-native-community/netinfo": {"version": "11.4.1", "resolved": "https://registry.npmjs.org/@react-native-community/netinfo/-/netinfo-11.4.1.tgz", "integrity": "sha512-B0BYAkghz3Q2V09BF88RA601XursIEA111tnc2JOaN7axJWmNefmfjZqw/KdSxKZp7CZUuPpjBmz/WCR9uaHYg==", "requires": {}}, "@react-native/assets-registry": {"version": "0.79.4"}, "@react-native/babel-plugin-codegen": {"version": "0.79.4", "requires": {"@babel/traverse": "^7.25.3", "@react-native/codegen": "0.79.4"}}, "@react-native/babel-preset": {"version": "0.79.4", "requires": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-export-default-from": "^7.24.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-export-default-from": "^7.24.7", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-transform-arrow-functions": "^7.24.7", "@babel/plugin-transform-async-generator-functions": "^7.25.4", "@babel/plugin-transform-async-to-generator": "^7.24.7", "@babel/plugin-transform-block-scoping": "^7.25.0", "@babel/plugin-transform-class-properties": "^7.25.4", "@babel/plugin-transform-classes": "^7.25.4", "@babel/plugin-transform-computed-properties": "^7.24.7", "@babel/plugin-transform-destructuring": "^7.24.8", "@babel/plugin-transform-flow-strip-types": "^7.25.2", "@babel/plugin-transform-for-of": "^7.24.7", "@babel/plugin-transform-function-name": "^7.25.1", "@babel/plugin-transform-literals": "^7.25.2", "@babel/plugin-transform-logical-assignment-operators": "^7.24.7", "@babel/plugin-transform-modules-commonjs": "^7.24.8", "@babel/plugin-transform-named-capturing-groups-regex": "^7.24.7", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.7", "@babel/plugin-transform-numeric-separator": "^7.24.7", "@babel/plugin-transform-object-rest-spread": "^7.24.7", "@babel/plugin-transform-optional-catch-binding": "^7.24.7", "@babel/plugin-transform-optional-chaining": "^7.24.8", "@babel/plugin-transform-parameters": "^7.24.7", "@babel/plugin-transform-private-methods": "^7.24.7", "@babel/plugin-transform-private-property-in-object": "^7.24.7", "@babel/plugin-transform-react-display-name": "^7.24.7", "@babel/plugin-transform-react-jsx": "^7.25.2", "@babel/plugin-transform-react-jsx-self": "^7.24.7", "@babel/plugin-transform-react-jsx-source": "^7.24.7", "@babel/plugin-transform-regenerator": "^7.24.7", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/plugin-transform-shorthand-properties": "^7.24.7", "@babel/plugin-transform-spread": "^7.24.7", "@babel/plugin-transform-sticky-regex": "^7.24.7", "@babel/plugin-transform-typescript": "^7.25.2", "@babel/plugin-transform-unicode-regex": "^7.24.7", "@babel/template": "^7.25.0", "@react-native/babel-plugin-codegen": "0.79.4", "babel-plugin-syntax-hermes-parser": "0.25.1", "babel-plugin-transform-flow-enums": "^0.0.2", "react-refresh": "^0.14.0"}}, "@react-native/codegen": {"version": "0.79.4", "requires": {"glob": "^7.1.1", "hermes-parser": "0.25.1", "invariant": "^2.2.4", "nullthrows": "^1.1.1", "yargs": "^17.6.2"}, "dependencies": {"glob": {"version": "7.2.3", "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}}}, "@react-native/community-cli-plugin": {"version": "0.79.4", "requires": {"@react-native/dev-middleware": "0.79.4", "chalk": "^4.0.0", "debug": "^2.2.0", "invariant": "^2.2.4", "metro": "^0.82.0", "metro-config": "^0.82.0", "metro-core": "^0.82.0", "semver": "^7.1.3"}, "dependencies": {"debug": {"version": "2.6.9", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0"}}}, "@react-native/debugger-frontend": {"version": "0.79.4"}, "@react-native/dev-middleware": {"version": "0.79.4", "requires": {"@isaacs/ttlcache": "^1.4.1", "@react-native/debugger-frontend": "0.79.4", "chrome-launcher": "^0.15.2", "chromium-edge-launcher": "^0.2.0", "connect": "^3.6.5", "debug": "^2.2.0", "invariant": "^2.2.4", "nullthrows": "^1.1.1", "open": "^7.0.3", "serve-static": "^1.16.2", "ws": "^6.2.3"}, "dependencies": {"debug": {"version": "2.6.9", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0"}}}, "@react-native/gradle-plugin": {"version": "0.79.4"}, "@react-native/js-polyfills": {"version": "0.79.4"}, "@react-native/normalize-color": {"version": "2.1.0"}, "@react-native/normalize-colors": {"version": "0.79.4"}, "@react-native/virtualized-lists": {"version": "0.79.4", "requires": {"invariant": "^2.2.4", "nullthrows": "^1.1.1"}}, "@react-navigation/bottom-tabs": {"version": "7.4.2", "requires": {"@react-navigation/elements": "^2.5.2", "color": "^4.2.3"}}, "@react-navigation/core": {"version": "7.12.1", "requires": {"@react-navigation/routers": "^7.4.1", "escape-string-regexp": "^4.0.0", "nanoid": "^3.3.11", "query-string": "^7.1.3", "react-is": "^19.1.0", "use-latest-callback": "^0.2.4", "use-sync-external-store": "^1.5.0"}, "dependencies": {"react-is": {"version": "19.1.0"}}}, "@react-navigation/elements": {"version": "2.5.2", "requires": {"color": "^4.2.3", "use-latest-callback": "^0.2.4", "use-sync-external-store": "^1.5.0"}}, "@react-navigation/native": {"version": "7.1.14", "requires": {"@react-navigation/core": "^7.12.1", "escape-string-regexp": "^4.0.0", "fast-deep-equal": "^3.1.3", "nanoid": "^3.3.11", "use-latest-callback": "^0.2.4"}}, "@react-navigation/native-stack": {"version": "7.3.21", "requires": {"@react-navigation/elements": "^2.5.2", "warn-once": "^0.1.1"}}, "@react-navigation/routers": {"version": "7.4.1", "requires": {"nanoid": "^3.3.11"}}, "@segment/ajv-human-errors": {"version": "2.15.0", "dev": true, "requires": {}}, "@segment/loosely-validate-event": {"version": "2.0.0", "dev": true, "requires": {"component-type": "^1.2.1", "join-component": "^1.1.0"}}, "@sideway/address": {"version": "4.1.5", "dev": true, "requires": {"@hapi/hoek": "^9.0.0"}}, "@sideway/formula": {"version": "3.0.1", "dev": true}, "@sideway/pinpoint": {"version": "2.0.0", "dev": true}, "@sinclair/typebox": {"version": "0.27.8"}, "@sinonjs/commons": {"version": "3.0.1", "requires": {"type-detect": "4.0.8"}}, "@sinonjs/fake-timers": {"version": "10.3.0", "requires": {"@sinonjs/commons": "^3.0.0"}}, "@swc/core": {"version": "1.12.7", "dev": true, "optional": true, "peer": true, "requires": {"@swc/core-darwin-arm64": "1.12.7", "@swc/core-darwin-x64": "1.12.7", "@swc/core-linux-arm-gnueabihf": "1.12.7", "@swc/core-linux-arm64-gnu": "1.12.7", "@swc/core-linux-arm64-musl": "1.12.7", "@swc/core-linux-x64-gnu": "1.12.7", "@swc/core-linux-x64-musl": "1.12.7", "@swc/core-win32-arm64-msvc": "1.12.7", "@swc/core-win32-ia32-msvc": "1.12.7", "@swc/core-win32-x64-msvc": "1.12.7", "@swc/counter": "^0.1.3", "@swc/types": "^0.1.23"}}, "@swc/core-win32-x64-msvc": {"version": "1.12.7", "dev": true, "optional": true, "peer": true}, "@swc/counter": {"version": "0.1.3", "dev": true, "optional": true, "peer": true}, "@swc/helpers": {"version": "0.5.17", "dev": true, "optional": true, "peer": true, "requires": {"tslib": "^2.8.0"}}, "@swc/types": {"version": "0.1.23", "dev": true, "optional": true, "peer": true, "requires": {"@swc/counter": "^0.1.3"}}, "@tamagui/animations-css": {"version": "1.129.4", "requires": {"@tamagui/constants": "1.129.4", "@tamagui/cubic-bezier-animator": "1.129.4", "@tamagui/use-presence": "1.129.4", "@tamagui/web": "1.129.4"}}, "@tamagui/animations-moti": {"version": "1.129.4", "requires": {"@tamagui/use-presence": "1.129.4", "@tamagui/web": "1.129.4", "moti": "^0.30.0"}}, "@tamagui/animations-react-native": {"version": "1.129.4", "requires": {"@tamagui/constants": "1.129.4", "@tamagui/use-presence": "1.129.4", "@tamagui/web": "1.129.4"}}, "@tamagui/colors": {"version": "1.129.4"}, "@tamagui/compose-refs": {"version": "1.129.4", "requires": {}}, "@tamagui/config": {"version": "1.129.4", "requires": {"@tamagui/animations-css": "1.129.4", "@tamagui/animations-moti": "1.129.4", "@tamagui/animations-react-native": "1.129.4", "@tamagui/colors": "1.129.4", "@tamagui/font-inter": "1.129.4", "@tamagui/font-silkscreen": "1.129.4", "@tamagui/react-native-media-driver": "1.129.4", "@tamagui/shorthands": "1.129.4", "@tamagui/themes": "1.129.4", "@tamagui/web": "1.129.4"}}, "@tamagui/constants": {"version": "1.129.4", "requires": {}}, "@tamagui/core": {"version": "1.129.4", "requires": {"@tamagui/react-native-media-driver": "1.129.4", "@tamagui/react-native-use-pressable": "1.129.4", "@tamagui/react-native-use-responder-events": "1.129.4", "@tamagui/use-event": "1.129.4", "@tamagui/web": "1.129.4"}}, "@tamagui/create-theme": {"version": "1.129.4", "requires": {"@tamagui/web": "1.129.4"}}, "@tamagui/cubic-bezier-animator": {"version": "1.129.4"}, "@tamagui/font-inter": {"version": "1.129.4", "requires": {"@tamagui/core": "1.129.4"}}, "@tamagui/font-silkscreen": {"version": "1.129.4", "requires": {"@tamagui/core": "1.129.4"}}, "@tamagui/helpers": {"version": "1.129.4", "requires": {"@tamagui/constants": "1.129.4", "@tamagui/simple-hash": "1.129.4"}}, "@tamagui/normalize-css-color": {"version": "1.129.4", "requires": {"@react-native/normalize-color": "^2.1.0"}}, "@tamagui/react-native-media-driver": {"version": "1.129.4", "requires": {"@tamagui/web": "1.129.4"}}, "@tamagui/react-native-use-pressable": {"version": "1.129.4", "requires": {}}, "@tamagui/react-native-use-responder-events": {"version": "1.129.4", "requires": {}}, "@tamagui/shorthands": {"version": "1.129.4", "requires": {"@tamagui/web": "1.129.4"}}, "@tamagui/simple-hash": {"version": "1.129.4"}, "@tamagui/theme-base": {"version": "1.129.4"}, "@tamagui/theme-builder": {"version": "1.129.4", "requires": {"@tamagui/create-theme": "1.129.4", "color2k": "^2.0.2"}}, "@tamagui/themes": {"version": "1.129.4", "requires": {"@tamagui/colors": "1.129.4", "@tamagui/create-theme": "1.129.4", "@tamagui/theme-builder": "1.129.4", "@tamagui/web": "1.129.4", "color2k": "^2.0.2"}}, "@tamagui/timer": {"version": "1.129.4"}, "@tamagui/types": {"version": "1.129.4"}, "@tamagui/use-did-finish-ssr": {"version": "1.129.4", "requires": {}}, "@tamagui/use-event": {"version": "1.129.4", "requires": {"@tamagui/constants": "1.129.4"}}, "@tamagui/use-force-update": {"version": "1.129.4", "requires": {}}, "@tamagui/use-presence": {"version": "1.129.4", "requires": {"@tamagui/web": "1.129.4"}}, "@tamagui/web": {"version": "1.129.4", "requires": {"@tamagui/compose-refs": "1.129.4", "@tamagui/constants": "1.129.4", "@tamagui/helpers": "1.129.4", "@tamagui/normalize-css-color": "1.129.4", "@tamagui/timer": "1.129.4", "@tamagui/types": "1.129.4", "@tamagui/use-did-finish-ssr": "1.129.4", "@tamagui/use-event": "1.129.4", "@tamagui/use-force-update": "1.129.4"}}, "@tsconfig/node10": {"version": "1.0.11", "dev": true}, "@tsconfig/node12": {"version": "1.0.11", "dev": true}, "@tsconfig/node14": {"version": "1.0.3", "dev": true}, "@tsconfig/node16": {"version": "1.0.4", "dev": true}, "@types/babel__core": {"version": "7.20.5", "requires": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "@types/babel__generator": {"version": "7.27.0", "requires": {"@babel/types": "^7.0.0"}}, "@types/babel__template": {"version": "7.4.4", "requires": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "@types/babel__traverse": {"version": "7.20.7", "requires": {"@babel/types": "^7.20.7"}}, "@types/bunyan": {"version": "1.8.11", "dev": true, "requires": {"@types/node": "*"}}, "@types/cli-progress": {"version": "3.11.6", "dev": true, "requires": {"@types/node": "*"}}, "@types/graceful-fs": {"version": "4.1.9", "requires": {"@types/node": "*"}}, "@types/hammerjs": {"version": "2.0.46"}, "@types/istanbul-lib-coverage": {"version": "2.0.6"}, "@types/istanbul-lib-report": {"version": "3.0.3", "requires": {"@types/istanbul-lib-coverage": "*"}}, "@types/istanbul-reports": {"version": "3.0.4", "requires": {"@types/istanbul-lib-report": "*"}}, "@types/node": {"version": "24.0.6", "requires": {"undici-types": "~7.8.0"}}, "@types/react": {"version": "19.0.14", "devOptional": true, "requires": {"csstype": "^3.0.2"}}, "@types/stack-utils": {"version": "2.0.3"}, "@types/yargs": {"version": "17.0.33", "requires": {"@types/yargs-parser": "*"}}, "@types/yargs-parser": {"version": "21.0.3"}, "@urql/core": {"version": "5.1.2", "requires": {"@0no-co/graphql.web": "^1.0.13", "wonka": "^6.3.2"}}, "@urql/exchange-retry": {"version": "1.3.2", "requires": {"@urql/core": "^5.1.2", "wonka": "^6.3.2"}}, "@xmldom/xmldom": {"version": "0.8.10"}, "abort-controller": {"version": "3.0.0", "requires": {"event-target-shim": "^5.0.0"}}, "accepts": {"version": "1.3.8", "requires": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}}, "acorn": {"version": "8.15.0"}, "acorn-walk": {"version": "8.3.4", "dev": true, "requires": {"acorn": "^8.11.0"}}, "agent-base": {"version": "7.1.3"}, "ajv": {"version": "8.11.0", "dev": true, "requires": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}}, "ajv-formats": {"version": "2.1.1", "dev": true, "requires": {"ajv": "^8.0.0"}}, "anser": {"version": "1.4.10"}, "ansi-escapes": {"version": "4.3.2", "requires": {"type-fest": "^0.21.3"}, "dependencies": {"type-fest": {"version": "0.21.3"}}}, "ansi-regex": {"version": "5.0.1"}, "ansi-styles": {"version": "4.3.0", "requires": {"color-convert": "^2.0.1"}}, "ansicolors": {"version": "0.3.2", "dev": true}, "any-promise": {"version": "1.3.0"}, "anymatch": {"version": "3.1.3", "requires": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}}, "arg": {"version": "5.0.2"}, "argparse": {"version": "1.0.10", "requires": {"sprintf-js": "~1.0.2"}}, "array-union": {"version": "2.1.0", "dev": true}, "asap": {"version": "2.0.6"}, "asn1": {"version": "0.2.6", "dev": true, "requires": {"safer-buffer": "~2.1.0"}}, "astral-regex": {"version": "2.0.0", "dev": true}, "async": {"version": "3.2.6", "dev": true}, "async-limiter": {"version": "1.0.1"}, "asynckit": {"version": "0.4.0"}, "at-least-node": {"version": "1.0.0", "dev": true}, "axios": {"version": "1.10.0", "requires": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "b4a": {"version": "1.6.7", "dev": true}, "babel-jest": {"version": "29.7.0", "requires": {"@jest/transform": "^29.7.0", "@types/babel__core": "^7.1.14", "babel-plugin-istanbul": "^6.1.1", "babel-preset-jest": "^29.6.3", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "slash": "^3.0.0"}}, "babel-plugin-istanbul": {"version": "6.1.1", "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^5.0.4", "test-exclude": "^6.0.0"}}, "babel-plugin-jest-hoist": {"version": "29.6.3", "requires": {"@babel/template": "^7.3.3", "@babel/types": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}}, "babel-plugin-polyfill-corejs2": {"version": "0.4.14", "requires": {"@babel/compat-data": "^7.27.7", "@babel/helper-define-polyfill-provider": "^0.6.5", "semver": "^6.3.1"}, "dependencies": {"semver": {"version": "6.3.1"}}}, "babel-plugin-polyfill-corejs3": {"version": "0.11.1", "requires": {"@babel/helper-define-polyfill-provider": "^0.6.3", "core-js-compat": "^3.40.0"}}, "babel-plugin-polyfill-regenerator": {"version": "0.6.5", "requires": {"@babel/helper-define-polyfill-provider": "^0.6.5"}}, "babel-plugin-react-native-web": {"version": "0.19.13"}, "babel-plugin-syntax-hermes-parser": {"version": "0.25.1", "requires": {"hermes-parser": "0.25.1"}}, "babel-plugin-transform-flow-enums": {"version": "0.0.2", "requires": {"@babel/plugin-syntax-flow": "^7.12.1"}}, "babel-preset-current-node-syntax": {"version": "1.1.0", "requires": {"@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/plugin-syntax-import-attributes": "^7.24.7", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-top-level-await": "^7.14.5"}}, "babel-preset-expo": {"version": "13.2.1", "requires": {"@babel/helper-module-imports": "^7.25.9", "@babel/plugin-proposal-decorators": "^7.12.9", "@babel/plugin-proposal-export-default-from": "^7.24.7", "@babel/plugin-syntax-export-default-from": "^7.24.7", "@babel/plugin-transform-export-namespace-from": "^7.25.9", "@babel/plugin-transform-flow-strip-types": "^7.25.2", "@babel/plugin-transform-modules-commonjs": "^7.24.8", "@babel/plugin-transform-object-rest-spread": "^7.24.7", "@babel/plugin-transform-parameters": "^7.24.7", "@babel/plugin-transform-private-methods": "^7.24.7", "@babel/plugin-transform-private-property-in-object": "^7.24.7", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/preset-react": "^7.22.15", "@babel/preset-typescript": "^7.23.0", "@react-native/babel-preset": "0.79.4", "babel-plugin-react-native-web": "~0.19.13", "babel-plugin-syntax-hermes-parser": "^0.25.1", "babel-plugin-transform-flow-enums": "^0.0.2", "debug": "^4.3.4", "react-refresh": "^0.14.2", "resolve-from": "^5.0.0"}}, "babel-preset-jest": {"version": "29.6.3", "requires": {"babel-plugin-jest-hoist": "^29.6.3", "babel-preset-current-node-syntax": "^1.0.0"}}, "balanced-match": {"version": "1.0.2"}, "bare-events": {"version": "2.5.4", "dev": true, "optional": true}, "base64-js": {"version": "1.5.1"}, "better-opn": {"version": "3.0.2", "requires": {"open": "^8.0.4"}, "dependencies": {"open": {"version": "8.4.2", "requires": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}}}}, "big-integer": {"version": "1.6.52"}, "boolbase": {"version": "1.0.0"}, "bplist-creator": {"version": "0.1.0", "requires": {"stream-buffers": "2.2.x"}}, "bplist-parser": {"version": "0.3.2", "requires": {"big-integer": "1.6.x"}}, "brace-expansion": {"version": "2.0.2", "requires": {"balanced-match": "^1.0.0"}}, "braces": {"version": "3.0.3", "requires": {"fill-range": "^7.1.1"}}, "browserslist": {"version": "4.25.1", "requires": {"caniuse-lite": "^1.0.30001726", "electron-to-chromium": "^1.5.173", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}}, "bser": {"version": "2.1.1", "requires": {"node-int64": "^0.4.0"}}, "buffer": {"version": "5.7.1", "requires": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "buffer-equal-constant-time": {"version": "1.0.1", "dev": true}, "buffer-from": {"version": "1.1.2"}, "bunyan": {"version": "1.8.15", "dev": true, "requires": {"dtrace-provider": "~0.8", "moment": "^2.19.3", "mv": "~2", "safe-json-stringify": "~1"}}, "bytes": {"version": "3.1.2"}, "caller-callsite": {"version": "2.0.0", "requires": {"callsites": "^2.0.0"}}, "caller-path": {"version": "2.0.0", "requires": {"caller-callsite": "^2.0.0"}}, "callsites": {"version": "2.0.0"}, "camelcase": {"version": "6.3.0"}, "caniuse-lite": {"version": "1.0.30001726"}, "cardinal": {"version": "2.1.1", "dev": true, "requires": {"ansicolors": "~0.3.2", "redeyed": "~2.1.0"}}, "chalk": {"version": "4.1.2", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "dependencies": {"supports-color": {"version": "7.2.0", "requires": {"has-flag": "^4.0.0"}}}}, "charenc": {"version": "0.0.2", "dev": true}, "chownr": {"version": "3.0.0"}, "chrome-launcher": {"version": "0.15.2", "requires": {"@types/node": "*", "escape-string-regexp": "^4.0.0", "is-wsl": "^2.2.0", "lighthouse-logger": "^1.0.0"}}, "chromium-edge-launcher": {"version": "0.2.0", "requires": {"@types/node": "*", "escape-string-regexp": "^4.0.0", "is-wsl": "^2.2.0", "lighthouse-logger": "^1.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2"}}, "ci-info": {"version": "3.9.0"}, "clean-stack": {"version": "3.0.1", "dev": true, "requires": {"escape-string-regexp": "4.0.0"}}, "cli-cursor": {"version": "2.1.0", "requires": {"restore-cursor": "^2.0.0"}}, "cli-progress": {"version": "3.12.0", "dev": true, "requires": {"string-width": "^4.2.3"}}, "cli-spinners": {"version": "2.9.2"}, "cliui": {"version": "8.0.1", "requires": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}}, "clone": {"version": "1.0.4"}, "color": {"version": "4.2.3", "requires": {"color-convert": "^2.0.1", "color-string": "^1.9.0"}}, "color-convert": {"version": "2.0.1", "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4"}, "color-string": {"version": "1.9.1", "requires": {"color-name": "^1.0.0", "simple-swizzle": "^0.2.2"}}, "color2k": {"version": "2.0.3"}, "combined-stream": {"version": "1.0.8", "requires": {"delayed-stream": "~1.0.0"}}, "commander": {"version": "2.20.3"}, "component-type": {"version": "1.2.2", "dev": true}, "compressible": {"version": "2.0.18", "requires": {"mime-db": ">= 1.43.0 < 2"}, "dependencies": {"mime-db": {"version": "1.54.0"}}}, "compression": {"version": "1.8.0", "requires": {"bytes": "3.1.2", "compressible": "~2.0.18", "debug": "2.6.9", "negotiator": "~0.6.4", "on-headers": "~1.0.2", "safe-buffer": "5.2.1", "vary": "~1.1.2"}, "dependencies": {"debug": {"version": "2.6.9", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0"}, "negotiator": {"version": "0.6.4"}}}, "concat-map": {"version": "0.0.1"}, "connect": {"version": "3.7.0", "requires": {"debug": "2.6.9", "finalhandler": "1.1.2", "parseurl": "~1.3.3", "utils-merge": "1.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0"}}}, "content-type": {"version": "1.0.5", "dev": true}, "convert-source-map": {"version": "2.0.0"}, "core-js-compat": {"version": "3.43.0", "requires": {"browserslist": "^4.25.0"}}, "cosmiconfig": {"version": "5.2.1", "requires": {"import-fresh": "^2.0.0", "is-directory": "^0.3.1", "js-yaml": "^3.13.1", "parse-json": "^4.0.0"}}, "create-require": {"version": "1.1.1", "dev": true}, "cross-fetch": {"version": "3.2.0", "requires": {"node-fetch": "^2.7.0"}, "dependencies": {"node-fetch": {"version": "2.7.0", "requires": {"whatwg-url": "^5.0.0"}}}}, "cross-spawn": {"version": "7.0.6", "requires": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}}, "crypt": {"version": "0.0.2", "dev": true}, "crypto-random-string": {"version": "2.0.0"}, "css-in-js-utils": {"version": "3.1.0", "requires": {"hyphenate-style-name": "^1.0.3"}}, "css-select": {"version": "5.1.0", "requires": {"boolbase": "^1.0.0", "css-what": "^6.1.0", "domhandler": "^5.0.2", "domutils": "^3.0.1", "nth-check": "^2.0.1"}}, "css-tree": {"version": "1.1.3", "requires": {"mdn-data": "2.0.14", "source-map": "^0.6.1"}, "dependencies": {"source-map": {"version": "0.6.1"}}}, "css-what": {"version": "6.1.0"}, "csstype": {"version": "3.1.3", "devOptional": true}, "dateformat": {"version": "4.6.3", "dev": true}, "debug": {"version": "4.4.1", "requires": {"ms": "^2.1.3"}}, "decode-uri-component": {"version": "0.2.2"}, "deep-extend": {"version": "0.6.0"}, "deepmerge": {"version": "4.3.1"}, "defaults": {"version": "1.0.4", "requires": {"clone": "^1.0.2"}}, "define-lazy-prop": {"version": "2.0.0"}, "delayed-stream": {"version": "1.0.0"}, "depd": {"version": "2.0.0"}, "destroy": {"version": "1.2.0"}, "detect-libc": {"version": "1.0.3"}, "diff": {"version": "4.0.2", "dev": true}, "dir-glob": {"version": "3.0.1", "dev": true, "requires": {"path-type": "^4.0.0"}}, "dom-serializer": {"version": "2.0.0", "requires": {"domelementtype": "^2.3.0", "domhandler": "^5.0.2", "entities": "^4.2.0"}}, "domelementtype": {"version": "2.3.0"}, "domhandler": {"version": "5.0.3", "requires": {"domelementtype": "^2.3.0"}}, "domino": {"version": "2.1.6", "dev": true}, "domutils": {"version": "3.2.2", "requires": {"dom-serializer": "^2.0.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3"}}, "dotenv": {"version": "16.4.7"}, "dotenv-expand": {"version": "11.0.7", "requires": {"dotenv": "^16.4.5"}}, "dtrace-provider": {"version": "0.8.8", "dev": true, "optional": true, "requires": {"nan": "^2.14.0"}}, "eas-cli": {"version": "16.13.2", "dev": true, "requires": {"@expo/apple-utils": "2.1.12", "@expo/code-signing-certificates": "0.0.5", "@expo/config": "10.0.6", "@expo/config-plugins": "9.0.12", "@expo/eas-build-job": "1.0.173", "@expo/eas-json": "16.13.2", "@expo/env": "^1.0.0", "@expo/json-file": "8.3.3", "@expo/logger": "1.0.117", "@expo/multipart-body-parser": "2.0.0", "@expo/osascript": "2.1.4", "@expo/package-manager": "1.7.0", "@expo/pkcs12": "0.1.3", "@expo/plist": "0.2.0", "@expo/plugin-help": "5.1.23", "@expo/plugin-warn-if-update-available": "2.5.1", "@expo/prebuild-config": "8.0.17", "@expo/results": "1.0.0", "@expo/rudder-sdk-node": "1.1.1", "@expo/spawn-async": "1.7.2", "@expo/steps": "1.0.173", "@expo/timeago.js": "1.0.0", "@oclif/core": "^1.26.2", "@oclif/plugin-autocomplete": "^2.3.10", "@segment/ajv-human-errors": "^2.1.2", "@urql/core": "4.0.11", "@urql/exchange-retry": "1.2.0", "ajv": "8.11.0", "ajv-formats": "2.1.1", "better-opn": "3.0.2", "bplist-parser": "^0.3.0", "chalk": "4.1.2", "cli-progress": "3.12.0", "dateformat": "4.6.3", "diff": "7.0.0", "dotenv": "16.3.1", "env-paths": "2.2.0", "envinfo": "7.11.0", "fast-deep-equal": "3.1.3", "fast-glob": "3.3.2", "figures": "3.2.0", "form-data": "4.0.0", "fs-extra": "11.2.0", "getenv": "1.0.0", "gradle-to-js": "2.0.1", "graphql": "16.8.1", "graphql-tag": "2.12.6", "https-proxy-agent": "5.0.1", "ignore": "5.3.0", "indent-string": "4.0.0", "jks-js": "1.1.0", "joi": "17.11.0", "jsonwebtoken": "9.0.0", "keychain": "1.5.0", "log-symbols": "4.1.0", "mime": "3.0.0", "minimatch": "5.1.2", "minizlib": "3.0.1", "nanoid": "3.3.8", "node-fetch": "2.6.7", "node-forge": "1.3.1", "node-stream-zip": "1.15.0", "nullthrows": "1.1.1", "ora": "5.1.0", "pkg-dir": "4.2.0", "pngjs": "7.0.0", "promise-limit": "2.7.0", "promise-retry": "2.0.1", "prompts": "2.4.2", "qrcode-terminal": "0.12.0", "resolve-from": "5.0.0", "semver": "7.5.4", "set-interval-async": "3.0.3", "slash": "3.0.0", "tar": "6.2.1", "tar-stream": "3.1.7", "terminal-link": "2.1.1", "tslib": "2.6.2", "turndown": "7.1.2", "untildify": "4.0.0", "uuid": "9.0.1", "wrap-ansi": "7.0.0", "yaml": "2.6.0", "zod": "^3.23.8"}, "dependencies": {"@expo/config": {"version": "10.0.6", "dev": true, "requires": {"@babel/code-frame": "~7.10.4", "@expo/config-plugins": "~9.0.10", "@expo/config-types": "^52.0.0", "@expo/json-file": "^9.0.0", "deepmerge": "^4.3.1", "getenv": "^1.0.0", "glob": "^10.4.2", "require-from-string": "^2.0.2", "resolve-from": "^5.0.0", "resolve-workspace-root": "^2.0.0", "semver": "^7.6.0", "slugify": "^1.3.4", "sucrase": "3.35.0"}, "dependencies": {"@expo/json-file": {"version": "9.1.4", "dev": true, "requires": {"@babel/code-frame": "~7.10.4", "json5": "^2.2.3"}}, "semver": {"version": "7.7.2", "dev": true}}}, "@expo/config-plugins": {"version": "9.0.12", "dev": true, "requires": {"@expo/config-types": "^52.0.0", "@expo/json-file": "~9.0.0", "@expo/plist": "^0.2.0", "@expo/sdk-runtime-versions": "^1.0.0", "chalk": "^4.1.2", "debug": "^4.3.5", "getenv": "^1.0.0", "glob": "^10.4.2", "resolve-from": "^5.0.0", "semver": "^7.5.4", "slash": "^3.0.0", "slugify": "^1.6.6", "xcode": "^3.0.1", "xml2js": "0.6.0"}, "dependencies": {"@expo/json-file": {"version": "9.0.2", "dev": true, "requires": {"@babel/code-frame": "~7.10.4", "json5": "^2.2.3", "write-file-atomic": "^2.3.0"}}}}, "@expo/config-types": {"version": "52.0.5", "dev": true}, "@expo/image-utils": {"version": "0.6.5", "dev": true, "requires": {"@expo/spawn-async": "^1.7.2", "chalk": "^4.0.0", "fs-extra": "9.0.0", "getenv": "^1.0.0", "jimp-compact": "0.16.1", "parse-png": "^2.1.0", "resolve-from": "^5.0.0", "semver": "^7.6.0", "temp-dir": "~2.0.0", "unique-string": "~2.0.0"}, "dependencies": {"fs-extra": {"version": "9.0.0", "dev": true, "requires": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^1.0.0"}}, "semver": {"version": "7.7.2", "dev": true}, "universalify": {"version": "1.0.0", "dev": true}}}, "@expo/json-file": {"version": "8.3.3", "dev": true, "requires": {"@babel/code-frame": "~7.10.4", "json5": "^2.2.2", "write-file-atomic": "^2.3.0"}}, "@expo/osascript": {"version": "2.1.4", "dev": true, "requires": {"@expo/spawn-async": "^1.7.2", "exec-async": "^2.2.0"}}, "@expo/package-manager": {"version": "1.7.0", "dev": true, "requires": {"@expo/json-file": "^9.0.0", "@expo/spawn-async": "^1.7.2", "ansi-regex": "^5.0.0", "chalk": "^4.0.0", "find-up": "^5.0.0", "js-yaml": "^3.13.1", "micromatch": "^4.0.8", "npm-package-arg": "^11.0.0", "ora": "^3.4.0", "resolve-workspace-root": "^2.0.0", "split": "^1.0.1", "sudo-prompt": "9.1.1"}, "dependencies": {"@expo/json-file": {"version": "9.1.4", "dev": true, "requires": {"@babel/code-frame": "~7.10.4", "json5": "^2.2.3"}}, "ansi-styles": {"version": "3.2.1", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "log-symbols": {"version": "2.2.0", "dev": true, "requires": {"chalk": "^2.0.1"}, "dependencies": {"chalk": {"version": "2.4.2", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}}}, "ora": {"version": "3.4.0", "dev": true, "requires": {"chalk": "^2.4.2", "cli-cursor": "^2.1.0", "cli-spinners": "^2.0.0", "log-symbols": "^2.2.0", "strip-ansi": "^5.2.0", "wcwidth": "^1.0.1"}, "dependencies": {"chalk": {"version": "2.4.2", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}}}, "strip-ansi": {"version": "5.2.0", "dev": true, "requires": {"ansi-regex": "^4.1.0"}, "dependencies": {"ansi-regex": {"version": "4.1.1", "dev": true}}}, "supports-color": {"version": "5.5.0", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "@expo/plist": {"version": "0.2.0", "dev": true, "requires": {"@xmldom/xmldom": "~0.7.7", "base64-js": "^1.2.3", "xmlbuilder": "^14.0.0"}}, "@expo/prebuild-config": {"version": "8.0.17", "dev": true, "requires": {"@expo/config": "~10.0.4", "@expo/config-plugins": "~9.0.0", "@expo/config-types": "^52.0.0", "@expo/image-utils": "^0.6.0", "@expo/json-file": "^9.0.0", "@react-native/normalize-colors": "0.76.2", "debug": "^4.3.1", "fs-extra": "^9.0.0", "resolve-from": "^5.0.0", "semver": "^7.6.0", "xml2js": "0.6.0"}, "dependencies": {"@expo/json-file": {"version": "9.1.4", "dev": true, "requires": {"@babel/code-frame": "~7.10.4", "json5": "^2.2.3"}}, "fs-extra": {"version": "9.1.0", "dev": true, "requires": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}}, "semver": {"version": "7.7.2", "dev": true}}}, "@oclif/core": {"version": "1.26.2", "dev": true, "requires": {"@oclif/linewrap": "^1.0.0", "@oclif/screen": "^3.0.4", "ansi-escapes": "^4.3.2", "ansi-styles": "^4.3.0", "cardinal": "^2.1.1", "chalk": "^4.1.2", "clean-stack": "^3.0.1", "cli-progress": "^3.10.0", "debug": "^4.3.4", "ejs": "^3.1.6", "fs-extra": "^9.1.0", "get-package-type": "^0.1.0", "globby": "^11.1.0", "hyperlinker": "^1.0.0", "indent-string": "^4.0.0", "is-wsl": "^2.2.0", "js-yaml": "^3.14.1", "natural-orderby": "^2.0.3", "object-treeify": "^1.1.33", "password-prompt": "^1.1.2", "semver": "^7.3.7", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "supports-color": "^8.1.1", "supports-hyperlinks": "^2.2.0", "tslib": "^2.4.1", "widest-line": "^3.1.0", "wrap-ansi": "^7.0.0"}, "dependencies": {"fs-extra": {"version": "9.1.0", "dev": true, "requires": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}}}}, "@react-native/normalize-colors": {"version": "0.76.2", "dev": true}, "@urql/core": {"version": "4.0.11", "dev": true, "requires": {"@0no-co/graphql.web": "^1.0.1", "wonka": "^6.3.2"}}, "@urql/exchange-retry": {"version": "1.2.0", "dev": true, "requires": {"@urql/core": ">=4.0.0", "wonka": "^6.3.2"}}, "@xmldom/xmldom": {"version": "0.7.13", "dev": true}, "agent-base": {"version": "6.0.2", "dev": true, "requires": {"debug": "4"}}, "chownr": {"version": "2.0.0", "dev": true}, "color-convert": {"version": "1.9.3", "dev": true, "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "dev": true}, "diff": {"version": "7.0.0", "dev": true}, "dotenv": {"version": "16.3.1", "dev": true}, "escape-string-regexp": {"version": "1.0.5", "dev": true}, "has-flag": {"version": "3.0.0", "dev": true}, "https-proxy-agent": {"version": "5.0.1", "dev": true, "requires": {"agent-base": "6", "debug": "4"}}, "joi": {"version": "17.11.0", "dev": true, "requires": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0", "@sideway/address": "^4.1.3", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}}, "lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "mime": {"version": "3.0.0", "dev": true}, "mimic-fn": {"version": "2.1.0", "dev": true}, "minimatch": {"version": "5.1.2", "dev": true, "requires": {"brace-expansion": "^2.0.1"}}, "minizlib": {"version": "3.0.1", "dev": true, "requires": {"minipass": "^7.0.4", "rimraf": "^5.0.5"}}, "nanoid": {"version": "3.3.8", "dev": true}, "onetime": {"version": "5.1.2", "dev": true, "requires": {"mimic-fn": "^2.1.0"}}, "ora": {"version": "5.1.0", "dev": true, "requires": {"chalk": "^4.1.0", "cli-cursor": "^3.1.0", "cli-spinners": "^2.4.0", "is-interactive": "^1.0.0", "log-symbols": "^4.0.0", "mute-stream": "0.0.8", "strip-ansi": "^6.0.0", "wcwidth": "^1.0.1"}, "dependencies": {"cli-cursor": {"version": "3.1.0", "dev": true, "requires": {"restore-cursor": "^3.1.0"}}}}, "pngjs": {"version": "7.0.0", "dev": true}, "qrcode-terminal": {"version": "0.12.0", "dev": true}, "restore-cursor": {"version": "3.1.0", "dev": true, "requires": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}}, "rimraf": {"version": "5.0.10", "dev": true, "requires": {"glob": "^10.3.7"}}, "semver": {"version": "7.5.4", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "tar": {"version": "6.2.1", "dev": true, "requires": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "dependencies": {"minipass": {"version": "5.0.0", "dev": true}, "minizlib": {"version": "2.1.2", "dev": true, "requires": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "dependencies": {"minipass": {"version": "3.3.6", "dev": true, "requires": {"yallist": "^4.0.0"}}}}}}, "tslib": {"version": "2.6.2", "dev": true}, "uuid": {"version": "9.0.1", "dev": true}, "xmlbuilder": {"version": "14.0.0", "dev": true}}}, "eastasianwidth": {"version": "0.2.0"}, "ecdsa-sig-formatter": {"version": "1.0.11", "dev": true, "requires": {"safe-buffer": "^5.0.1"}}, "ee-first": {"version": "1.1.1"}, "ejs": {"version": "3.1.10", "dev": true, "requires": {"jake": "^10.8.5"}}, "electron-to-chromium": {"version": "1.5.177"}, "emoji-regex": {"version": "8.0.0"}, "encodeurl": {"version": "1.0.2"}, "entities": {"version": "4.5.0"}, "env-editor": {"version": "0.4.2"}, "env-paths": {"version": "2.2.0", "dev": true}, "env-string": {"version": "1.0.1", "dev": true}, "envinfo": {"version": "7.11.0", "dev": true}, "err-code": {"version": "2.0.3", "dev": true}, "error-ex": {"version": "1.3.2", "requires": {"is-arrayish": "^0.2.1"}}, "error-stack-parser": {"version": "2.1.4", "requires": {"stackframe": "^1.3.4"}}, "escalade": {"version": "3.2.0"}, "escape-html": {"version": "1.0.3"}, "escape-string-regexp": {"version": "4.0.0"}, "esprima": {"version": "4.0.1"}, "etag": {"version": "1.8.1"}, "event-target-shim": {"version": "5.0.1"}, "exec-async": {"version": "2.2.0"}, "expo": {"version": "53.0.13", "requires": {"@babel/runtime": "^7.20.0", "@expo/cli": "0.24.15", "@expo/config": "~11.0.10", "@expo/config-plugins": "~10.0.3", "@expo/fingerprint": "0.13.1", "@expo/metro-config": "0.20.15", "@expo/vector-icons": "^14.0.0", "babel-preset-expo": "~13.2.1", "expo-asset": "~11.1.5", "expo-constants": "~17.1.6", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-keep-awake": "~14.1.4", "expo-modules-autolinking": "2.1.12", "expo-modules-core": "2.4.0", "react-native-edge-to-edge": "1.6.0", "whatwg-url-without-unicode": "8.0.0-3"}}, "expo-asset": {"version": "11.1.5", "requires": {"@expo/image-utils": "^0.7.4", "expo-constants": "~17.1.5"}}, "expo-constants": {"version": "17.1.6", "requires": {"@expo/config": "~11.0.9", "@expo/env": "~1.0.5"}}, "expo-file-system": {"version": "18.1.10", "requires": {}}, "expo-font": {"version": "13.3.1", "requires": {"fontfaceobserver": "^2.1.0"}}, "expo-keep-awake": {"version": "14.1.4", "requires": {}}, "expo-modules-autolinking": {"version": "2.1.12", "requires": {"@expo/spawn-async": "^1.7.2", "chalk": "^4.1.0", "commander": "^7.2.0", "find-up": "^5.0.0", "glob": "^10.4.2", "require-from-string": "^2.0.2", "resolve-from": "^5.0.0"}, "dependencies": {"commander": {"version": "7.2.0"}}}, "expo-modules-core": {"version": "2.4.0", "requires": {"invariant": "^2.2.4"}}, "expo-status-bar": {"version": "2.2.3", "requires": {"react-native-edge-to-edge": "1.6.0", "react-native-is-edge-to-edge": "^1.1.6"}}, "exponential-backoff": {"version": "3.1.2"}, "fast-deep-equal": {"version": "3.1.3"}, "fast-fifo": {"version": "1.3.2", "dev": true}, "fast-glob": {"version": "3.3.2", "dev": true, "requires": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}}, "fast-json-stable-stringify": {"version": "2.1.0"}, "fastq": {"version": "1.19.1", "dev": true, "requires": {"reusify": "^1.0.4"}}, "fb-watchman": {"version": "2.0.2", "requires": {"bser": "2.1.1"}}, "fbjs": {"version": "3.0.5", "requires": {"cross-fetch": "^3.1.5", "fbjs-css-vars": "^1.0.0", "loose-envify": "^1.0.0", "object-assign": "^4.1.0", "promise": "^7.1.1", "setimmediate": "^1.0.5", "ua-parser-js": "^1.0.35"}, "dependencies": {"promise": {"version": "7.3.1", "requires": {"asap": "~2.0.3"}}}}, "fbjs-css-vars": {"version": "1.0.2"}, "fetch-retry": {"version": "4.1.1", "dev": true}, "figures": {"version": "3.2.0", "dev": true, "requires": {"escape-string-regexp": "^1.0.5"}, "dependencies": {"escape-string-regexp": {"version": "1.0.5", "dev": true}}}, "filelist": {"version": "1.0.4", "dev": true, "requires": {"minimatch": "^5.0.1"}, "dependencies": {"minimatch": {"version": "5.1.2", "dev": true, "requires": {"brace-expansion": "^2.0.1"}}}}, "fill-range": {"version": "7.1.1", "requires": {"to-regex-range": "^5.0.1"}}, "filter-obj": {"version": "1.1.0"}, "finalhandler": {"version": "1.1.2", "requires": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}, "dependencies": {"debug": {"version": "2.6.9", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0"}, "on-finished": {"version": "2.3.0", "requires": {"ee-first": "1.1.1"}}, "statuses": {"version": "1.5.0"}}}, "find-up": {"version": "5.0.0", "requires": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "dependencies": {"locate-path": {"version": "6.0.0", "requires": {"p-locate": "^5.0.0"}}, "p-locate": {"version": "5.0.0", "requires": {"p-limit": "^3.0.2"}}}}, "flow-enums-runtime": {"version": "0.0.6"}, "follow-redirects": {"version": "1.15.9"}, "fontfaceobserver": {"version": "2.3.0"}, "foreground-child": {"version": "3.3.1", "requires": {"cross-spawn": "^7.0.6", "signal-exit": "^4.0.1"}, "dependencies": {"signal-exit": {"version": "4.1.0"}}}, "form-data": {"version": "4.0.0", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}}, "framer-motion": {"version": "6.5.1", "requires": {"@emotion/is-prop-valid": "^0.8.2", "@motionone/dom": "10.12.0", "framesync": "6.0.1", "hey-listen": "^1.0.8", "popmotion": "11.0.3", "style-value-types": "5.0.0", "tslib": "^2.1.0"}}, "framesync": {"version": "6.0.1", "requires": {"tslib": "^2.1.0"}}, "freeport-async": {"version": "2.0.0"}, "fresh": {"version": "0.5.2"}, "fs-extra": {"version": "11.2.0", "dev": true, "requires": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}}, "fs-minipass": {"version": "2.1.0", "dev": true, "requires": {"minipass": "^3.0.0"}, "dependencies": {"minipass": {"version": "3.3.6", "dev": true, "requires": {"yallist": "^4.0.0"}}}}, "fs.realpath": {"version": "1.0.0"}, "function-bind": {"version": "1.1.2"}, "gensync": {"version": "1.0.0-beta.2"}, "get-caller-file": {"version": "2.0.5"}, "get-package-type": {"version": "0.1.0"}, "getenv": {"version": "1.0.0"}, "glob": {"version": "10.4.5", "requires": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "dependencies": {"minimatch": {"version": "9.0.5", "requires": {"brace-expansion": "^2.0.1"}}}}, "glob-parent": {"version": "5.1.2", "dev": true, "requires": {"is-glob": "^4.0.1"}}, "globals": {"version": "11.12.0"}, "globby": {"version": "11.1.0", "dev": true, "requires": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}}, "golden-fleece": {"version": "1.0.9", "dev": true}, "graceful-fs": {"version": "4.2.11"}, "gradle-to-js": {"version": "2.0.1", "dev": true, "requires": {"lodash.merge": "^4.6.2"}}, "graphql": {"version": "16.8.1", "devOptional": true}, "graphql-tag": {"version": "2.12.6", "dev": true, "requires": {"tslib": "^2.1.0"}, "dependencies": {"tslib": {"version": "2.6.2", "dev": true}}}, "has-flag": {"version": "4.0.0"}, "hasown": {"version": "2.0.2", "requires": {"function-bind": "^1.1.2"}}, "hermes-estree": {"version": "0.25.1"}, "hermes-parser": {"version": "0.25.1", "requires": {"hermes-estree": "0.25.1"}}, "hey-listen": {"version": "1.0.8"}, "hoist-non-react-statics": {"version": "3.3.2", "requires": {"react-is": "^16.7.0"}, "dependencies": {"react-is": {"version": "16.13.1"}}}, "hosted-git-info": {"version": "7.0.2", "requires": {"lru-cache": "^10.0.1"}}, "http-call": {"version": "5.3.0", "dev": true, "requires": {"content-type": "^1.0.4", "debug": "^4.1.1", "is-retry-allowed": "^1.1.0", "is-stream": "^2.0.0", "parse-json": "^4.0.0", "tunnel-agent": "^0.6.0"}}, "http-errors": {"version": "2.0.0", "requires": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}}, "https-proxy-agent": {"version": "7.0.6", "requires": {"agent-base": "^7.1.2", "debug": "4"}}, "hyperlinker": {"version": "1.0.0", "dev": true}, "hyphenate-style-name": {"version": "1.1.0"}, "ieee754": {"version": "1.2.1"}, "ignore": {"version": "5.3.0", "dev": true}, "image-size": {"version": "1.2.1", "requires": {"queue": "6.0.2"}}, "import-fresh": {"version": "2.0.0", "requires": {"caller-path": "^2.0.0", "resolve-from": "^3.0.0"}, "dependencies": {"resolve-from": {"version": "3.0.0"}}}, "imurmurhash": {"version": "0.1.4"}, "indent-string": {"version": "4.0.0", "dev": true}, "inflight": {"version": "1.0.6", "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4"}, "ini": {"version": "1.3.8"}, "inline-style-prefixer": {"version": "7.0.1", "requires": {"css-in-js-utils": "^3.1.0"}}, "invariant": {"version": "2.2.4", "requires": {"loose-envify": "^1.0.0"}}, "is-arrayish": {"version": "0.2.1"}, "is-buffer": {"version": "1.1.6", "dev": true}, "is-core-module": {"version": "2.16.1", "requires": {"hasown": "^2.0.2"}}, "is-directory": {"version": "0.3.1"}, "is-docker": {"version": "2.2.1"}, "is-extglob": {"version": "2.1.1", "dev": true}, "is-fullwidth-code-point": {"version": "3.0.0"}, "is-glob": {"version": "4.0.3", "dev": true, "requires": {"is-extglob": "^2.1.1"}}, "is-interactive": {"version": "1.0.0", "dev": true}, "is-number": {"version": "7.0.0"}, "is-plain-obj": {"version": "2.1.0"}, "is-retry-allowed": {"version": "1.2.0", "dev": true}, "is-stream": {"version": "2.0.1", "dev": true}, "is-unicode-supported": {"version": "0.1.0", "dev": true}, "is-wsl": {"version": "2.2.0", "requires": {"is-docker": "^2.0.0"}}, "isexe": {"version": "2.0.0"}, "istanbul-lib-coverage": {"version": "3.2.2"}, "istanbul-lib-instrument": {"version": "5.2.1", "requires": {"@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0", "semver": "^6.3.0"}, "dependencies": {"semver": {"version": "6.3.1"}}}, "jackspeak": {"version": "3.4.3", "requires": {"@isaacs/cliui": "^8.0.2", "@pkgjs/parseargs": "^0.11.0"}}, "jake": {"version": "10.9.2", "dev": true, "requires": {"async": "^3.2.3", "chalk": "^4.0.2", "filelist": "^1.0.4", "minimatch": "^3.1.2"}}, "jest-environment-node": {"version": "29.7.0", "requires": {"@jest/environment": "^29.7.0", "@jest/fake-timers": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "jest-mock": "^29.7.0", "jest-util": "^29.7.0"}}, "jest-get-type": {"version": "29.6.3"}, "jest-haste-map": {"version": "29.7.0", "requires": {"@jest/types": "^29.6.3", "@types/graceful-fs": "^4.1.3", "@types/node": "*", "anymatch": "^3.0.3", "fb-watchman": "^2.0.0", "fsevents": "^2.3.2", "graceful-fs": "^4.2.9", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "jest-worker": "^29.7.0", "micromatch": "^4.0.4", "walker": "^1.0.8"}}, "jest-message-util": {"version": "29.7.0", "requires": {"@babel/code-frame": "^7.12.13", "@jest/types": "^29.6.3", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "micromatch": "^4.0.4", "pretty-format": "^29.7.0", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}}}, "jest-mock": {"version": "29.7.0", "requires": {"@jest/types": "^29.6.3", "@types/node": "*", "jest-util": "^29.7.0"}}, "jest-regex-util": {"version": "29.6.3"}, "jest-util": {"version": "29.7.0", "requires": {"@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}}, "jest-validate": {"version": "29.7.0", "requires": {"@jest/types": "^29.6.3", "camelcase": "^6.2.0", "chalk": "^4.0.0", "jest-get-type": "^29.6.3", "leven": "^3.1.0", "pretty-format": "^29.7.0"}}, "jest-worker": {"version": "29.7.0", "requires": {"@types/node": "*", "jest-util": "^29.7.0", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}}, "jimp-compact": {"version": "0.16.1"}, "jks-js": {"version": "1.1.0", "dev": true, "requires": {"node-forge": "^1.3.1", "node-int64": "^0.4.0", "node-rsa": "^1.1.1"}}, "joi": {"version": "17.13.3", "dev": true, "requires": {"@hapi/hoek": "^9.3.0", "@hapi/topo": "^5.1.0", "@sideway/address": "^4.1.5", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}}, "join-component": {"version": "1.1.0", "dev": true}, "js-tokens": {"version": "4.0.0"}, "js-yaml": {"version": "3.14.1", "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "jsc-safe-url": {"version": "0.2.4"}, "jsep": {"version": "1.4.0", "dev": true}, "jsesc": {"version": "3.1.0"}, "json-parse-better-errors": {"version": "1.0.2"}, "json-schema-traverse": {"version": "1.0.0", "dev": true}, "json5": {"version": "2.2.3"}, "jsonfile": {"version": "6.1.0", "dev": true, "requires": {"graceful-fs": "^4.1.6", "universalify": "^2.0.0"}}, "jsonwebtoken": {"version": "9.0.0", "dev": true, "requires": {"jws": "^3.2.2", "lodash": "^4.17.21", "ms": "^2.1.1", "semver": "^7.3.8"}, "dependencies": {"lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.5.4", "dev": true, "requires": {"lru-cache": "^6.0.0"}}}}, "jwa": {"version": "1.4.2", "dev": true, "requires": {"buffer-equal-constant-time": "^1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}}, "jws": {"version": "3.2.2", "dev": true, "requires": {"jwa": "^1.4.1", "safe-buffer": "^5.0.1"}}, "keychain": {"version": "1.5.0", "dev": true}, "kleur": {"version": "3.0.3"}, "lan-network": {"version": "0.1.7"}, "leven": {"version": "3.1.0"}, "lighthouse-logger": {"version": "1.4.2", "requires": {"debug": "^2.6.9", "marky": "^1.2.2"}, "dependencies": {"debug": {"version": "2.6.9", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0"}}}, "lightningcss": {"version": "1.27.0", "requires": {"detect-libc": "^1.0.3", "lightningcss-darwin-arm64": "1.27.0", "lightningcss-darwin-x64": "1.27.0", "lightningcss-freebsd-x64": "1.27.0", "lightningcss-linux-arm-gnueabihf": "1.27.0", "lightningcss-linux-arm64-gnu": "1.27.0", "lightningcss-linux-arm64-musl": "1.27.0", "lightningcss-linux-x64-gnu": "1.27.0", "lightningcss-linux-x64-musl": "1.27.0", "lightningcss-win32-arm64-msvc": "1.27.0", "lightningcss-win32-x64-msvc": "1.27.0"}}, "lightningcss-win32-x64-msvc": {"version": "1.27.0", "optional": true}, "lines-and-columns": {"version": "1.2.4"}, "locate-path": {"version": "5.0.0", "requires": {"p-locate": "^4.1.0"}}, "lodash": {"version": "4.17.21", "dev": true}, "lodash.clonedeep": {"version": "4.5.0", "dev": true}, "lodash.debounce": {"version": "4.0.8"}, "lodash.get": {"version": "4.4.2", "dev": true}, "lodash.merge": {"version": "4.6.2", "dev": true}, "lodash.throttle": {"version": "4.1.1"}, "log-symbols": {"version": "4.1.0", "dev": true, "requires": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}}, "loose-envify": {"version": "1.4.0", "requires": {"js-tokens": "^3.0.0 || ^4.0.0"}}, "lru-cache": {"version": "10.4.3"}, "make-error": {"version": "1.3.6", "dev": true}, "makeerror": {"version": "1.0.12", "requires": {"tmpl": "1.0.5"}}, "marky": {"version": "1.3.0"}, "md5": {"version": "2.3.0", "dev": true, "requires": {"charenc": "0.0.2", "crypt": "0.0.2", "is-buffer": "~1.1.6"}}, "mdn-data": {"version": "2.0.14"}, "memoize-one": {"version": "5.2.1"}, "merge-options": {"version": "3.0.4", "requires": {"is-plain-obj": "^2.1.0"}}, "merge-stream": {"version": "2.0.0"}, "merge2": {"version": "1.4.1", "dev": true}, "metro": {"version": "0.82.4", "requires": {"@babel/code-frame": "^7.24.7", "@babel/core": "^7.25.2", "@babel/generator": "^7.25.0", "@babel/parser": "^7.25.3", "@babel/template": "^7.25.0", "@babel/traverse": "^7.25.3", "@babel/types": "^7.25.2", "accepts": "^1.3.7", "chalk": "^4.0.0", "ci-info": "^2.0.0", "connect": "^3.6.5", "debug": "^4.4.0", "error-stack-parser": "^2.0.6", "flow-enums-runtime": "^0.0.6", "graceful-fs": "^4.2.4", "hermes-parser": "0.28.1", "image-size": "^1.0.2", "invariant": "^2.2.4", "jest-worker": "^29.7.0", "jsc-safe-url": "^0.2.2", "lodash.throttle": "^4.1.1", "metro-babel-transformer": "0.82.4", "metro-cache": "0.82.4", "metro-cache-key": "0.82.4", "metro-config": "0.82.4", "metro-core": "0.82.4", "metro-file-map": "0.82.4", "metro-resolver": "0.82.4", "metro-runtime": "0.82.4", "metro-source-map": "0.82.4", "metro-symbolicate": "0.82.4", "metro-transform-plugins": "0.82.4", "metro-transform-worker": "0.82.4", "mime-types": "^2.1.27", "nullthrows": "^1.1.1", "serialize-error": "^2.1.0", "source-map": "^0.5.6", "throat": "^5.0.0", "ws": "^7.5.10", "yargs": "^17.6.2"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "ci-info": {"version": "2.0.0"}, "hermes-estree": {"version": "0.28.1"}, "hermes-parser": {"version": "0.28.1", "requires": {"hermes-estree": "0.28.1"}}, "ws": {"version": "7.5.10", "requires": {}}}}, "metro-babel-transformer": {"version": "0.82.4", "requires": {"@babel/core": "^7.25.2", "flow-enums-runtime": "^0.0.6", "hermes-parser": "0.28.1", "nullthrows": "^1.1.1"}, "dependencies": {"hermes-estree": {"version": "0.28.1"}, "hermes-parser": {"version": "0.28.1", "requires": {"hermes-estree": "0.28.1"}}}}, "metro-cache": {"version": "0.82.4", "requires": {"exponential-backoff": "^3.1.1", "flow-enums-runtime": "^0.0.6", "https-proxy-agent": "^7.0.5", "metro-core": "0.82.4"}}, "metro-cache-key": {"version": "0.82.4", "requires": {"flow-enums-runtime": "^0.0.6"}}, "metro-config": {"version": "0.82.4", "requires": {"connect": "^3.6.5", "cosmiconfig": "^5.0.5", "flow-enums-runtime": "^0.0.6", "jest-validate": "^29.7.0", "metro": "0.82.4", "metro-cache": "0.82.4", "metro-core": "0.82.4", "metro-runtime": "0.82.4"}}, "metro-core": {"version": "0.82.4", "requires": {"flow-enums-runtime": "^0.0.6", "lodash.throttle": "^4.1.1", "metro-resolver": "0.82.4"}}, "metro-file-map": {"version": "0.82.4", "requires": {"debug": "^4.4.0", "fb-watchman": "^2.0.0", "flow-enums-runtime": "^0.0.6", "graceful-fs": "^4.2.4", "invariant": "^2.2.4", "jest-worker": "^29.7.0", "micromatch": "^4.0.4", "nullthrows": "^1.1.1", "walker": "^1.0.7"}}, "metro-minify-terser": {"version": "0.82.4", "requires": {"flow-enums-runtime": "^0.0.6", "terser": "^5.15.0"}}, "metro-resolver": {"version": "0.82.4", "requires": {"flow-enums-runtime": "^0.0.6"}}, "metro-runtime": {"version": "0.82.4", "requires": {"@babel/runtime": "^7.25.0", "flow-enums-runtime": "^0.0.6"}}, "metro-source-map": {"version": "0.82.4", "requires": {"@babel/traverse": "^7.25.3", "@babel/traverse--for-generate-function-map": "npm:@babel/traverse@^7.25.3", "@babel/types": "^7.25.2", "flow-enums-runtime": "^0.0.6", "invariant": "^2.2.4", "metro-symbolicate": "0.82.4", "nullthrows": "^1.1.1", "ob1": "0.82.4", "source-map": "^0.5.6", "vlq": "^1.0.0"}}, "metro-symbolicate": {"version": "0.82.4", "requires": {"flow-enums-runtime": "^0.0.6", "invariant": "^2.2.4", "metro-source-map": "0.82.4", "nullthrows": "^1.1.1", "source-map": "^0.5.6", "vlq": "^1.0.0"}}, "metro-transform-plugins": {"version": "0.82.4", "requires": {"@babel/core": "^7.25.2", "@babel/generator": "^7.25.0", "@babel/template": "^7.25.0", "@babel/traverse": "^7.25.3", "flow-enums-runtime": "^0.0.6", "nullthrows": "^1.1.1"}}, "metro-transform-worker": {"version": "0.82.4", "requires": {"@babel/core": "^7.25.2", "@babel/generator": "^7.25.0", "@babel/parser": "^7.25.3", "@babel/types": "^7.25.2", "flow-enums-runtime": "^0.0.6", "metro": "0.82.4", "metro-babel-transformer": "0.82.4", "metro-cache": "0.82.4", "metro-cache-key": "0.82.4", "metro-minify-terser": "0.82.4", "metro-source-map": "0.82.4", "metro-transform-plugins": "0.82.4", "nullthrows": "^1.1.1"}}, "micromatch": {"version": "4.0.8", "requires": {"braces": "^3.0.3", "picomatch": "^2.3.1"}}, "mime": {"version": "1.6.0"}, "mime-db": {"version": "1.52.0"}, "mime-types": {"version": "2.1.35", "requires": {"mime-db": "1.52.0"}}, "mimic-fn": {"version": "1.2.0"}, "minimatch": {"version": "3.1.2", "requires": {"brace-expansion": "^1.1.7"}, "dependencies": {"brace-expansion": {"version": "1.1.12", "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}}}, "minimist": {"version": "1.2.8"}, "minipass": {"version": "7.1.2"}, "minizlib": {"version": "3.0.2", "requires": {"minipass": "^7.1.2"}}, "mkdirp": {"version": "1.0.4"}, "moment": {"version": "2.30.1", "dev": true, "optional": true}, "moti": {"version": "0.30.0", "requires": {"framer-motion": "^6.5.1"}}, "ms": {"version": "2.1.3"}, "multipasta": {"version": "0.2.5", "dev": true}, "mute-stream": {"version": "0.0.8", "dev": true}, "mv": {"version": "2.1.1", "dev": true, "optional": true, "requires": {"mkdirp": "~0.5.1", "ncp": "~2.0.0", "rimraf": "~2.4.0"}, "dependencies": {"glob": {"version": "6.0.4", "dev": true, "optional": true, "requires": {"inflight": "^1.0.4", "inherits": "2", "minimatch": "2 || 3", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "mkdirp": {"version": "0.5.6", "dev": true, "optional": true, "requires": {"minimist": "^1.2.6"}}, "rimraf": {"version": "2.4.5", "dev": true, "optional": true, "requires": {"glob": "^6.0.1"}}}}, "mz": {"version": "2.7.0", "requires": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}}, "nan": {"version": "2.22.2", "dev": true, "optional": true}, "nanoid": {"version": "3.3.11"}, "natural-orderby": {"version": "2.0.3", "dev": true}, "ncp": {"version": "2.0.0", "dev": true, "optional": true}, "negotiator": {"version": "0.6.3"}, "nested-error-stacks": {"version": "2.0.1"}, "node-fetch": {"version": "2.6.7", "dev": true, "requires": {"whatwg-url": "^5.0.0"}}, "node-forge": {"version": "1.3.1"}, "node-int64": {"version": "0.4.0"}, "node-releases": {"version": "2.0.19"}, "node-rsa": {"version": "1.1.1", "dev": true, "requires": {"asn1": "^0.2.4"}}, "node-stream-zip": {"version": "1.15.0", "dev": true}, "normalize-path": {"version": "3.0.0"}, "npm-package-arg": {"version": "11.0.3", "requires": {"hosted-git-info": "^7.0.0", "proc-log": "^4.0.0", "semver": "^7.3.5", "validate-npm-package-name": "^5.0.0"}, "dependencies": {"lru-cache": {"version": "6.0.0", "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.5.4", "requires": {"lru-cache": "^6.0.0"}}}}, "nth-check": {"version": "2.1.1", "requires": {"boolbase": "^1.0.0"}}, "nullthrows": {"version": "1.1.1"}, "ob1": {"version": "0.82.4", "requires": {"flow-enums-runtime": "^0.0.6"}}, "object-assign": {"version": "4.1.1"}, "object-treeify": {"version": "1.1.33", "dev": true}, "on-finished": {"version": "2.4.1", "requires": {"ee-first": "1.1.1"}}, "on-headers": {"version": "1.0.2"}, "once": {"version": "1.4.0", "requires": {"wrappy": "1"}}, "onetime": {"version": "2.0.1", "requires": {"mimic-fn": "^1.0.0"}}, "open": {"version": "7.4.2", "requires": {"is-docker": "^2.0.0", "is-wsl": "^2.1.1"}}, "ora": {"version": "3.4.0", "requires": {"chalk": "^2.4.2", "cli-cursor": "^2.1.0", "cli-spinners": "^2.0.0", "log-symbols": "^2.2.0", "strip-ansi": "^5.2.0", "wcwidth": "^1.0.1"}, "dependencies": {"ansi-regex": {"version": "4.1.1"}, "ansi-styles": {"version": "3.2.1", "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.2", "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "color-convert": {"version": "1.9.3", "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3"}, "escape-string-regexp": {"version": "1.0.5"}, "has-flag": {"version": "3.0.0"}, "log-symbols": {"version": "2.2.0", "requires": {"chalk": "^2.0.1"}}, "strip-ansi": {"version": "5.2.0", "requires": {"ansi-regex": "^4.1.0"}}, "supports-color": {"version": "5.5.0", "requires": {"has-flag": "^3.0.0"}}}}, "p-limit": {"version": "3.1.0", "requires": {"yocto-queue": "^0.1.0"}}, "p-locate": {"version": "4.1.0", "requires": {"p-limit": "^2.2.0"}, "dependencies": {"p-limit": {"version": "2.3.0", "requires": {"p-try": "^2.0.0"}}}}, "p-try": {"version": "2.2.0"}, "package-json-from-dist": {"version": "1.0.1"}, "parse-json": {"version": "4.0.0", "requires": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}}, "parse-png": {"version": "2.1.0", "requires": {"pngjs": "^3.3.0"}}, "parseurl": {"version": "1.3.3"}, "password-prompt": {"version": "1.1.3", "dev": true, "requires": {"ansi-escapes": "^4.3.2", "cross-spawn": "^7.0.3"}}, "path-exists": {"version": "4.0.0"}, "path-is-absolute": {"version": "1.0.1"}, "path-key": {"version": "3.1.1"}, "path-parse": {"version": "1.0.7"}, "path-scurry": {"version": "1.11.1", "requires": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}}, "path-type": {"version": "4.0.0", "dev": true}, "picocolors": {"version": "1.1.1"}, "picomatch": {"version": "2.3.1"}, "pirates": {"version": "4.0.7"}, "pkg-dir": {"version": "4.2.0", "dev": true, "requires": {"find-up": "^4.0.0"}, "dependencies": {"find-up": {"version": "4.1.0", "dev": true, "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}}}, "plist": {"version": "3.1.0", "requires": {"@xmldom/xmldom": "^0.8.8", "base64-js": "^1.5.1", "xmlbuilder": "^15.1.1"}}, "pngjs": {"version": "3.4.0"}, "popmotion": {"version": "11.0.3", "requires": {"framesync": "6.0.1", "hey-listen": "^1.0.8", "style-value-types": "5.0.0", "tslib": "^2.1.0"}}, "postcss": {"version": "8.4.49", "requires": {"nanoid": "^3.3.7", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}}, "postcss-value-parser": {"version": "4.2.0"}, "pretty-bytes": {"version": "5.6.0"}, "pretty-format": {"version": "29.7.0", "requires": {"@jest/schemas": "^29.6.3", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "dependencies": {"ansi-styles": {"version": "5.2.0"}}}, "proc-log": {"version": "4.2.0"}, "progress": {"version": "2.0.3"}, "promise": {"version": "8.3.0", "requires": {"asap": "~2.0.6"}}, "promise-limit": {"version": "2.7.0", "dev": true}, "promise-retry": {"version": "2.0.1", "dev": true, "requires": {"err-code": "^2.0.2", "retry": "^0.12.0"}}, "prompts": {"version": "2.4.2", "requires": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}}, "proxy-from-env": {"version": "1.1.0"}, "punycode": {"version": "2.3.1"}, "qrcode-terminal": {"version": "0.11.0"}, "query-string": {"version": "7.1.3", "requires": {"decode-uri-component": "^0.2.2", "filter-obj": "^1.1.0", "split-on-first": "^1.0.0", "strict-uri-encode": "^2.0.0"}}, "queue": {"version": "6.0.2", "requires": {"inherits": "~2.0.3"}}, "queue-microtask": {"version": "1.2.3", "dev": true}, "range-parser": {"version": "1.2.1"}, "rc": {"version": "1.2.8", "requires": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}}, "react": {"version": "19.0.0"}, "react-devtools-core": {"version": "6.1.3", "requires": {"shell-quote": "^1.6.1", "ws": "^7"}, "dependencies": {"ws": {"version": "7.5.10", "requires": {}}}}, "react-dom": {"version": "19.0.0", "requires": {"scheduler": "^0.25.0"}}, "react-freeze": {"version": "1.0.4", "requires": {}}, "react-is": {"version": "18.3.1"}, "react-native": {"version": "0.79.4", "requires": {"@jest/create-cache-key-function": "^29.7.0", "@react-native/assets-registry": "0.79.4", "@react-native/codegen": "0.79.4", "@react-native/community-cli-plugin": "0.79.4", "@react-native/gradle-plugin": "0.79.4", "@react-native/js-polyfills": "0.79.4", "@react-native/normalize-colors": "0.79.4", "@react-native/virtualized-lists": "0.79.4", "abort-controller": "^3.0.0", "anser": "^1.4.9", "ansi-regex": "^5.0.0", "babel-jest": "^29.7.0", "babel-plugin-syntax-hermes-parser": "0.25.1", "base64-js": "^1.5.1", "chalk": "^4.0.0", "commander": "^12.0.0", "event-target-shim": "^5.0.1", "flow-enums-runtime": "^0.0.6", "glob": "^7.1.1", "invariant": "^2.2.4", "jest-environment-node": "^29.7.0", "memoize-one": "^5.0.0", "metro-runtime": "^0.82.0", "metro-source-map": "^0.82.0", "nullthrows": "^1.1.1", "pretty-format": "^29.7.0", "promise": "^8.3.0", "react-devtools-core": "^6.1.1", "react-refresh": "^0.14.0", "regenerator-runtime": "^0.13.2", "scheduler": "0.25.0", "semver": "^7.1.3", "stacktrace-parser": "^0.1.10", "whatwg-fetch": "^3.0.0", "ws": "^6.2.3", "yargs": "^17.6.2"}, "dependencies": {"commander": {"version": "12.1.0"}, "glob": {"version": "7.2.3", "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}}}, "react-native-edge-to-edge": {"version": "1.6.0", "requires": {}}, "react-native-gesture-handler": {"version": "2.24.0", "requires": {"@egjs/hammerjs": "^2.0.17", "hoist-non-react-statics": "^3.3.0", "invariant": "^2.2.4"}}, "react-native-is-edge-to-edge": {"version": "1.1.7", "requires": {}}, "react-native-reanimated": {"version": "3.17.5", "requires": {"@babel/plugin-transform-arrow-functions": "^7.0.0-0", "@babel/plugin-transform-class-properties": "^7.0.0-0", "@babel/plugin-transform-classes": "^7.0.0-0", "@babel/plugin-transform-nullish-coalescing-operator": "^7.0.0-0", "@babel/plugin-transform-optional-chaining": "^7.0.0-0", "@babel/plugin-transform-shorthand-properties": "^7.0.0-0", "@babel/plugin-transform-template-literals": "^7.0.0-0", "@babel/plugin-transform-unicode-regex": "^7.0.0-0", "@babel/preset-typescript": "^7.16.7", "convert-source-map": "^2.0.0", "invariant": "^2.2.4", "react-native-is-edge-to-edge": "1.1.7"}}, "react-native-safe-area-context": {"version": "5.4.0", "requires": {}}, "react-native-screens": {"version": "4.11.1", "requires": {"react-freeze": "^1.0.0", "react-native-is-edge-to-edge": "^1.1.7", "warn-once": "^0.1.0"}}, "react-native-svg": {"version": "15.11.2", "requires": {"css-select": "^5.1.0", "css-tree": "^1.1.3", "warn-once": "0.1.1"}}, "react-native-web": {"version": "0.20.0", "requires": {"@babel/runtime": "^7.18.6", "@react-native/normalize-colors": "^0.74.1", "fbjs": "^3.0.4", "inline-style-prefixer": "^7.0.1", "memoize-one": "^6.0.0", "nullthrows": "^1.1.1", "postcss-value-parser": "^4.2.0", "styleq": "^0.1.3"}, "dependencies": {"@react-native/normalize-colors": {"version": "0.74.89"}, "memoize-one": {"version": "6.0.0"}}}, "react-refresh": {"version": "0.14.2"}, "redeyed": {"version": "2.1.1", "dev": true, "requires": {"esprima": "~4.0.0"}}, "regenerate": {"version": "1.4.2"}, "regenerate-unicode-properties": {"version": "10.2.0", "requires": {"regenerate": "^1.4.2"}}, "regenerator-runtime": {"version": "0.13.11"}, "regexpu-core": {"version": "6.2.0", "requires": {"regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.2.0", "regjsgen": "^0.8.0", "regjsparser": "^0.12.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}}, "regjsgen": {"version": "0.8.0"}, "regjsparser": {"version": "0.12.0", "requires": {"jsesc": "~3.0.2"}, "dependencies": {"jsesc": {"version": "3.0.2"}}}, "remove-trailing-slash": {"version": "0.1.1", "dev": true}, "require-directory": {"version": "2.1.1"}, "require-from-string": {"version": "2.0.2"}, "requireg": {"version": "0.2.2", "requires": {"nested-error-stacks": "~2.0.1", "rc": "~1.2.7", "resolve": "~1.7.1"}, "dependencies": {"resolve": {"version": "1.7.1", "requires": {"path-parse": "^1.0.5"}}}}, "resolve": {"version": "1.22.10", "requires": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}}, "resolve-from": {"version": "5.0.0"}, "resolve-workspace-root": {"version": "2.0.0"}, "resolve.exports": {"version": "2.0.3"}, "restore-cursor": {"version": "2.0.0", "requires": {"onetime": "^2.0.0", "signal-exit": "^3.0.2"}}, "retry": {"version": "0.12.0", "dev": true}, "reusify": {"version": "1.1.0", "dev": true}, "rimraf": {"version": "3.0.2", "requires": {"glob": "^7.1.3"}, "dependencies": {"glob": {"version": "7.2.3", "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}}}, "run-parallel": {"version": "1.2.0", "dev": true, "requires": {"queue-microtask": "^1.2.2"}}, "safe-buffer": {"version": "5.2.1"}, "safe-json-stringify": {"version": "1.2.0", "dev": true, "optional": true}, "safer-buffer": {"version": "2.1.2", "dev": true}, "sax": {"version": "1.4.1"}, "scheduler": {"version": "0.25.0"}, "semver": {"version": "7.7.2"}, "send": {"version": "0.19.0", "requires": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "requires": {"ms": "2.0.0"}, "dependencies": {"ms": {"version": "2.0.0"}}}}}, "serialize-error": {"version": "2.1.0"}, "serve-static": {"version": "1.16.2", "requires": {"encodeurl": "~2.0.0", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.19.0"}, "dependencies": {"encodeurl": {"version": "2.0.0"}}}, "set-interval-async": {"version": "3.0.3", "dev": true}, "setimmediate": {"version": "1.0.5"}, "setprototypeof": {"version": "1.2.0"}, "shebang-command": {"version": "2.0.0", "requires": {"shebang-regex": "^3.0.0"}}, "shebang-regex": {"version": "3.0.0"}, "shell-quote": {"version": "1.8.3"}, "signal-exit": {"version": "3.0.7"}, "simple-plist": {"version": "1.3.1", "requires": {"bplist-creator": "0.1.0", "bplist-parser": "0.3.1", "plist": "^3.0.5"}, "dependencies": {"bplist-parser": {"version": "0.3.1", "requires": {"big-integer": "1.6.x"}}}}, "simple-swizzle": {"version": "0.2.2", "requires": {"is-arrayish": "^0.3.1"}, "dependencies": {"is-arrayish": {"version": "0.3.2"}}}, "sisteransi": {"version": "1.0.5"}, "slash": {"version": "3.0.0"}, "slice-ansi": {"version": "4.0.0", "dev": true, "requires": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}}, "slugify": {"version": "1.6.6"}, "source-map": {"version": "0.5.7"}, "source-map-js": {"version": "1.2.1"}, "source-map-support": {"version": "0.5.21", "requires": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}, "dependencies": {"source-map": {"version": "0.6.1"}}}, "split": {"version": "1.0.1", "dev": true, "requires": {"through": "2"}}, "split-on-first": {"version": "1.1.0"}, "sprintf-js": {"version": "1.0.3"}, "stack-utils": {"version": "2.0.6", "requires": {"escape-string-regexp": "^2.0.0"}, "dependencies": {"escape-string-regexp": {"version": "2.0.0"}}}, "stackframe": {"version": "1.3.4"}, "stacktrace-parser": {"version": "0.1.11", "requires": {"type-fest": "^0.7.1"}}, "statuses": {"version": "2.0.1"}, "stream-buffers": {"version": "2.2.0"}, "streamx": {"version": "2.22.1", "dev": true, "requires": {"bare-events": "^2.2.0", "fast-fifo": "^1.3.2", "text-decoder": "^1.1.0"}}, "strict-uri-encode": {"version": "2.0.0"}, "string-width": {"version": "4.2.3", "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "string-width-cjs": {"version": "npm:string-width@4.2.3", "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "strip-ansi": {"version": "6.0.1", "requires": {"ansi-regex": "^5.0.1"}}, "strip-ansi-cjs": {"version": "npm:strip-ansi@6.0.1", "requires": {"ansi-regex": "^5.0.1"}}, "strip-json-comments": {"version": "2.0.1"}, "structured-headers": {"version": "0.4.1"}, "style-value-types": {"version": "5.0.0", "requires": {"hey-listen": "^1.0.8", "tslib": "^2.1.0"}}, "styleq": {"version": "0.1.3"}, "sucrase": {"version": "3.35.0", "requires": {"@jridgewell/gen-mapping": "^0.3.2", "commander": "^4.0.0", "glob": "^10.3.10", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "dependencies": {"commander": {"version": "4.1.1"}}}, "sudo-prompt": {"version": "9.1.1", "dev": true}, "supports-color": {"version": "8.1.1", "requires": {"has-flag": "^4.0.0"}}, "supports-hyperlinks": {"version": "2.3.0", "requires": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "dependencies": {"supports-color": {"version": "7.2.0", "requires": {"has-flag": "^4.0.0"}}}}, "supports-preserve-symlinks-flag": {"version": "1.0.0"}, "tar": {"version": "7.4.3", "requires": {"@isaacs/fs-minipass": "^4.0.0", "chownr": "^3.0.0", "minipass": "^7.1.2", "minizlib": "^3.0.1", "mkdirp": "^3.0.1", "yallist": "^5.0.0"}, "dependencies": {"mkdirp": {"version": "3.0.1"}, "yallist": {"version": "5.0.0"}}}, "tar-stream": {"version": "3.1.7", "dev": true, "requires": {"b4a": "^1.6.4", "fast-fifo": "^1.2.0", "streamx": "^2.15.0"}}, "temp-dir": {"version": "2.0.0"}, "terminal-link": {"version": "2.1.1", "requires": {"ansi-escapes": "^4.2.1", "supports-hyperlinks": "^2.0.0"}}, "terser": {"version": "5.43.1", "requires": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.14.0", "commander": "^2.20.0", "source-map-support": "~0.5.20"}}, "test-exclude": {"version": "6.0.0", "requires": {"@istanbuljs/schema": "^0.1.2", "glob": "^7.1.4", "minimatch": "^3.0.4"}, "dependencies": {"glob": {"version": "7.2.3", "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}}}, "text-decoder": {"version": "1.2.3", "dev": true, "requires": {"b4a": "^1.6.4"}}, "thenify": {"version": "3.3.1", "requires": {"any-promise": "^1.0.0"}}, "thenify-all": {"version": "1.6.0", "requires": {"thenify": ">= 3.1.0 < 4"}}, "this-file": {"version": "2.0.3", "dev": true}, "throat": {"version": "5.0.0"}, "through": {"version": "2.3.8", "dev": true}, "tmpl": {"version": "1.0.5"}, "to-regex-range": {"version": "5.0.1", "requires": {"is-number": "^7.0.0"}}, "toidentifier": {"version": "1.0.1"}, "tr46": {"version": "0.0.3"}, "ts-interface-checker": {"version": "0.1.13"}, "ts-node": {"version": "10.9.2", "dev": true, "requires": {"@cspotcode/source-map-support": "^0.8.0", "@tsconfig/node10": "^1.0.7", "@tsconfig/node12": "^1.0.7", "@tsconfig/node14": "^1.0.0", "@tsconfig/node16": "^1.0.2", "acorn": "^8.4.1", "acorn-walk": "^8.1.1", "arg": "^4.1.0", "create-require": "^1.1.0", "diff": "^4.0.1", "make-error": "^1.1.1", "v8-compile-cache-lib": "^3.0.1", "yn": "3.1.1"}, "dependencies": {"arg": {"version": "4.1.3", "dev": true}}}, "tslib": {"version": "2.8.1"}, "tunnel-agent": {"version": "0.6.0", "dev": true, "requires": {"safe-buffer": "^5.0.1"}}, "turndown": {"version": "7.1.2", "dev": true, "requires": {"domino": "^2.1.6"}}, "type-detect": {"version": "4.0.8"}, "type-fest": {"version": "0.7.1"}, "typescript": {"version": "5.8.3", "dev": true}, "ua-parser-js": {"version": "1.0.40"}, "undici": {"version": "6.21.3"}, "undici-types": {"version": "7.8.0"}, "unicode-canonical-property-names-ecmascript": {"version": "2.0.1"}, "unicode-match-property-ecmascript": {"version": "2.0.0", "requires": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}}, "unicode-match-property-value-ecmascript": {"version": "2.2.0"}, "unicode-property-aliases-ecmascript": {"version": "2.1.0"}, "unique-string": {"version": "2.0.0", "requires": {"crypto-random-string": "^2.0.0"}}, "universalify": {"version": "2.0.1", "dev": true}, "unpipe": {"version": "1.0.0"}, "untildify": {"version": "4.0.0", "dev": true}, "update-browserslist-db": {"version": "1.1.3", "requires": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}}, "uri-js": {"version": "4.4.1", "dev": true, "requires": {"punycode": "^2.1.0"}}, "use-latest-callback": {"version": "0.2.4", "requires": {}}, "use-sync-external-store": {"version": "1.5.0", "requires": {}}, "utils-merge": {"version": "1.0.1"}, "uuid": {"version": "8.3.2", "dev": true}, "v8-compile-cache-lib": {"version": "3.0.1", "dev": true}, "validate-npm-package-name": {"version": "5.0.1"}, "vary": {"version": "1.1.2"}, "vlq": {"version": "1.0.1"}, "walker": {"version": "1.0.8", "requires": {"makeerror": "1.0.12"}}, "warn-once": {"version": "0.1.1"}, "wcwidth": {"version": "1.0.1", "requires": {"defaults": "^1.0.3"}}, "webidl-conversions": {"version": "5.0.0"}, "whatwg-fetch": {"version": "3.6.20"}, "whatwg-url": {"version": "5.0.0", "requires": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "dependencies": {"webidl-conversions": {"version": "3.0.1"}}}, "whatwg-url-without-unicode": {"version": "8.0.0-3", "requires": {"buffer": "^5.4.3", "punycode": "^2.1.1", "webidl-conversions": "^5.0.0"}}, "which": {"version": "2.0.2", "requires": {"isexe": "^2.0.0"}}, "widest-line": {"version": "3.1.0", "dev": true, "requires": {"string-width": "^4.0.0"}}, "wonka": {"version": "6.3.5"}, "wordwrap": {"version": "1.0.0", "dev": true}, "wrap-ansi": {"version": "7.0.0", "requires": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}}, "wrap-ansi-cjs": {"version": "npm:wrap-ansi@7.0.0", "requires": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}}, "wrappy": {"version": "1.0.2"}, "write-file-atomic": {"version": "2.4.3", "dev": true, "requires": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}}, "ws": {"version": "6.2.3", "requires": {"async-limiter": "~1.0.0"}}, "xcode": {"version": "3.0.1", "requires": {"simple-plist": "^1.1.0", "uuid": "^7.0.3"}, "dependencies": {"uuid": {"version": "7.0.3"}}}, "xml2js": {"version": "0.6.0", "requires": {"sax": ">=0.6.0", "xmlbuilder": "~11.0.0"}, "dependencies": {"xmlbuilder": {"version": "11.0.1"}}}, "xmlbuilder": {"version": "15.1.1"}, "y18n": {"version": "5.0.8"}, "yallist": {"version": "4.0.0"}, "yaml": {"version": "2.6.0", "dev": true}, "yargs": {"version": "17.7.2", "requires": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}}, "yargs-parser": {"version": "21.1.1"}, "yn": {"version": "3.1.1", "dev": true}, "yocto-queue": {"version": "0.1.0"}, "zod": {"version": "3.25.67", "dev": true}, "zustand": {"version": "5.0.6", "requires": {}}}}
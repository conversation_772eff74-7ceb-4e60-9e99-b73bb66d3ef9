import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';

import { RootStackParamList } from '../../types';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants';
import { useTheme } from '../../components/common/ThemeProvider';
import TouchableScale from '../../components/ui/TouchableScale';
import PullToRefresh from '../../components/ui/PullToRefresh';
import { mockTwitterPosts } from '../../components/home/<USER>';

type TwitterListScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

interface TwitterPost {
  id: string;
  author: string;
  handle: string;
  content: string;
  timestamp: string;
  likes: number;
  retweets: number;
  verified: boolean;
}

const TwitterListScreen: React.FC = () => {
  const navigation = useNavigation<TwitterListScreenNavigationProp>();
  const { colors } = useTheme();
  const [posts, setPosts] = useState<TwitterPost[]>(mockTwitterPosts);
  const [refreshing, setRefreshing] = useState(false);
  const [expandedPosts, setExpandedPosts] = useState<Set<string>>(new Set());

  const styles = createStyles(colors);

  useEffect(() => {
    navigation.setOptions({
      title: '推特观点',
      headerStyle: {
        backgroundColor: colors.background,
      },
      headerTintColor: colors.text,
      headerLeft: () => (
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.headerButton}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
      ),
    });
  }, [navigation, colors]);

  const onRefresh = async () => {
    setRefreshing(true);
    // 模拟刷新数据
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const handlePostPress = (postId: string) => {
    console.log(`点击推特帖子: ${postId}`);
    // 这里可以实现跳转到推特详情或外部链接
  };

  const togglePostExpansion = (postId: string) => {
    setExpandedPosts(prev => {
      const newSet = new Set(prev);
      if (newSet.has(postId)) {
        newSet.delete(postId);
      } else {
        newSet.add(postId);
      }
      return newSet;
    });
  };

  const isPostExpanded = (postId: string) => expandedPosts.has(postId);

  const shouldShowExpandButton = (content: string) => content.length > 120;

  const renderTwitterPost = ({ item: post }: { item: TwitterPost }) => (
    <View style={styles.postCard}>
      <View style={styles.postHeader}>
        <View style={styles.authorInfo}>
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>
              {post.author.charAt(0).toUpperCase()}
            </Text>
          </View>
          <View style={styles.authorDetails}>
            <View style={styles.authorName}>
              <Text style={styles.authorNameText}>{post.author}</Text>
              {post.verified && (
                <Ionicons name="checkmark-circle" size={16} color="#1DA1F2" />
              )}
            </View>
            <Text style={styles.authorHandle}>@{post.handle}</Text>
          </View>
        </View>
        <Text style={styles.timestamp}>{post.timestamp}</Text>
      </View>

      <View style={styles.contentContainer}>
        <TouchableOpacity
          onPress={() => shouldShowExpandButton(post.content) ? togglePostExpansion(post.id) : handlePostPress(post.id)}
          activeOpacity={0.7}
        >
          <Text
            style={styles.postContent}
            numberOfLines={isPostExpanded(post.id) ? undefined : 3}
            ellipsizeMode="tail"
          >
            {post.content}
          </Text>
        </TouchableOpacity>
        {shouldShowExpandButton(post.content) && (
          <TouchableOpacity
            onPress={() => togglePostExpansion(post.id)}
            style={styles.expandButton}
          >
            <Text style={styles.expandButtonText}>
              {isPostExpanded(post.id) ? '收起' : '展开'}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.postStats}>
        <View style={styles.statItem}>
          <Ionicons name="heart-outline" size={16} color={colors.textSecondary} />
          <Text style={styles.statText}>{post.likes}</Text>
        </View>
        <View style={styles.statItem}>
          <Ionicons name="repeat-outline" size={16} color={colors.textSecondary} />
          <Text style={styles.statText}>{post.retweets}</Text>
        </View>
        <View style={styles.statItem}>
          <Ionicons name="chatbubble-outline" size={16} color={colors.textSecondary} />
          <Text style={styles.statText}>评论</Text>
        </View>
        <View style={styles.statItem}>
          <Ionicons name="share-outline" size={16} color={colors.textSecondary} />
          <Text style={styles.statText}>分享</Text>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        data={posts}
        renderItem={renderTwitterPost}
        keyExtractor={(item) => item.id}
        style={styles.list}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <PullToRefresh refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="logo-twitter" size={48} color="#1DA1F2" />
            <Text style={styles.emptyText}>暂无推特观点</Text>
          </View>
        }
      />
    </SafeAreaView>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    headerButton: {
      padding: SPACING.sm,
      marginLeft: SPACING.xs,
    },
    list: {
      flex: 1,
    },
    listContent: {
      paddingVertical: SPACING.md,
    },
    postCard: {
      marginHorizontal: SPACING.md,
      marginBottom: SPACING.md,
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.lg,
      padding: SPACING.md,
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    postHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: SPACING.md,
    },
    authorInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    avatar: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: '#1DA1F2',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: SPACING.sm,
    },
    avatarText: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#FFFFFF',
    },
    authorDetails: {
      flex: 1,
    },
    authorName: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 2,
    },
    authorNameText: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
      marginRight: SPACING.xs,
    },
    authorHandle: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    timestamp: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    contentContainer: {
      marginBottom: SPACING.md,
    },
    postContent: {
      fontSize: FONT_SIZES.base,
      color: colors.text,
      lineHeight: 20,
    },
    expandButton: {
      alignSelf: 'flex-start',
      marginTop: SPACING.xs,
      paddingVertical: SPACING.xs,
      paddingHorizontal: SPACING.sm,
    },
    expandButtonText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    postStats: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingTop: SPACING.sm,
      borderTopWidth: 1,
      borderTopColor: colors.borderLight,
    },
    statItem: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    statText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginLeft: SPACING.xs,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: SPACING.xl * 2,
    },
    emptyText: {
      fontSize: FONT_SIZES.base,
      color: colors.textSecondary,
      marginTop: SPACING.md,
    },
  });

export default TwitterListScreen;

import { Router } from 'express';
import { pricesController } from '@/controllers/prices';
import { validateQuery, validateParams } from '@/middlewares/validation';
import {
  GetPricesQuerySchema,
  GetPriceHistoryQuerySchema,
} from '@/types';
import { z } from 'zod';

const router: Router = Router();

// Validation schemas for parameters
const CryptoIdParamsSchema = z.object({
  id: z.string().min(1, 'Cryptocurrency ID is required'),
});

const TopCryptosQuerySchema = z.object({
  limit: z.coerce.number().min(1).max(50).default(10),
  vs_currency: z.string().default('usd'),
});

const CryptoDetailQuerySchema = z.object({
  vs_currency: z.string().default('usd'),
});

/**
 * @route GET /api/prices
 * @desc Get cryptocurrency prices with pagination
 * @access Public
 * @query {string} ids - Comma-separated list of coin IDs
 * @query {string} vs_currency - Target currency (default: usd)
 * @query {string} order - Sort order (market_cap_desc, market_cap_asc, volume_desc, volume_asc)
 * @query {number} per_page - Items per page (default: 100, max: 250)
 * @query {number} page - Page number (default: 1)
 * @query {boolean} sparkline - Include sparkline data (default: false)
 */
router.get(
  '/',
  validateQuery(GetPricesQuerySchema),
  pricesController.getPrices
);

/**
 * @route GET /api/prices/trending
 * @desc Get trending cryptocurrencies
 * @access Public
 */
router.get(
  '/trending',
  pricesController.getTrendingCryptocurrencies
);

/**
 * @route GET /api/prices/top
 * @desc Get top cryptocurrencies by market cap
 * @access Public
 * @query {number} limit - Number of cryptocurrencies (default: 10, max: 50)
 * @query {string} vs_currency - Target currency (default: usd)
 */
router.get(
  '/top',
  validateQuery(TopCryptosQuerySchema),
  pricesController.getTopCryptocurrencies
);

/**
 * @route GET /api/prices/market-overview
 * @desc Get market overview statistics
 * @access Public
 * @query {string} vs_currency - Target currency (default: usd)
 */
router.get(
  '/market-overview',
  validateQuery(z.object({ vs_currency: z.string().default('usd') })),
  pricesController.getMarketOverview
);

/**
 * @route GET /api/prices/:id
 * @desc Get specific cryptocurrency price data
 * @access Public
 * @param {string} id - Cryptocurrency ID (e.g., bitcoin, ethereum)
 * @query {string} vs_currency - Target currency (default: usd)
 */
router.get(
  '/:id',
  validateParams(CryptoIdParamsSchema),
  validateQuery(CryptoDetailQuerySchema),
  pricesController.getCryptocurrencyById
);

/**
 * @route GET /api/prices/:id/history
 * @desc Get price history for a cryptocurrency
 * @access Public
 * @param {string} id - Cryptocurrency ID
 * @query {string} vs_currency - Target currency (default: usd)
 * @query {string} days - Number of days (1, 7, 14, 30, 90, 180, 365, max)
 * @query {string} interval - Data interval (minutely, hourly, daily)
 */
router.get(
  '/:id/history',
  validateParams(CryptoIdParamsSchema),
  validateQuery(GetPriceHistoryQuerySchema),
  pricesController.getPriceHistory
);

export default router;
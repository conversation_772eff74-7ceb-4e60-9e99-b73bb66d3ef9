import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Animated,
  ViewStyle,
  FlatList,
  Text,
  Modal,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../common/ThemeProvider';
import { SPACING, FONT_SIZES, BORDER_RADIUS, FONT_WEIGHTS, SPRING_CONFIG } from '../../constants';
import TouchableScale from './TouchableScale';

const { width: screenWidth } = Dimensions.get('window');

interface SearchSuggestion {
  id: string;
  text: string;
  type: 'history' | 'suggestion' | 'category';
  category?: string;
}

interface EnhancedSearchBarProps {
  placeholder?: string;
  onSearch: (query: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  style?: ViewStyle;
  autoFocus?: boolean;
  suggestions?: SearchSuggestion[];
  searchHistory?: string[];
  onClearHistory?: () => void;
}

const EnhancedSearchBar: React.FC<EnhancedSearchBarProps> = ({
  placeholder = '搜索区块链资讯...',
  onSearch,
  onFocus,
  onBlur,
  style,
  autoFocus = false,
  suggestions = [],
  searchHistory = [],
  onClearHistory,
}) => {
  const { colors } = useTheme();
  const [query, setQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef<TextInput>(null);
  const focusAnim = useRef(new Animated.Value(0)).current;
  const suggestionAnim = useRef(new Animated.Value(0)).current;

  const styles = createStyles(colors);

  // 生成搜索建议
  const generateSuggestions = (searchText: string): SearchSuggestion[] => {
    if (!searchText.trim()) {
      return searchHistory.slice(0, 5).map((item, index) => ({
        id: `history-${index}`,
        text: item,
        type: 'history',
      }));
    }

    const filtered = suggestions.filter(item =>
      item.text.toLowerCase().includes(searchText.toLowerCase())
    );

    const historySuggestions = searchHistory
      .filter(item => item.toLowerCase().includes(searchText.toLowerCase()))
      .slice(0, 3)
      .map((item, index) => ({
        id: `history-${index}`,
        text: item,
        type: 'history' as const,
      }));

    return [...historySuggestions, ...filtered].slice(0, 8);
  };

  const currentSuggestions = generateSuggestions(query);

  useEffect(() => {
    if (isFocused && (query.length > 0 || searchHistory.length > 0)) {
      setShowSuggestions(true);
      Animated.spring(suggestionAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: SPRING_CONFIG.tension,
        friction: SPRING_CONFIG.friction,
      }).start();
    } else {
      Animated.spring(suggestionAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: SPRING_CONFIG.tension,
        friction: SPRING_CONFIG.friction,
      }).start(() => {
        setShowSuggestions(false);
      });
    }
  }, [isFocused, query, searchHistory.length]);

  const handleFocus = () => {
    setIsFocused(true);
    Animated.timing(focusAnim, {
      toValue: 1,
      duration: 200,
      useNativeDriver: false,
    }).start();
    onFocus?.();
  };

  const handleBlur = () => {
    // 延迟关闭以允许点击建议
    setTimeout(() => {
      setIsFocused(false);
      Animated.timing(focusAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }).start();
      onBlur?.();
    }, 150);
  };

  const handleSearch = (searchText?: string) => {
    const searchQuery = searchText || query;
    if (searchQuery.trim()) {
      onSearch(searchQuery.trim());
      setQuery(searchQuery);
      inputRef.current?.blur();
    }
  };

  const handleSuggestionPress = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.text);
    handleSearch(suggestion.text);
  };

  const handleClear = () => {
    setQuery('');
    inputRef.current?.focus();
  };

  const borderColor = focusAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [colors.border, colors.primary],
  });

  const shadowOpacity = focusAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 0.1],
  });

  const renderSuggestionItem = ({ item }: { item: SearchSuggestion }) => (
    <TouchableScale
      style={styles.suggestionItem}
      onPress={() => handleSuggestionPress(item)}
    >
      <Ionicons
        name={
          item.type === 'history'
            ? 'time-outline'
            : item.type === 'category'
            ? 'folder-outline'
            : 'search-outline'
        }
        size={16}
        color={colors.textSecondary}
        style={styles.suggestionIcon}
      />
      <Text style={styles.suggestionText} numberOfLines={1}>
        {item.text}
      </Text>
      {item.type === 'history' && (
        <TouchableOpacity style={styles.removeButton}>
          <Ionicons name="close" size={14} color={colors.textSecondary} />
        </TouchableOpacity>
      )}
    </TouchableScale>
  );

  return (
    <View style={[styles.container, style]}>
      <Animated.View
        style={[
          styles.searchContainer,
          {
            borderColor,
            shadowOpacity,
          },
        ]}
      >
        <Ionicons
          name="search"
          size={20}
          color={isFocused ? colors.primary : colors.textSecondary}
          style={styles.searchIcon}
        />

        <TextInput
          ref={inputRef}
          style={styles.input}
          placeholder={placeholder}
          placeholderTextColor={colors.textSecondary}
          value={query}
          onChangeText={setQuery}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onSubmitEditing={() => handleSearch()}
          returnKeyType="search"
          autoFocus={autoFocus}
          selectionColor={colors.primary}
        />

        {query.length > 0 && (
          <TouchableScale onPress={handleClear} style={styles.clearButton}>
            <Ionicons name="close-circle" size={20} color={colors.textSecondary} />
          </TouchableScale>
        )}
      </Animated.View>

      {showSuggestions && (
        <Animated.View
          style={[
            styles.suggestionsContainer,
            {
              opacity: suggestionAnim,
              transform: [
                {
                  translateY: suggestionAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [-10, 0],
                  }),
                },
              ],
            },
          ]}
        >
          <FlatList
            data={currentSuggestions}
            renderItem={renderSuggestionItem}
            keyExtractor={(item) => item.id}
            style={styles.suggestionsList}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            ListHeaderComponent={
              query.length === 0 && searchHistory.length > 0 ? (
                <View style={styles.suggestionHeader}>
                  <Text style={styles.suggestionHeaderText}>最近搜索</Text>
                  {onClearHistory && (
                    <TouchableOpacity onPress={onClearHistory}>
                      <Text style={styles.clearHistoryText}>清空</Text>
                    </TouchableOpacity>
                  )}
                </View>
              ) : null
            }
          />
        </Animated.View>
      )}
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      position: 'relative',
      zIndex: 1000,
    },
    searchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.xl,
      borderWidth: 2,
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      marginHorizontal: SPACING.md,
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowRadius: 8,
      elevation: 3,
    },
    searchIcon: {
      marginRight: SPACING.sm,
    },
    input: {
      flex: 1,
      fontSize: FONT_SIZES.base,
      color: colors.text,
      paddingVertical: SPACING.xs,
      fontWeight: FONT_WEIGHTS.medium,
    },
    clearButton: {
      marginLeft: SPACING.sm,
      padding: SPACING.xs,
    },
    suggestionsContainer: {
      position: 'absolute',
      top: 60,
      left: SPACING.md,
      right: SPACING.md,
      backgroundColor: colors.surfaceElevated,
      borderRadius: BORDER_RADIUS.lg,
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 12,
      elevation: 8,
      maxHeight: 300,
    },
    suggestionsList: {
      maxHeight: 280,
    },
    suggestionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    suggestionHeaderText: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textSecondary,
    },
    clearHistoryText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    suggestionItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.borderLight,
    },
    suggestionIcon: {
      marginRight: SPACING.sm,
    },
    suggestionText: {
      flex: 1,
      fontSize: FONT_SIZES.base,
      color: colors.text,
      fontWeight: FONT_WEIGHTS.medium,
    },
    removeButton: {
      padding: SPACING.xs,
    },
  });

export default EnhancedSearchBar;

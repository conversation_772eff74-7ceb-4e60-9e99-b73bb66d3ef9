import { Request, Response } from 'express';
import { dataScheduler } from '@/scheduler/scheduler';
import { logger } from '@/utils/logger';
import { asyncHandler } from '@/middlewares/error';

/**
 * Flash Controller
 * Handles flash news related API endpoints
 */
class FlashController {
  /**
   * Get paginated flash news items
   * GET /api/flash
   */
  public getFlash = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;

    logger.info('Fetching flash news items from scheduler', {
      page,
      limit,
      requestId: res.getHeader('x-request-id'),
    });

    const startTime = Date.now();

    // Get flash news items from dataScheduler
    const result = dataScheduler.getPagedData(page, limit, 'news_flash');
    
    // Transform data to match expected format
    const items = result.items.map(item => ({
      id: item.id,
      title: item.type === 'twitter' ? `Tweet by ${item.author.displayName}` : item.title,
      summary: item.type === 'twitter' ? item.content : item.summary,
      content: item.type === 'twitter' ? item.content : (item.content || item.summary),
      imageUrl: item.type === 'twitter' ? item.author.avatar : item.imageUrl,
      publishedAt: item.publishedAt,
      tags: item.type === 'twitter' ? item.hashtags : item.tags,
      source: item.source,
      author: item.type === 'twitter' ? item.author.displayName : item.author,
      isHot: item.type === 'twitter' ? false : (item.isHot || false),
      isImportant: item.type === 'twitter' ? false : (item.isImportant || false),
      engagement: item.engagement,
    }));

    const duration = Date.now() - startTime;

    logger.info('Flash news items fetched successfully from scheduler', {
      itemsCount: items.length,
      total: result.total,
      duration: `${duration}ms`,
      requestId: res.getHeader('x-request-id'),
    });

    const response = {
      success: true,
      data: {
        items,
        total: result.total,
        page: result.page,
        limit: result.limit,
        hasMore: result.hasMore,
        totalPages: Math.ceil(result.total / result.limit),
      },
      message: 'Flash news items retrieved successfully',
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(response);
  });

  /**
   * Search flash news items
   * GET /api/flash/search
   */
  public searchFlash = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const query = req.query.q as string;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;

    if (!query) {
      res.status(400).json({
        success: false,
        error: 'Search query is required',
        timestamp: new Date().toISOString(),
      });
      return;
    }

    logger.info('Searching flash news items in scheduler', {
      query,
      page,
      limit,
      requestId: res.getHeader('x-request-id'),
    });

    const startTime = Date.now();

    // Search flash news items using dataScheduler
    const result = dataScheduler.searchData(query, page, limit);
    
    // Filter for news_flash type items only
    const flashItems = result.items.filter(item => item.type === 'news_flash');
    
    // Transform data to match expected format
    const items = flashItems.map(item => ({
      id: item.id,
      title: item.title,
      summary: item.summary,
      content: item.content || item.summary,
      imageUrl: item.imageUrl,
      publishedAt: item.publishedAt,
      tags: item.tags,
      source: item.source,
      author: item.author,
      isHot: item.isHot || false,
      isImportant: item.isImportant || false,
      engagement: item.engagement,
    }));

    const duration = Date.now() - startTime;

    logger.info('Flash news search completed in scheduler', {
      query,
      resultsCount: items.length,
      duration: `${duration}ms`,
      requestId: res.getHeader('x-request-id'),
    });

    const response = {
      success: true,
      data: {
        items,
        total: items.length,
        page,
        limit,
        hasMore: false,
        totalPages: 1,
      },
      message: `Flash news search results for "${query}"`,
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(response);
  });
}

// Export singleton instance
export const flashController = new FlashController();
export default flashController;
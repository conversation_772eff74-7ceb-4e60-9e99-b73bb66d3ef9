import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../common/ThemeProvider';
import { SPACING, FONT_SIZES, BORDER_RADIUS, FONT_WEIGHTS } from '../../constants';
import { NewsArticle } from '../../types';
import TouchableScale from '../ui/TouchableScale';
import { formatRelativeTime } from '../../utils/timeFormat';
import ImagePlaceholder from '../ui/ImagePlaceholder';

interface TrendingSectionProps {
  articles: NewsArticle[];
  onArticlePress: (articleId: string) => void;
  onSeeAllPress: () => void;
}

const TrendingSection: React.FC<TrendingSectionProps> = ({
  articles,
  onArticlePress,
  onSeeAllPress,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  const renderTrendingArticle = (article: NewsArticle, index: number) => (
    <TouchableScale
      key={article.id}
      style={styles.trendingItem}
      onPress={() => onArticlePress(article.id)}
    >

      {article.imageUrl ? (
        <Image
          source={{ uri: article.imageUrl }}
          style={styles.trendingImage}
          onError={() => console.log('图片加载失败')}
        />
      ) : (
        <ImagePlaceholder
          width={64}
          height={48}
          style={styles.trendingImage}
          iconName="newspaper-outline"
          text=""
        />
      )}

      <View style={styles.trendingContent}>
        <Text style={styles.trendingTitle} numberOfLines={2}>
          {article.title}
        </Text>
        <View style={styles.trendingMeta}>
          <View style={styles.categoryBadge}>
            <Text style={styles.categoryText}>{article.category.name}</Text>
          </View>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Ionicons name="eye-outline" size={12} color={colors.textSecondary} />
              <Text style={styles.statText}>1.2k</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="heart-outline" size={12} color={colors.textSecondary} />
              <Text style={styles.statText}>89</Text>
            </View>
          </View>
        </View>
        <Text style={styles.trendingTime}>{formatRelativeTime(article.publishedAt)}</Text>
      </View>

      <View style={styles.actionButton}>
        <Ionicons name="chevron-forward" size={16} color={colors.textSecondary} />
      </View>
    </TouchableScale>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Ionicons name="flame" size={20} color={colors.warning} />
          <Text style={styles.sectionTitle}>热门资讯</Text>
          <View style={styles.hotBadge}>
            <Text style={styles.hotBadgeText}>HOT</Text>
          </View>
        </View>
        <TouchableOpacity onPress={onSeeAllPress} style={styles.seeAllButton}>
          <Text style={styles.seeAllText}>查看全部</Text>
          <Ionicons name="chevron-forward" size={16} color={colors.primary} />
        </TouchableOpacity>
      </View>

      <View style={styles.trendingList}>
        {articles.slice(0, 5).map(renderTrendingArticle)}
      </View>
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      marginVertical: SPACING.lg,
      paddingHorizontal: SPACING.md,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: SPACING.md,
    },
    headerLeft: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    sectionTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.text,
      marginLeft: SPACING.sm,
    },
    hotBadge: {
      backgroundColor: colors.warning,
      paddingHorizontal: SPACING.xs,
      paddingVertical: 2,
      borderRadius: BORDER_RADIUS.sm,
      marginLeft: SPACING.sm,
    },
    hotBadgeText: {
      fontSize: FONT_SIZES.xs,
      color: '#FFFFFF',
      fontWeight: FONT_WEIGHTS.bold,
    },
    seeAllButton: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    seeAllText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
      marginRight: SPACING.xs,
    },
    trendingList: {
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.lg,
      overflow: 'hidden',
    },
    trendingItem: {
      flexDirection: 'row',
      alignItems: 'flex-start', // 改为顶部对齐，提供更好的视觉层次
      paddingVertical: SPACING.sm, // 减少垂直间距，使排版更紧凑
      paddingHorizontal: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.borderLight,
      minHeight: 72, // 减少最小高度，使布局更紧凑
    },
    rankNumber: {
      fontSize: FONT_SIZES.base, // 稍微减小字体大小
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.primary,
    },
    trendingIndicator: {
      backgroundColor: colors.success + '20',
      borderRadius: BORDER_RADIUS.sm,
      padding: 2,
    },
    trendingImage: {
      width: 64, // 稍微减小图片尺寸
      height: 48,
      borderRadius: BORDER_RADIUS.sm,
      marginRight: SPACING.sm, // 减少右边距
      backgroundColor: colors.backgroundSecondary,
      marginTop: SPACING.xs, // 添加顶部边距以对齐
    },
    trendingContent: {
      flex: 1,
    },
    trendingTitle: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
      lineHeight: 18, // 减少行高，使布局更紧凑
      marginBottom: SPACING.xs, // 减少底部边距
    },
    trendingMeta: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: SPACING.xs,
      marginTop: SPACING.xs, // 添加顶部边距，改善层次感
    },
    categoryBadge: {
      backgroundColor: colors.primary + '20',
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.xs,
      borderRadius: BORDER_RADIUS.sm,
    },
    categoryText: {
      fontSize: FONT_SIZES.xs,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    statsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    statItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginLeft: SPACING.sm,
    },
    statText: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      marginLeft: SPACING.xs,
      fontWeight: FONT_WEIGHTS.medium,
    },
    trendingTime: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    actionButton: {
      padding: SPACING.sm,
      marginLeft: SPACING.sm,
    },
  });

export default TrendingSection;

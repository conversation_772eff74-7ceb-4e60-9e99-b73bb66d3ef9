import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';


import { RootStackParamList, MainTabParamList } from '../types';
import { useAppStore } from '../store';
import { COLORS } from '../constants';

// 导入屏幕组件
import HomeScreen from '../screens/Home/HomeScreen';
import NewsScreen from '../screens/News/NewsScreen';
import AIAssistantScreen from '../screens/AI/AIAssistantScreen';
import DiscoverScreen from '../screens/Discover/DiscoverScreen';
import ProfileScreen from '../screens/Profile/ProfileScreen';
import ArticleDetailScreen from '../screens/Details/ArticleDetailScreen';
import FeaturedListScreen from '../screens/Lists/FeaturedListScreen';
import TwitterListScreen from '../screens/Lists/TwitterListScreen';
import TrendingListScreen from '../screens/Lists/TrendingListScreen';
import APITestScreen from '../screens/Test/APITestScreen';


const Stack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

// 底部标签导航
function MainTabNavigator() {
  const { theme } = useAppStore();
  
  const currentTheme = theme === 'light' ? 'light' : 'dark';
  const colors = COLORS[currentTheme];

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'News') {
            iconName = focused ? 'flash' : 'flash-outline';
          } else if (route.name === 'AI') {
            iconName = focused ? 'sparkles' : 'sparkles-outline';
          } else if (route.name === 'Discover') {
            iconName = focused ? 'compass' : 'compass-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          } else {
            iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textSecondary,
        tabBarStyle: {
          backgroundColor: colors.surface,
          borderTopColor: colors.border,
        },
        headerStyle: {
          backgroundColor: colors.background,
        },
        headerTintColor: colors.text,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          title: '首页',
          headerShown: false,
        }}
      />
      <Tab.Screen
        name="News"
        component={NewsScreen}
        options={{
          title: '快讯',
          headerShown: false,
        }}
      />
      <Tab.Screen
        name="AI"
        component={AIAssistantScreen}
        options={{
          title: 'AI助手',
          headerShown: false,
        }}
      />
      <Tab.Screen
        name="Discover"
        component={DiscoverScreen}
        options={{
          title: '发现',
          headerShown: false,
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          title: '我的',
          headerShown: false,
        }}
      />
    </Tab.Navigator>
  );
}

// 根导航
export default function RootNavigator() {
  const { theme } = useAppStore();
  
  const currentTheme = theme === 'light' ? 'light' : 'dark';
  const colors = COLORS[currentTheme];

  return (
    <NavigationContainer
      theme={{
        dark: currentTheme === 'dark',
        colors: {
          primary: colors.primary,
          background: colors.background,
          card: colors.surface,
          text: colors.text,
          border: colors.border,
          notification: colors.primary,
        },
        fonts: {
          regular: {
            fontFamily: 'System',
            fontWeight: '400',
          },
          medium: {
            fontFamily: 'System',
            fontWeight: '500',
          },
          bold: {
            fontFamily: 'System',
            fontWeight: '700',
          },
          heavy: {
            fontFamily: 'System',
            fontWeight: '900',
          },
        },
      }}
    >
      <Stack.Navigator
        screenOptions={{
          headerStyle: {
            backgroundColor: colors.background,
          },
          headerTintColor: colors.text,
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        <Stack.Screen 
          name="Main" 
          component={MainTabNavigator} 
          options={{ headerShown: false }} 
        />
        <Stack.Screen
          name="ArticleDetail"
          component={ArticleDetailScreen}
          options={{
            title: '文章详情',
            presentation: 'modal',
          }}
        />
        <Stack.Screen
          name="FeaturedList"
          component={FeaturedListScreen}
          options={{
            title: '精选文章',
          }}
        />
        <Stack.Screen
          name="TwitterList"
          component={TwitterListScreen}
          options={{
            title: '推特观点',
          }}
        />
        <Stack.Screen
          name="TrendingList"
          component={TrendingListScreen}
          options={{
            title: '热门资讯',
          }}
        />
        <Stack.Screen
          name="APITest"
          component={APITestScreen}
          options={{
            title: 'API 测试',
          }}
        />

      </Stack.Navigator>
    </NavigationContainer>
  );
}

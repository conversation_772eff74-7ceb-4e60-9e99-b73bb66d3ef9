import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';

import { RootStackParamList, NewsArticle } from '../../types';
import { useNewsStore } from '../../store';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants';
import { useTheme } from '../../components/common/ThemeProvider';
import TouchableScale from '../../components/ui/TouchableScale';
import PullToRefresh from '../../components/ui/PullToRefresh';

type TrendingListScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const TrendingListScreen: React.FC = () => {
  const navigation = useNavigation<TrendingListScreenNavigationProp>();
  const { colors } = useTheme();
  const { articles, isLoading, refreshArticles } = useNewsStore();
  const [refreshing, setRefreshing] = useState(false);

  const styles = createStyles(colors);

  useEffect(() => {
    navigation.setOptions({
      title: '热门资讯',
      headerStyle: {
        backgroundColor: colors.background,
      },
      headerTintColor: colors.text,
      headerLeft: () => (
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.headerButton}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
      ),
    });
  }, [navigation, colors]);

  const onRefresh = async () => {
    setRefreshing(true);
    await refreshArticles();
    setRefreshing(false);
  };

  const navigateToArticle = (articleId: string) => {
    navigation.navigate('ArticleDetail', { articleId });
  };

  const renderTrendingArticle = ({ item: article, index }: { item: NewsArticle; index: number }) => (
    <TouchableScale
      style={styles.articleCard}
      onPress={() => navigateToArticle(article.id)}
    >
      <View style={styles.rankContainer}>
        <Text style={styles.rankNumber}>{index + 1}</Text>
        <View style={styles.trendingIndicator}>
          <Ionicons name="trending-up" size={14} color={colors.success} />
        </View>
      </View>

      <Image
        source={{ uri: article.imageUrl || 'https://via.placeholder.com/80x60' }}
        style={styles.articleImage}
      />

      <View style={styles.articleContent}>
        <Text style={styles.articleTitle} numberOfLines={2}>
          {article.title}
        </Text>
        <Text style={styles.articleSummary} numberOfLines={2}>
          {article.summary}
        </Text>
        <View style={styles.articleMeta}>
          <Text style={styles.articleCategory}>{article.category.name}</Text>
          <Text style={styles.articleTime}>{article.publishedAt}</Text>
        </View>
      </View>

      <View style={styles.actionButton}>
        <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
      </View>
    </TouchableScale>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Ionicons name="flame" size={24} color={colors.warning} />
          <Text style={styles.headerTitle}>热门资讯排行</Text>
          <View style={styles.hotBadge}>
            <Text style={styles.hotBadgeText}>HOT</Text>
          </View>
        </View>
        <Text style={styles.headerSubtitle}>实时更新最热门的区块链资讯</Text>
      </View>

      <FlatList
        data={articles}
        renderItem={renderTrendingArticle}
        keyExtractor={(item) => item.id}
        style={styles.list}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <PullToRefresh refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="flame-outline" size={48} color={colors.textSecondary} />
            <Text style={styles.emptyText}>暂无热门资讯</Text>
          </View>
        }
      />
    </SafeAreaView>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    headerButton: {
      padding: SPACING.sm,
      marginLeft: SPACING.xs,
    },
    header: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.lg,
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerContent: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: SPACING.xs,
    },
    headerTitle: {
      fontSize: FONT_SIZES.xl,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.text,
      marginLeft: SPACING.sm,
    },
    hotBadge: {
      backgroundColor: colors.warning,
      paddingHorizontal: SPACING.sm,
      paddingVertical: 2,
      borderRadius: BORDER_RADIUS.sm,
      marginLeft: SPACING.sm,
    },
    hotBadgeText: {
      fontSize: FONT_SIZES.xs,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#FFFFFF',
    },
    headerSubtitle: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginLeft: 32,
    },
    list: {
      flex: 1,
    },
    listContent: {
      paddingVertical: SPACING.md,
    },
    articleCard: {
      flexDirection: 'row',
      alignItems: 'center',
      marginHorizontal: SPACING.md,
      marginBottom: SPACING.md,
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.lg,
      padding: SPACING.md,
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    rankContainer: {
      alignItems: 'center',
      marginRight: SPACING.md,
      minWidth: 40,
    },
    rankNumber: {
      fontSize: FONT_SIZES.xl,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.primary,
      marginBottom: SPACING.xs,
    },
    trendingIndicator: {
      backgroundColor: colors.success + '20',
      borderRadius: BORDER_RADIUS.sm,
      padding: 2,
    },
    articleImage: {
      width: 80,
      height: 60,
      borderRadius: BORDER_RADIUS.md,
      marginRight: SPACING.md,
    },
    articleContent: {
      flex: 1,
    },
    articleTitle: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
      marginBottom: SPACING.xs,
      lineHeight: 20,
    },
    articleSummary: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginBottom: SPACING.sm,
      lineHeight: 18,
    },
    articleMeta: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    articleCategory: {
      fontSize: FONT_SIZES.xs,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    articleTime: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
    },
    actionButton: {
      padding: SPACING.xs,
      marginLeft: SPACING.sm,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: SPACING.xl * 2,
    },
    emptyText: {
      fontSize: FONT_SIZES.base,
      color: colors.textSecondary,
      marginTop: SPACING.md,
    },
  });

export default TrendingListScreen;

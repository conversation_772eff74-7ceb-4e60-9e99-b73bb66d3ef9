import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../common/ThemeProvider';
import { SPACING, FONT_SIZES, BORDER_RADIUS, FONT_WEIGHTS } from '../../constants';
import { NewsArticle } from '../../types';
import TouchableScale from '../ui/TouchableScale';

interface FeaturedSectionProps {
  articles: NewsArticle[];
  onArticlePress: (articleId: string) => void;
  onSeeAllPress: () => void;
}

const FeaturedSection: React.FC<FeaturedSectionProps> = ({
  articles,
  onArticlePress,
  onSeeAllPress,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  const renderFeaturedArticle = (article: NewsArticle, index: number) => (
    <TouchableScale
      key={article.id}
      style={index === 0 ? [styles.featuredCard, styles.firstCard] : styles.featuredCard}
      onPress={() => onArticlePress(article.id)}
    >
      <Image
        source={{ uri: article.imageUrl || 'https://via.placeholder.com/300x180' }}
        style={styles.featuredImage}
      />
      <View style={styles.featuredOverlay}>
        <View style={styles.featuredBadge}>
          <Ionicons name="star" size={12} color="#FFFFFF" />
          <Text style={styles.featuredBadgeText}>精选</Text>
        </View>
        <View style={styles.featuredContent}>
          <Text style={styles.featuredTitle} numberOfLines={2}>
            {article.title}
          </Text>
          <View style={styles.featuredMeta}>
            <Text style={styles.featuredCategory}>{article.category.name}</Text>
            <Text style={styles.featuredTime}>{article.publishedAt}</Text>
          </View>
        </View>
      </View>
    </TouchableScale>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Ionicons name="star" size={20} color={colors.primary} />
          <Text style={styles.sectionTitle}>精选文章</Text>
        </View>
        <TouchableOpacity onPress={onSeeAllPress} style={styles.seeAllButton}>
          <Text style={styles.seeAllText}>查看全部</Text>
          <Ionicons name="chevron-forward" size={16} color={colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        decelerationRate="fast"
        snapToInterval={316} // 卡片宽度(300) + 右边距(16)
        snapToAlignment="center"
        pagingEnabled={false}
        scrollEventThrottle={16}
        bounces={false}
        bouncesZoom={false}
      >
        {articles.slice(0, 5).map(renderFeaturedArticle)}
      </ScrollView>
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      marginVertical: SPACING.lg,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: SPACING.md,
      marginBottom: SPACING.md,
    },
    headerLeft: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    sectionTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.text,
      marginLeft: SPACING.sm,
    },
    seeAllButton: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    seeAllText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
      marginRight: SPACING.xs,
    },
    scrollContent: {
      paddingLeft: SPACING.md,
      paddingRight: SPACING.md,
      alignItems: 'center', // 垂直居中对齐
    },
    featuredCard: {
      width: 300,
      height: 200,
      marginRight: SPACING.md,
      borderRadius: BORDER_RADIUS.xl,
      overflow: 'hidden',
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 5,
    },
    firstCard: {
      marginLeft: 0,
    },
    featuredImage: {
      width: '100%',
      height: '100%',
      position: 'absolute',
    },
    featuredOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.4)',
      justifyContent: 'space-between',
      padding: SPACING.md,
    },
    featuredBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.primary,
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.xs,
      borderRadius: BORDER_RADIUS.full,
      alignSelf: 'flex-start',
    },
    featuredBadgeText: {
      fontSize: FONT_SIZES.xs,
      color: '#FFFFFF',
      fontWeight: FONT_WEIGHTS.semibold,
      marginLeft: SPACING.xs,
    },
    featuredContent: {
      flex: 1,
      justifyContent: 'flex-end',
    },
    featuredTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#FFFFFF',
      lineHeight: 22,
      marginBottom: SPACING.sm,
    },
    featuredMeta: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    featuredCategory: {
      fontSize: FONT_SIZES.sm,
      color: '#FFFFFF',
      fontWeight: FONT_WEIGHTS.medium,
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.xs,
      borderRadius: BORDER_RADIUS.sm,
    },
    featuredTime: {
      fontSize: FONT_SIZES.sm,
      color: 'rgba(255, 255, 255, 0.8)',
      fontWeight: FONT_WEIGHTS.medium,
    },
  });

export default FeaturedSection;

import { Router } from 'express';
import { newsController } from '@/controllers/news';
import { validateQuery, validateParams } from '@/middlewares/validation';
import {
  GetNewsQuerySchema,
  SearchNewsQuerySchema,
} from '@/types';
import { z } from 'zod';

const router: Router = Router();

// Validation schemas for parameters
const ArticleIdParamsSchema = z.object({
  id: z.string().min(1, 'Article ID is required'),
});

const CategorySlugParamsSchema = z.object({
  slug: z.string().min(1, 'Category slug is required'),
});

const LimitQuerySchema = z.object({
  limit: z.coerce.number().min(1).max(50).default(10),
});

const CategoryPageQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
});

/**
 * @route GET /api/news
 * @desc Get paginated news articles
 * @access Public
 * @query {number} page - Page number (default: 1)
 * @query {number} limit - Items per page (default: 20, max: 100)
 * @query {string} category - Filter by category slug
 * @query {string} sortBy - Sort by field (publishedAt, relevance, popularity)
 * @query {string} sortOrder - Sort order (asc, desc)
 */
router.get(
  '/',
  validateQuery(GetNewsQuerySchema),
  newsController.getNews
);

/**
 * @route GET /api/news/search
 * @desc Search news articles
 * @access Public
 * @query {string} q - Search query (required)
 * @query {number} page - Page number (default: 1)
 * @query {number} limit - Items per page (default: 20, max: 50)
 * @query {string} category - Filter by category slug
 * @query {string} sortBy - Sort by field (publishedAt, relevance)
 */
router.get(
  '/search',
  validateQuery(SearchNewsQuerySchema),
  newsController.searchNews
);

/**
 * @route GET /api/news/trending
 * @desc Get trending news articles
 * @access Public
 * @query {number} limit - Number of articles (default: 10, max: 50)
 */
router.get(
  '/trending',
  validateQuery(LimitQuerySchema),
  newsController.getTrendingNews
);

/**
 * @route GET /api/news/featured
 * @desc Get featured news articles
 * @access Public
 * @query {number} limit - Number of articles (default: 5, max: 50)
 */
router.get(
  '/featured',
  validateQuery(LimitQuerySchema),
  newsController.getFeaturedNews
);

/**
 * @route GET /api/news/categories
 * @desc Get news categories
 * @access Public
 */
router.get(
  '/categories',
  newsController.getCategories
);

/**
 * @route GET /api/news/category/:slug
 * @desc Get articles by category
 * @access Public
 * @param {string} slug - Category slug
 * @query {number} page - Page number (default: 1)
 * @query {number} limit - Items per page (default: 20, max: 100)
 */
router.get(
  '/category/:slug',
  validateParams(CategorySlugParamsSchema),
  validateQuery(CategoryPageQuerySchema),
  newsController.getArticlesByCategory
);

/**
 * @route GET /api/news/:id
 * @desc Get specific news article by ID
 * @access Public
 * @param {string} id - Article ID
 */
router.get(
  '/:id',
  validateParams(ArticleIdParamsSchema),
  newsController.getArticleById
);

export default router;
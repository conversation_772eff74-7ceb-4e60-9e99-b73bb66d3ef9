import request from 'supertest';
import app from '@/app';

describe('News API Endpoints', () => {
  describe('GET /api/news', () => {
    it('should return paginated news articles', async () => {
      const response = await request(app)
        .get('/api/news')
        .query({ page: 1, limit: 10 })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('items');
      expect(response.body.data).toHaveProperty('total');
      expect(response.body.data).toHaveProperty('page', 1);
      expect(response.body.data).toHaveProperty('limit', 10);
      expect(response.body.data).toHaveProperty('hasMore');
      expect(response.body.data).toHaveProperty('totalPages');
      expect(Array.isArray(response.body.data.items)).toBe(true);
    });

    it('should return 400 for invalid page parameter', async () => {
      const response = await request(app)
        .get('/api/news')
        .query({ page: 0 })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
    });

    it('should return 400 for invalid limit parameter', async () => {
      const response = await request(app)
        .get('/api/news')
        .query({ limit: 101 })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('GET /api/news/search', () => {
    it('should search news articles successfully', async () => {
      const response = await request(app)
        .get('/api/news/search')
        .query({ q: 'bitcoin', page: 1, limit: 5 })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('items');
      expect(Array.isArray(response.body.data.items)).toBe(true);
    });

    it('should return 400 for missing search query', async () => {
      const response = await request(app)
        .get('/api/news/search')
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
    });

    it('should return 400 for empty search query', async () => {
      const response = await request(app)
        .get('/api/news/search')
        .query({ q: '' })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('GET /api/news/trending', () => {
    it('should return trending news articles', async () => {
      const response = await request(app)
        .get('/api/news/trending')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should respect limit parameter', async () => {
      const response = await request(app)
        .get('/api/news/trending')
        .query({ limit: 5 })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data.length).toBeLessThanOrEqual(5);
    });
  });

  describe('GET /api/news/categories', () => {
    it('should return news categories', async () => {
      const response = await request(app)
        .get('/api/news/categories')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
      
      // Check that each category has required properties
      if (response.body.data.length > 0) {
        const category = response.body.data[0];
        expect(category).toHaveProperty('id');
        expect(category).toHaveProperty('name');
        expect(category).toHaveProperty('slug');
        expect(category).toHaveProperty('color');
        expect(category).toHaveProperty('icon');
      }
    });
  });

  describe('GET /api/news/:id', () => {
    it('should return 404 for non-existent article', async () => {
      const response = await request(app)
        .get('/api/news/non-existent-id')
        .expect(404);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
    });

    it('should return 400 for empty article ID', async () => {
      const response = await request(app)
        .get('/api/news/')
        .expect(404); // This will match the catch-all route
    });
  });

  describe('GET /api/news/category/:slug', () => {
    it('should return articles for valid category', async () => {
      const response = await request(app)
        .get('/api/news/category/cryptocurrency')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('items');
      expect(Array.isArray(response.body.data.items)).toBe(true);
    });

    it('should return 400 for empty category slug', async () => {
      const response = await request(app)
        .get('/api/news/category/')
        .expect(404); // This will match a different route or 404
    });
  });
});
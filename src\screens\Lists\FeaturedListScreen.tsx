import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';

import { RootStackParamList, NewsArticle } from '../../types';
import { useNewsStore } from '../../store';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants';
import { useTheme } from '../../components/common/ThemeProvider';
import TouchableScale from '../../components/ui/TouchableScale';
import PullToRefresh from '../../components/ui/PullToRefresh';

type FeaturedListScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const FeaturedListScreen: React.FC = () => {
  const navigation = useNavigation<FeaturedListScreenNavigationProp>();
  const { colors } = useTheme();
  const { articles, isLoading, refreshArticles } = useNewsStore();
  const [refreshing, setRefreshing] = useState(false);

  const styles = createStyles(colors);

  useEffect(() => {
    navigation.setOptions({
      title: '精选文章',
      headerStyle: {
        backgroundColor: colors.background,
      },
      headerTintColor: colors.text,
      headerLeft: () => (
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.headerButton}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
      ),
    });
  }, [navigation, colors]);

  const onRefresh = async () => {
    setRefreshing(true);
    await refreshArticles();
    setRefreshing(false);
  };

  const navigateToArticle = (articleId: string) => {
    navigation.navigate('ArticleDetail', { articleId });
  };

  const renderFeaturedArticle = ({ item: article, index }: { item: NewsArticle; index: number }) => (
    <TouchableScale
      style={styles.articleCard}
      onPress={() => navigateToArticle(article.id)}
    >
      <Image
        source={{ uri: article.imageUrl || 'https://via.placeholder.com/300x180' }}
        style={styles.articleImage}
      />
      <View style={styles.articleContent}>
        <View style={styles.articleBadge}>
          <Ionicons name="star" size={12} color="#FFFFFF" />
          <Text style={styles.badgeText}>精选</Text>
        </View>
        <Text style={styles.articleTitle} numberOfLines={2}>
          {article.title}
        </Text>
        <Text style={styles.articleSummary} numberOfLines={3}>
          {article.summary}
        </Text>
        <View style={styles.articleMeta}>
          <Text style={styles.articleCategory}>{article.category.name}</Text>
          <Text style={styles.articleTime}>{article.publishedAt}</Text>
        </View>
      </View>
    </TouchableScale>
  );

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        data={articles}
        renderItem={renderFeaturedArticle}
        keyExtractor={(item) => item.id}
        style={styles.list}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <PullToRefresh refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="star-outline" size={48} color={colors.textSecondary} />
            <Text style={styles.emptyText}>暂无精选文章</Text>
          </View>
        }
      />
    </SafeAreaView>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    headerButton: {
      padding: SPACING.sm,
      marginLeft: SPACING.xs,
    },
    list: {
      flex: 1,
    },
    listContent: {
      paddingVertical: SPACING.md,
    },
    articleCard: {
      marginHorizontal: SPACING.md,
      marginBottom: SPACING.lg,
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.lg,
      overflow: 'hidden',
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    articleImage: {
      width: '100%',
      height: 200,
    },
    articleContent: {
      padding: SPACING.md,
    },
    articleBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.primary,
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.xs,
      borderRadius: BORDER_RADIUS.full,
      alignSelf: 'flex-start',
      marginBottom: SPACING.sm,
    },
    badgeText: {
      fontSize: FONT_SIZES.xs,
      fontWeight: FONT_WEIGHTS.semibold,
      color: '#FFFFFF',
      marginLeft: SPACING.xs,
    },
    articleTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.text,
      marginBottom: SPACING.sm,
      lineHeight: 24,
    },
    articleSummary: {
      fontSize: FONT_SIZES.base,
      color: colors.textSecondary,
      marginBottom: SPACING.md,
      lineHeight: 20,
    },
    articleMeta: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    articleCategory: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    articleTime: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: SPACING.xl * 2,
    },
    emptyText: {
      fontSize: FONT_SIZES.base,
      color: colors.textSecondary,
      marginTop: SPACING.md,
    },
  });

export default FeaturedListScreen;

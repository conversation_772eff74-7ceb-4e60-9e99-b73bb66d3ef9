import { Request, Response, NextFunction } from 'express';
import { fetcherService } from './service';
import { 
  FetcherQuerySchema, 
  SearchQuerySchema, 
  PricesQuerySchema,
  FetcherQuery,
  SearchQuery,
  PricesQuery 
} from './types';
import { ApiResponse, PaginatedResponse } from '@/types';
import { logger } from '@/utils/logger';

export class FetcherController {
  /**
   * GET /api/news - Get aggregated news
   */
  async getNews(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query = FetcherQuerySchema.parse(req.query) as FetcherQuery;
      const result = await fetcherService.getNews(query);
      
      const response: ApiResponse<PaginatedResponse<any>> = {
        success: true,
        data: {
          items: result.items,
          total: result.total,
          page: query.page,
          limit: query.limit,
          hasMore: result.items.length === query.limit,
          totalPages: Math.ceil(result.total / query.limit),
        },
        message: `Successfully fetched ${result.items.length} news items`,
        timestamp: new Date().toISOString(),
      };

      // Add metadata
      res.setHeader('X-Sources', result.sources.join(','));
      res.setHeader('X-Cached', result.cached.toString());
      res.setHeader('X-Request-Time', result.timestamp.toString());
      
      if (result.errors?.length) {
        res.setHeader('X-Source-Errors', result.errors.length.toString());
      }

      res.json(response);
    } catch (error) {
      logger.error('❌ Controller error in getNews:', error);
      next(error);
    }
  }

  /**
   * GET /api/feed - Get aggregated feed
   */
  async getFeed(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query = FetcherQuerySchema.parse(req.query) as FetcherQuery;
      const result = await fetcherService.getFeed(query);
      
      const response: ApiResponse<PaginatedResponse<any>> = {
        success: true,
        data: {
          items: result.items,
          total: result.total,
          page: query.page,
          limit: query.limit,
          hasMore: result.items.length === query.limit,
          totalPages: Math.ceil(result.total / query.limit),
        },
        message: `Successfully fetched ${result.items.length} feed items`,
        timestamp: new Date().toISOString(),
      };

      // Add metadata
      res.setHeader('X-Sources', result.sources.join(','));
      res.setHeader('X-Cached', result.cached.toString());
      res.setHeader('X-Request-Time', result.timestamp.toString());
      
      if (result.errors?.length) {
        res.setHeader('X-Source-Errors', result.errors.length.toString());
      }

      res.json(response);
    } catch (error) {
      logger.error('❌ Controller error in getFeed:', error);
      next(error);
    }
  }

  /**
   * GET /api/flash - Get flash news
   */
  async getFlash(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query = FetcherQuerySchema.parse(req.query) as FetcherQuery;
      const result = await fetcherService.getFlash(query);
      
      const response: ApiResponse<PaginatedResponse<any>> = {
        success: true,
        data: {
          items: result.items,
          total: result.total,
          page: query.page,
          limit: query.limit,
          hasMore: result.items.length === query.limit,
          totalPages: Math.ceil(result.total / query.limit),
        },
        message: `Successfully fetched ${result.items.length} flash news items`,
        timestamp: new Date().toISOString(),
      };

      // Add metadata
      res.setHeader('X-Sources', result.sources.join(','));
      res.setHeader('X-Cached', result.cached.toString());
      res.setHeader('X-Request-Time', result.timestamp.toString());
      
      if (result.errors?.length) {
        res.setHeader('X-Source-Errors', result.errors.length.toString());
      }

      res.json(response);
    } catch (error) {
      logger.error('❌ Controller error in getFlash:', error);
      next(error);
    }
  }

  /**
   * GET /api/search - Search news
   */
  async searchNews(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query = SearchQuerySchema.parse(req.query) as SearchQuery;
      const result = await fetcherService.searchNews(query);
      
      const response: ApiResponse<PaginatedResponse<any>> = {
        success: true,
        data: {
          items: result.items,
          total: result.total,
          page: query.page,
          limit: query.limit,
          hasMore: result.items.length === query.limit,
          totalPages: Math.ceil(result.total / query.limit),
        },
        message: `Found ${result.items.length} items for "${query.q}"`,
        timestamp: new Date().toISOString(),
      };

      // Add metadata
      res.setHeader('X-Sources', result.sources.join(','));
      res.setHeader('X-Cached', result.cached.toString());
      res.setHeader('X-Request-Time', result.timestamp.toString());
      res.setHeader('X-Search-Query', query.q);
      
      if (result.errors?.length) {
        res.setHeader('X-Source-Errors', result.errors.length.toString());
      }

      res.json(response);
    } catch (error) {
      logger.error('❌ Controller error in searchNews:', error);
      next(error);
    }
  }

  /**
   * GET /api/hot-tweets - Get hot tweets
   */
  async getHotTweets(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query = FetcherQuerySchema.parse(req.query) as FetcherQuery;
      const result = await fetcherService.getHotTweets(query);
      
      const response: ApiResponse<PaginatedResponse<any>> = {
        success: true,
        data: {
          items: result.items,
          total: result.total,
          page: query.page,
          limit: query.limit,
          hasMore: result.items.length === query.limit,
          totalPages: Math.ceil(result.total / query.limit),
        },
        message: `Successfully fetched ${result.items.length} hot tweets`,
        timestamp: new Date().toISOString(),
      };

      // Add metadata
      res.setHeader('X-Sources', result.sources.join(','));
      res.setHeader('X-Cached', result.cached.toString());
      res.setHeader('X-Request-Time', result.timestamp.toString());
      
      if (result.errors?.length) {
        res.setHeader('X-Source-Errors', result.errors.length.toString());
      }

      res.json(response);
    } catch (error) {
      logger.error('❌ Controller error in getHotTweets:', error);
      next(error);
    }
  }

  /**
   * GET /api/prices - Get price data
   */
  async getPrices(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query = PricesQuerySchema.parse(req.query) as PricesQuery;
      const result = await fetcherService.getPrices(query);
      
      const response: ApiResponse<any[]> = {
        success: true,
        data: result.items,
        message: `Successfully fetched ${result.items.length} price items`,
        timestamp: new Date().toISOString(),
      };

      // Add metadata
      res.setHeader('X-Sources', result.sources.join(','));
      res.setHeader('X-Cached', result.cached.toString());
      res.setHeader('X-Request-Time', result.timestamp.toString());
      
      if (result.errors?.length) {
        res.setHeader('X-Source-Errors', result.errors.length.toString());
      }

      res.json(response);
    } catch (error) {
      logger.error('❌ Controller error in getPrices:', error);
      next(error);
    }
  }

  /**
   * GET /api/sources - Get available sources
   */
  async getSources(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const sources = fetcherService.getAvailableSources();
      
      const response: ApiResponse<any[]> = {
        success: true,
        data: sources,
        message: `Found ${sources.length} available sources`,
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      logger.error('❌ Controller error in getSources:', error);
      next(error);
    }
  }

  /**
   * GET /api/stats - Get fetcher statistics
   */
  async getStats(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const stats = fetcherService.getStats();
      
      const response: ApiResponse<any> = {
        success: true,
        data: stats,
        message: 'Fetcher statistics retrieved successfully',
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      logger.error('❌ Controller error in getStats:', error);
      next(error);
    }
  }

  /**
   * POST /api/cache/clear - Clear fetcher cache
   */
  async clearCache(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      fetcherService.clearCache();
      
      const response: ApiResponse<null> = {
        success: true,
        data: null,
        message: 'Cache cleared successfully',
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      logger.error('❌ Controller error in clearCache:', error);
      next(error);
    }
  }

  /**
   * GET /api/health - Health check for fetcher
   */
  async healthCheck(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const stats = fetcherService.getStats();
      const sources = fetcherService.getAvailableSources();
      
      const enabledSources = sources.filter(s => s.enabled);
      const healthStatus = {
        status: 'healthy',
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
        sources: {
          total: sources.length,
          enabled: enabledSources.length,
          disabled: sources.length - enabledSources.length,
        },
        cache: {
          hits: stats.cacheStats?.hits || 0,
          misses: stats.cacheStats?.misses || 0,
          keys: stats.cacheStats?.keys || 0,
        },
        requests: {
          total: stats.totalRequests,
          successful: stats.successfulRequests,
          failed: stats.failedRequests,
          successRate: stats.totalRequests > 0 
            ? (stats.successfulRequests / stats.totalRequests * 100).toFixed(2) + '%'
            : '0%',
        },
      };

      const response: ApiResponse<any> = {
        success: true,
        data: healthStatus,
        message: 'Fetcher is healthy',
        timestamp: new Date().toISOString(),
      };

      res.json(response);
    } catch (error) {
      logger.error('❌ Controller error in healthCheck:', error);
      next(error);
    }
  }
}

// Export controller instance
export const fetcherController = new FetcherController();
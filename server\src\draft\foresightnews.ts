import { writeFileSync } from "node:fs"
import { parseCompressedProperties } from "./decode"
import path from "node:path"

const headers = {
  "accept": "application/json, text/plain, */*",
  "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
  "cache-control": "no-cache",
  "pragma": "no-cache",
  "priority": "u=1, i",
  "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
  "sec-ch-ua-mobile": "?0",
  "sec-ch-ua-platform": "\"Windows\"",
  "sec-fetch-dest": "empty",
  "sec-fetch-mode": "cors",
  "sec-fetch-site": "same-site",
  "x-requested-with": "XMLHttpRequest",
  "cookie": "ssxmod_itna=Qui=1GCb0KiIxYQiQo0=GR3tD=tG77DwxBP017sDuxiK08D6BDBR0QDtjmTFkTCDGxB+oyUr8Dv1DBkbxbDnqD80DQeDvYxwu=GM77x7xvT8S0b+omCWGx0IC4UQx0k=xRh2b0dhOv6k0oHrz7FHYr4DHxi8DB9DtjIHDenaDCeDQxirDD4DACPDFxibDinAT4DdC3fEAREp7DGrDlKDRo4KC4GWDiPD72Oih2OXdRoDTxiaDDpIM/oiDGvi41I7Dm4vpUA6wDD1qWSeT0oD9E4DsaiSD2oD0EtMD68E+Tt9dOo6n40OD0IUKawe2Q/WLtcm098B0bSDol+qtbx8RD3b44RD7GbYGiATxbDxFT4U2eCDqKGPCimWwHCooDiTiCIzhqi+eMusxmTqjGmBYTiNIQxdEhS2dElm+hxyD=LhxMA5tB5a7f+2dGjxCipk75Y144D; ssxmod_itna2=Qui=1GCb0KiIxYQiQo0=GR3tD=tG77DwxBP017sDuxiK08D6BDBR0QDtjmTFkTCDGxB+oyUr8D5bD8+eibthKZgT5Q9cu2X38xFGSWmtPD",
  "Referer": "https://foresightnews.pro/",
  "Referrer-Policy": "strict-origin-when-cross-origin"
}
const endpoints = {
  // flash 快讯
  dayNews: "https://api.foresightnews.pro/v1/dayNews?date=20250708",
  // 新闻 
  news: "https://api.foresightnews.pro/v1/news?page=1&size=4",
  // 订阅源
  feed: "https://api.foresightnews.pro/v2/feed?page=5&size=30",
} as const;

const fetchData = async (type: keyof typeof endpoints) => {
  await fetch(endpoints[type], {
    headers,
    "method": "GET"
  })
    .then(res => res.json())
    .then(res => {
      const data = parseCompressedProperties(res)
      console.log(data)
      writeFileSync(path.resolve(__dirname, `foresightnews.${type}.json`), JSON.stringify(data, null, 2))
    })
}
const run = async () => {
  for (const endpoint in endpoints) {
    await fetchData(endpoint as keyof typeof endpoints)
  }
}
run()
import React, { useEffect, useState } from "react";
import { View, Text, ScrollView, StyleSheet, Alert } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";

import { RootStackParamList, NewsArticle } from "../../types";
import { useNewsStore } from "../../store";
import { apiService } from "../../services/api";
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
} from "../../constants";
import { useTheme } from "../../components/common/ThemeProvider";
import PullToRefresh from "../../components/ui/PullToRefresh";
import InlineSearchBar from "../../components/ui/InlineSearchBar";
import FeaturedSection from "../../components/home/<USER>";
import TwitterSection, {
  mockTwitterPosts,
} from "../../components/home/<USER>";
import TrendingSection from "../../components/home/<USER>";
import PageHeader from "../../components/ui/PageHeader";
import { Ionicons } from "@expo/vector-icons";
import NetworkStatusIndicator from "../../components/ui/NetworkStatusIndicator";
import { useNetworkStatus } from "../../hooks/useNetworkStatus";

type HomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const HomeScreen: React.FC = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { colors } = useTheme();
  const {
    articles,
    isLoading,
    error,
    loadArticles,
    refreshArticles,
    searchArticles
  } = useNewsStore();
  const networkStatus = useNetworkStatus();

  const [refreshing, setRefreshing] = useState(false);
  const [trendingArticles, setTrendingArticles] = useState<NewsArticle[]>([]);
  const [hotTweets, setHotTweets] = useState<any[]>([]);
  const [searchHistory, setSearchHistory] = useState<string[]>([
    "比特币突破新高",
    "DeFi总锁仓价值",
    "NFT艺术品",
  ]);

  const styles = createStyles(colors);

  useEffect(() => {
    initializeData();
  }, []);

  const initializeData = async () => {
    try {
      // 并行加载所有数据
      await Promise.all([
        loadArticles(1), // 加载文章列表
        loadTrendingArticles(), // 加载热门文章
        loadHotTweets(), // 加载热门推特
      ]);
    } catch (error) {
      console.error('初始化数据失败:', error);
    }
  };

  const loadTrendingArticles = async () => {
    try {
      const response = await apiService.getTrendingArticles(5);
      if (response.success) {
        setTrendingArticles(response.data);
        // 检查是否使用了缓存或模拟数据
        if (response.message?.includes('缓存数据')) {
          networkStatus.updateStatus('cached', '热门文章使用缓存数据');
        } else if (response.message?.includes('模拟数据')) {
          networkStatus.updateStatus('mock', '热门文章使用模拟数据');
        }
      }
    } catch (error) {
      console.error('加载热门文章失败:', error);
      networkStatus.showNetworkError(error as Error);
    }
  };

  const loadHotTweets = async () => {
    try {
      const response = await apiService.getHotTweets(5);
      if (response.success) {
        setHotTweets(response.data);
        // 检查是否使用了缓存或模拟数据
        if (response.message?.includes('缓存数据')) {
          networkStatus.updateStatus('cached', '推特数据使用缓存');
        } else if (response.message?.includes('模拟数据')) {
          networkStatus.updateStatus('mock', '推特数据使用模拟数据');
        }
      }
    } catch (error) {
      console.error('加载热门推特失败:', error);
      networkStatus.showNetworkError(error as Error);
      // 如果API失败，使用模拟数据
      setHotTweets(mockTwitterPosts);
      networkStatus.updateStatus('mock', '推特数据使用本地模拟数据');
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        refreshArticles(),
        loadTrendingArticles(),
        loadHotTweets(),
      ]);
    } catch (error) {
      console.error('刷新数据失败:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const navigateToArticle = (articleId: string) => {
    navigation.navigate("ArticleDetail", { articleId });
  };

  const handleSearch = async (query: string) => {
    // 添加到搜索历史
    if (!searchHistory.includes(query)) {
      setSearchHistory((prev) => [query, ...prev.slice(0, 9)]);
    }

    // 执行搜索
    await searchArticles(query);
  };

  const clearSearchHistory = () => {
    setSearchHistory([]);
  };

  const handleSeeAllPress = (section: string) => {
    switch (section) {
      case "featured":
        navigation.navigate("FeaturedList");
        break;
      case "twitter":
        navigation.navigate("TwitterList");
        break;
      case "trending":
        navigation.navigate("TrendingList");
        break;
      default:
        console.log(`查看全部 ${section}`);
    }
  };

  const handleTwitterPostPress = (postId: string) => {
    // 处理推特帖子点击
    console.log(`点击推特帖子: ${postId}`);
  };

  // 分类文章数据
  const getFeaturedArticles = () => articles.slice(0, 5);
  const getCurrentTrendingArticles = () => trendingArticles.length > 0 ? trendingArticles : articles.slice(0, 5);
  const getCurrentHotTweets = () => hotTweets.length > 0 ? hotTweets : mockTwitterPosts;

  const renderHeader = () => (
    <PageHeader
      title="区块链资讯"
      rightComponent={
        <InlineSearchBar
          onSearch={handleSearch}
          searchHistory={searchHistory}
          onClearHistory={clearSearchHistory}
          style={styles.searchBar}
        />
      }
    />
  );

  const renderContent = () => (
    <View>
      <FeaturedSection
        articles={getFeaturedArticles()}
        onArticlePress={navigateToArticle}
        onSeeAllPress={() => handleSeeAllPress("featured")}
      />

      <TwitterSection
        posts={getCurrentHotTweets()}
        onPostPress={handleTwitterPostPress}
        onSeeAllPress={() => handleSeeAllPress("twitter")}
      />

      <TrendingSection
        articles={getCurrentTrendingArticles()}
        onArticlePress={navigateToArticle}
        onSeeAllPress={() => handleSeeAllPress("trending")}
      />
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <NetworkStatusIndicator
        isVisible={networkStatus.showIndicator}
        status={networkStatus.status}
        message={networkStatus.message}
      />
      {renderHeader()}
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <PullToRefresh refreshing={refreshing} onRefresh={onRefresh} />
        }
        decelerationRate="normal"
        scrollEventThrottle={16}
        bounces={true}
        bouncesZoom={false}
        overScrollMode="auto"
      >
        {renderContent()}
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },

    searchBar: {
      // 移除flex: 1，让搜索按钮保持固定大小
    },
    searchButton: {
      padding: SPACING.sm,
    },
    listContent: {
      paddingBottom: SPACING.xl,
    },
    section: {
      marginVertical: SPACING.md,
    },
    sectionTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: "bold",
      color: colors.text,
      marginHorizontal: SPACING.md,
      marginBottom: SPACING.sm,
    },
    featuredCard: {
      width: 280,
      marginLeft: SPACING.md,
      overflow: "hidden",
    },
    featuredImage: {
      width: "100%",
      height: 160,
      borderRadius: BORDER_RADIUS.md,
      marginBottom: SPACING.sm,
    },
    featuredContent: {
      paddingTop: 0,
    },
    featuredTitle: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
      marginBottom: SPACING.xs,
      lineHeight: 20,
    },
    featuredCategory: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    articleCard: {
      marginHorizontal: SPACING.md,
      marginVertical: SPACING.xs,
    },
    articleRow: {
      flexDirection: "row",
    },
    articleImage: {
      width: 100,
      height: 80,
      borderRadius: BORDER_RADIUS.md,
      marginRight: SPACING.md,
    },
    articleContent: {
      flex: 1,
    },
    articleTitle: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
      marginBottom: SPACING.xs,
      lineHeight: 20,
    },
    articleSummary: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginBottom: SPACING.sm,
      lineHeight: 18,
    },
    articleMeta: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    articleCategory: {
      fontSize: FONT_SIZES.xs,
      color: colors.primary,
      fontWeight: "500",
    },
    articleTime: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
    },
  });

export default HomeScreen;

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { useAppStore } from '../../store';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants';
import { useTheme } from '../../components/common/ThemeProvider';
import TouchableScale from '../../components/ui/TouchableScale';
import PageHeader from '../../components/ui/PageHeader';
import CustomSwitch from '../../components/ui/CustomSwitch';
import { RootStackParamList } from '../../types';

type ProfileScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const ProfileScreen: React.FC = () => {
  const navigation = useNavigation<ProfileScreenNavigationProp>();
  const { theme, setTheme, user, favorites, clearFavorites } = useAppStore();
  const { colors } = useTheme();

  const styles = createStyles(colors);

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  const handleClearFavorites = () => {
    Alert.alert(
      '清空收藏',
      '确定要清空所有收藏的文章吗？',
      [
        { text: '取消', style: 'cancel' },
        { text: '确定', style: 'destructive', onPress: clearFavorites },
      ]
    );
  };

  const renderHeader = () => (
    <PageHeader title="个人中心" />
  );

  const renderUserInfo = () => (
    <View style={styles.userSection}>
      <View style={styles.avatar}>
        <Ionicons name="person" size={40} color={colors.textSecondary} />
      </View>
      <View style={styles.userInfo}>
        <Text style={styles.userName}>
          {user?.name || '未登录用户'}
        </Text>
        <Text style={styles.userEmail}>
          {user?.email || '点击登录'}
        </Text>
      </View>
      <TouchableScale style={styles.editButton}>
        <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
      </TouchableScale>
    </View>
  );

  const renderStatsSection = () => (
    <View style={styles.statsSection}>
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>{favorites.length}</Text>
        <Text style={styles.statLabel}>收藏</Text>
      </View>
      <View style={styles.statDivider} />
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>0</Text>
        <Text style={styles.statLabel}>阅读</Text>
      </View>
      <View style={styles.statDivider} />
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>0</Text>
        <Text style={styles.statLabel}>分享</Text>
      </View>
    </View>
  );

  const renderMenuItem = (
    icon: string,
    title: string,
    subtitle?: string,
    onPress?: () => void,
    rightComponent?: React.ReactNode,
    position?: 'first' | 'middle' | 'last' | 'single'
  ) => {
    const getItemStyle = () => {
      const baseStyle = [styles.menuItem];

      switch (position) {
        case 'first':
          baseStyle.push(styles.menuItemFirst);
          break;
        case 'middle':
          baseStyle.push(styles.menuItemMiddle);
          break;
        case 'last':
          baseStyle.push(styles.menuItemLast);
          break;
        case 'single':
          baseStyle.push(styles.menuItemSingle);
          break;
        default:
          baseStyle.push(styles.menuItemSingle);
      }

      return baseStyle;
    };

    return (
      <TouchableScale style={getItemStyle()} onPress={onPress}>
        <View style={styles.menuItemLeft}>
          <Ionicons name={icon as any} size={24} color={colors.primary} />
          <View style={styles.menuItemText}>
            <Text style={styles.menuItemTitle}>{title}</Text>
            {subtitle && <Text style={styles.menuItemSubtitle}>{subtitle}</Text>}
          </View>
        </View>
        {rightComponent || (
          <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
        )}
      </TouchableScale>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}
      <ScrollView showsVerticalScrollIndicator={false}>
        {renderUserInfo()}
        {renderStatsSection()}

        <View style={styles.menuSection}>
          <Text style={styles.sectionTitle}>设置</Text>
          <View style={styles.menuGroup}>
            {renderMenuItem(
              'moon',
              '深色模式',
              theme === 'dark' ? '已开启' : '已关闭',
              undefined,
              <CustomSwitch
                value={theme === 'dark'}
                onValueChange={toggleTheme}
                trackColor={{ false: colors.border, true: colors.primary }}
                thumbColor={colors.background}
              />,
              'first'
            )}

            {renderMenuItem(
              'notifications',
              '推送通知',
              '接收最新资讯推送',
              undefined,
              undefined,
              'middle'
            )}

            {renderMenuItem(
              'language',
              '语言设置',
              '简体中文',
              undefined,
              undefined,
              'last'
            )}
          </View>
        </View>

        <View style={styles.menuSection}>
          <Text style={styles.sectionTitle}>内容</Text>
          <View style={styles.menuGroup}>
            {renderMenuItem(
              'heart',
              '我的收藏',
              `${favorites.length} 篇文章`,
              undefined,
              undefined,
              'first'
            )}

            {renderMenuItem(
              'time',
              '阅读历史',
              '查看阅读记录',
              undefined,
              undefined,
              'middle'
            )}

            {renderMenuItem(
              'trash',
              '清空收藏',
              '删除所有收藏的文章',
              handleClearFavorites,
              undefined,
              'last'
            )}
          </View>
        </View>

        <View style={styles.menuSection}>
          <Text style={styles.sectionTitle}>开发工具</Text>
          <View style={styles.menuGroup}>
            {renderMenuItem(
              'code-slash',
              'API 测试',
              '测试前后端连接状态',
              () => navigation.navigate('APITest'),
              undefined,
              'single'
            )}
          </View>
        </View>

        <View style={styles.menuSection}>
          <Text style={styles.sectionTitle}>其他</Text>
          <View style={styles.menuGroup}>
            {renderMenuItem(
              'help-circle',
              '帮助与反馈',
              '使用帮助和问题反馈',
              undefined,
              undefined,
              'first'
            )}

            {renderMenuItem(
              'information-circle',
              '关于我们',
              '版本 1.0.0',
              undefined,
              undefined,
              'last'
            )}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },

    userSection: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.surface,
      margin: SPACING.md,
      padding: SPACING.md,
      borderRadius: 12,
    },
    avatar: {
      width: 60,
      height: 60,
      borderRadius: 30,
      backgroundColor: colors.border,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: SPACING.md,
    },
    userInfo: {
      flex: 1,
    },
    userName: {
      fontSize: FONT_SIZES.lg,
      fontWeight: '600',
      color: colors.text,
      marginBottom: SPACING.xs,
    },
    userEmail: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    editButton: {
      padding: SPACING.sm,
    },
    statsSection: {
      flexDirection: 'row',
      backgroundColor: colors.surface,
      marginHorizontal: SPACING.md,
      marginBottom: SPACING.md,
      borderRadius: 12,
      paddingVertical: SPACING.md,
    },
    statItem: {
      flex: 1,
      alignItems: 'center',
    },
    statNumber: {
      fontSize: FONT_SIZES['2xl'],
      fontWeight: 'bold',
      color: colors.primary,
      marginBottom: SPACING.xs,
    },
    statLabel: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    statDivider: {
      width: 1,
      backgroundColor: colors.border,
      marginVertical: SPACING.sm,
    },
    menuSection: {
      marginBottom: SPACING.lg,
    },
    sectionTitle: {
      fontSize: FONT_SIZES.md,
      fontWeight: '600',
      color: colors.text,
      marginHorizontal: SPACING.md,
      marginBottom: SPACING.sm,
    },
    menuGroup: {
      backgroundColor: colors.surface,
      marginHorizontal: SPACING.md,
      borderRadius: BORDER_RADIUS.lg,
      overflow: 'hidden',
    },
    menuItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.borderLight,
    },
    menuItemFirst: {
      borderTopLeftRadius: BORDER_RADIUS.lg,
      borderTopRightRadius: BORDER_RADIUS.lg,
    },
    menuItemMiddle: {
      // 中间项不需要额外的圆角
    },
    menuItemLast: {
      borderBottomLeftRadius: BORDER_RADIUS.lg,
      borderBottomRightRadius: BORDER_RADIUS.lg,
      borderBottomWidth: 0, // 最后一项不需要底部边框
    },
    menuItemSingle: {
      borderRadius: BORDER_RADIUS.lg,
      borderBottomWidth: 0,
    },
    menuItemLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    menuItemText: {
      marginLeft: SPACING.md,
      flex: 1,
    },
    menuItemTitle: {
      fontSize: FONT_SIZES.md,
      color: colors.text,
      marginBottom: 2,
    },
    menuItemSubtitle: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
  });

export default ProfileScreen;

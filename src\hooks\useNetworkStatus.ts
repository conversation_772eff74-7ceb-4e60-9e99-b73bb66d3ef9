import { useState, useEffect, useCallback } from "react";
import NetInfo from "@react-native-community/netinfo";
console.log("NetInfo:", NetInfo);
export type NetworkStatus = "online" | "offline" | "cached" | "mock";

interface NetworkStatusState {
  isConnected: boolean;
  status: NetworkStatus;
  message: string;
  showIndicator: boolean;
}

interface UseNetworkStatusReturn extends NetworkStatusState {
  updateStatus: (status: NetworkStatus, message?: string) => void;
  hideIndicator: () => void;
  showNetworkError: (error: Error) => void;
}

export const useNetworkStatus = (): UseNetworkStatusReturn => {
  const [state, setState] = useState<NetworkStatusState>({
    isConnected: true,
    status: "online",
    message: "",
    showIndicator: false,
  });

  useEffect(() => {
    console.log("useNetworkStatus:", NetInfo);
    const unsubscribe = NetInfo.addEventListener((state) => {
      const isConnected = state.isConnected ?? false;

      setState((prev) => ({
        ...prev,
        isConnected,
        status: isConnected ? "online" : "offline",
        message: isConnected ? "网络连接正常" : "网络连接断开",
        showIndicator: !isConnected,
      }));

      // 自动隐藏在线状态指示器
      if (isConnected) {
        setTimeout(() => {
          setState((prev) => ({ ...prev, showIndicator: false }));
        }, 2000);
      }
    });

    return unsubscribe;
  }, []);

  const updateStatus = useCallback(
    (status: NetworkStatus, message?: string) => {
      const statusMessages = {
        online: "网络连接正常",
        offline: "网络连接断开",
        cached: "使用缓存数据",
        mock: "使用模拟数据（开发模式）",
      };

      setState((prev) => ({
        ...prev,
        status,
        message: message || statusMessages[status],
        showIndicator: true,
      }));

      // 自动隐藏指示器（除了离线状态）
      if (status !== "offline") {
        setTimeout(() => {
          setState((prev) => ({ ...prev, showIndicator: false }));
        }, 3000);
      }
    },
    []
  );

  const hideIndicator = useCallback(() => {
    setState((prev) => ({ ...prev, showIndicator: false }));
  }, []);

  const showNetworkError = useCallback(
    (error: Error) => {
      let status: NetworkStatus = "offline";
      let message = "网络请求失败";

      if (error.message.includes("网络连接失败")) {
        status = "offline";
        message = "网络连接失败，请检查网络设置";
      } else if (error.message.includes("缓存数据")) {
        status = "cached";
        message = "网络异常，正在使用缓存数据";
      } else if (error.message.includes("模拟数据")) {
        status = "mock";
        message = "后端服务不可用，使用模拟数据";
      } else if (error.message.includes("服务暂时不可用")) {
        status = "offline";
        message = "服务暂时不可用，请稍后重试";
      }

      updateStatus(status, message);
    },
    [updateStatus]
  );

  return {
    ...state,
    updateStatus,
    hideIndicator,
    showNetworkError,
  };
};

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../common/ThemeProvider';
import { SPACING, FONT_SIZES, BORDER_RADIUS, FONT_WEIGHTS } from '../../constants';
import { NewsArticle } from '../../types';
import TouchableScale from './TouchableScale';

interface RelatedArticlesProps {
  articles: NewsArticle[];
  onArticlePress: (articleId: string) => void;
  style?: any;
}

const RelatedArticles: React.FC<RelatedArticlesProps> = ({
  articles,
  onArticlePress,
  style,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  const renderArticleItem = ({ item, index }: { item: NewsArticle; index: number }) => (
    <TouchableScale
      style={styles.articleCard}
      onPress={() => onArticlePress(item.id)}
    >
      <Image
        source={{ uri: item.imageUrl || 'https://via.placeholder.com/120x80' }}
        style={styles.articleImage}
      />
      <View style={styles.articleContent}>
        <Text style={styles.articleTitle} numberOfLines={2}>
          {item.title}
        </Text>
        <Text style={styles.articleSummary} numberOfLines={2}>
          {item.summary}
        </Text>
        <View style={styles.articleMeta}>
          <View style={styles.categoryBadge}>
            <Text style={styles.categoryText}>{item.category.name}</Text>
          </View>
          <Text style={styles.readTime}>{item.readTime}分钟</Text>
        </View>
      </View>
    </TouchableScale>
  );

  if (articles.length === 0) {
    return null;
  }

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.sectionTitle}>相关推荐</Text>
      <FlatList
        data={articles}
        renderItem={renderArticleItem}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      marginVertical: SPACING.lg,
    },
    sectionTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.text,
      marginHorizontal: SPACING.md,
      marginBottom: SPACING.md,
    },
    listContent: {
      paddingHorizontal: SPACING.md,
    },
    separator: {
      width: SPACING.md,
    },
    articleCard: {
      width: 280,
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.lg,
      overflow: 'hidden',
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    articleImage: {
      width: '100%',
      height: 140,
    },
    articleContent: {
      padding: SPACING.md,
    },
    articleTitle: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
      marginBottom: SPACING.xs,
      lineHeight: 20,
    },
    articleSummary: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginBottom: SPACING.sm,
      lineHeight: 18,
    },
    articleMeta: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    categoryBadge: {
      backgroundColor: colors.primary + '20',
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.xs,
      borderRadius: BORDER_RADIUS.sm,
    },
    categoryText: {
      fontSize: FONT_SIZES.xs,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    readTime: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      fontWeight: FONT_WEIGHTS.medium,
    },
  });

export default RelatedArticles;

import { Router } from 'express';
import { flashController } from '@/controllers/flash';

const router: Router = Router();

/**
 * @route GET /api/flash
 * @desc Get paginated flash news items
 * @access Public
 * @query {number} page - Page number (default: 1)
 * @query {number} limit - Items per page (default: 20, max: 100)
 */
router.get('/', flashController.getFlash);

/**
 * @route GET /api/flash/search
 * @desc Search flash news items
 * @access Public
 * @query {string} q - Search query (required)
 * @query {number} page - Page number (default: 1)
 * @query {number} limit - Items per page (default: 20, max: 50)
 */
router.get('/search', flashController.searchFlash);

export default router;
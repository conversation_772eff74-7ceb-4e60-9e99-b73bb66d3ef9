import { Router } from 'express';
import { newsController } from '@/controllers/news';
import { validateQuery } from '@/middlewares/validation';
import { SearchNewsQuerySchema } from '@/types';

const router: Router = Router();

/**
 * @route GET /api/search
 * @desc Search across news articles
 * @access Public
 * @query {string} q - Search query (required)
 * @query {number} page - Page number (default: 1)
 * @query {number} limit - Items per page (default: 20, max: 50)
 * @query {string} category - Filter by category slug
 * @query {string} sortBy - Sort by field (publishedAt, relevance)
 */
router.get(
  '/',
  validateQuery(SearchNewsQuerySchema),
  newsController.searchNews
);

export default router;
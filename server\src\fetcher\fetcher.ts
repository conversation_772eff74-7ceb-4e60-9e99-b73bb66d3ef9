import {
  SourceConfig,
  ProxyConfig,
  FetcherConfig,
  RequestHeaders,
  FetchOptions,
  FetchResult,
  AggregatedResult,
  UnifiedItem,
  TwitterItem,
  PriceData
} from './types';
import { logger } from '@/utils/logger';
import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import { SocksProxyAgent } from 'socks-proxy-agent';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { HttpProxyAgent } from 'http-proxy-agent';
import crypto from 'crypto';
import NodeCache from 'node-cache';
import { response } from 'express';
import { parseCompressedProperties } from '@/draft/decode';

// ========================================
// User Agent and Header Management
// ========================================

export class HeaderManager {
  private static readonly USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:132.0) Gecko/20100101 Firefox/132.0',
    'Mozilla/5.0 (X11; Linux x86_64; rv:132.0) Gecko/20100101 Firefox/132.0',
  ];

  private static readonly ACCEPT_LANGUAGES = [
    'zh-CN,zh;q=0.9,en;q=0.8',
    'en-US,en;q=0.9,zh;q=0.8',
    'zh-TW,zh;q=0.9,en;q=0.8',
    'ja-JP,ja;q=0.9,en;q=0.8',
    'ko-KR,ko;q=0.9,en;q=0.8',
  ];

  private static readonly PLATFORMS = [
    'Windows',
    'macOS',
    'Linux',
  ];

  static getRandomUserAgent(): string {
    return this.USER_AGENTS[Math.floor(Math.random() * this.USER_AGENTS.length)];
  }

  static getRandomAcceptLanguage(): string {
    return this.ACCEPT_LANGUAGES[Math.floor(Math.random() * this.ACCEPT_LANGUAGES.length)];
  }

  static generateRandomIP(): string {
    const octets = [];
    for (let i = 0; i < 4; i++) {
      // Generate realistic IP ranges
      if (i === 0) {
        const ranges = [10, 172, 192]; // Private IP ranges
        octets.push(ranges[Math.floor(Math.random() * ranges.length)]);
      } else if (i === 1 && octets[0] === 172) {
        octets.push(Math.floor(Math.random() * 16) + 16); // 172.16-31.x.x
      } else if (i === 1 && octets[0] === 192) {
        octets.push(168); // 192.168.x.x
      } else {
        octets.push(Math.floor(Math.random() * 256));
      }
    }
    return octets.join('.');
  }

  static createHeaders(sourceConfig: SourceConfig, options: Partial<RequestHeaders> = {}): RequestHeaders {
    const baseHeaders: RequestHeaders = {
      'User-Agent': this.getRandomUserAgent(),
      'Accept': 'application/json, text/plain, */*',
      'Accept-Language': this.getRandomAcceptLanguage(),
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
      'X-Forwarded-For': this.generateRandomIP(),
      'sec-ch-ua': `"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"`,
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': `"${this.PLATFORMS[Math.floor(Math.random() * this.PLATFORMS.length)]}"`,
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-origin',
      ...sourceConfig.headers,
      ...options,
    };

    // Clean up undefined values
    Object.keys(baseHeaders).forEach(key => {
      if (baseHeaders[key as keyof RequestHeaders] === undefined) {
        delete baseHeaders[key as keyof RequestHeaders];
      }
    });

    return baseHeaders;
  }
}

// ========================================
// Proxy Manager
// ========================================

export class ProxyManager {
  private proxies: ProxyConfig[] = [];
  private currentProxyIndex = 0;

  constructor(proxies: ProxyConfig[]) {
    this.proxies = proxies.filter(p => p.enabled);
  }

  getNextProxy(): ProxyConfig | null {
    if (this.proxies.length === 0) return null;

    const proxy = this.proxies[this.currentProxyIndex];
    this.currentProxyIndex = (this.currentProxyIndex + 1) % this.proxies.length;
    return proxy || null;
  }

  createProxyAgent(proxy: ProxyConfig) {
    const proxyUrl = `${proxy.protocol}://${proxy.host}:${proxy.port}`;

    if (proxy.auth) {
      const authUrl = `${proxy.protocol}://${proxy.auth.username}:${proxy.auth.password}@${proxy.host}:${proxy.port}`;

      switch (proxy.protocol) {
        case 'socks5':
          return new SocksProxyAgent(authUrl);
        case 'https':
          return new HttpsProxyAgent(authUrl);
        case 'http':
          return new HttpProxyAgent(authUrl);
        default:
          throw new Error(`Unsupported proxy protocol: ${proxy.protocol}`);
      }
    } else {
      switch (proxy.protocol) {
        case 'socks5':
          return new SocksProxyAgent(proxyUrl);
        case 'https':
          return new HttpsProxyAgent(proxyUrl);
        case 'http':
          return new HttpProxyAgent(proxyUrl);
        default:
          throw new Error(`Unsupported proxy protocol: ${proxy.protocol}`);
      }
    }
  }

  getAllProxies(): ProxyConfig[] {
    return [...this.proxies];
  }

  addProxy(proxy: ProxyConfig): void {
    if (proxy.enabled) {
      this.proxies.push(proxy);
    }
  }

  removeProxy(host: string, port: number): void {
    this.proxies = this.proxies.filter(p => !(p.host === host && p.port === port));
  }
}

// ========================================
// Data Transformers
// ========================================

export class DataTransformer {
  static transformChainCatcher(response: any, sourceId: string): UnifiedItem[] {
    if (!response.data?.items) return [];

    return response.data.items.map((item: any) => ({
      id: `${sourceId}-${item.id}`,
      type: 'news_flash' as const,
      title: item.title,
      summary: item.description,
      content: item.description,
      imageUrl: item.thumb || undefined,
      publishedAt: new Date(item.releaseTime || item.createTime).toISOString(),
      tags: item.tagNames || [],
      source: {
        id: sourceId,
        name: 'ChainCatcher',
        url: 'https://www.chaincatcher.com',
      },
      keywords: item.keywords?.split(',').filter(Boolean) || [],
      newsFlashType: item.newsFlashType,
      isHot: item.isHot === 1,
    }));
  }

  static transformTechFlow(response: any, sourceId: string): UnifiedItem[] {
    if (!response.content) return [];

    return response.content.map((item: any) => ({
      id: `${sourceId}-${item.nnewflash_id}`,
      type: 'news_flash' as const,
      title: item.stitle,
      summary: item.sabstract,
      content: item.scontent?.replace(/<[^>]*>/g, '') || item.sabstract,
      sourceUrl: item.surl,
      publishedAt: new Date(item.dcreate_time).toISOString(),
      tags: [],
      source: {
        id: sourceId,
        name: 'TechFlow',
        url: 'https://www.techflowpost.com',
      },
      author: item.screate_person,
      isHot: item.is_hot === 'Y',
      engagement: {
        views: parseInt(item.ncount) || 0,
      },
    }));
  }

  static transformForesightNews(response: any, sourceId: string): UnifiedItem[] {
    if (!response.data?.list) return [];

    return response.data.list.map((item: any) => ({
      id: `${sourceId}-${item.id}`,
      type: 'news_article' as const,
      title: item.title,
      summary: item.brief,
      content: item.content?.replace(/<[^>]*>/g, '') || item.brief,
      imageUrl: item.img || undefined,
      sourceUrl: item.source_link,
      publishedAt: new Date(item.published_at * 1000).toISOString(),
      tags: item.tags?.map((tag: any) => tag.name) || [],
      source: {
        id: sourceId,
        name: 'Foresight News',
        url: 'https://foresightnews.pro',
      },
      isImportant: item.is_important,
      label: item.label,
      wikis: item.wikis || item.new_wikis,
    }));
  }

  static transformPANews(response: any, sourceId: string): UnifiedItem[] {
    if (!response.data?.flashNews) return [];

    const items: UnifiedItem[] = [];

    for (const dayGroup of response.data.flashNews) {
      for (const item of dayGroup.list) {
        items.push({
          id: `${sourceId}-${item.id}`,
          type: 'news_flash' as const,
          title: item.title,
          summary: item.desc,
          content: item.desc,
          imageUrl: item.img || undefined,
          publishedAt: new Date(item.publishTime * 1000).toISOString(),
          tags: item.tags || [],
          source: {
            id: sourceId,
            name: 'PANews',
            url: 'https://www.panewslab.com',
          },
          author: item.author?.name,
          engagement: {
            views: item.readnum || 0,
            likes: item.lovenum || 0,
          },
        });
      }
    }

    return items;
  }

  static transformTheBlockBeats(response: any, sourceId: string): UnifiedItem[] {
    if (!response.data?.list) return [];

    return response.data.list.map((item: any) => ({
      id: `${sourceId}-${item.id}`,
      type: 'news_flash' as const,
      title: item.title,
      summary: item.abstract || item.content?.replace(/<[^>]*>/g, '').substring(0, 200) + '...',
      content: item.content?.replace(/<[^>]*>/g, '') || item.abstract,
      imageUrl: item.img_url || item.c_img_url || undefined,
      sourceUrl: item.url || undefined,
      publishedAt: new Date(item.add_time * 1000).toISOString(),
      tags: item.tag_list || [],
      source: {
        id: sourceId,
        name: 'TheBlockBeats',
        url: 'https://www.theblockbeats.info',
      },
      isHot: item.is_hot === 1,
      engagement: {
        views: item.comment_count || 0,
      },
    }));
  }

  static transformOdaily(response: any, sourceId: string): UnifiedItem[] {
    if (!response.data?.items) return [];

    return response.data.items.map((item: any) => ({
      id: `${sourceId}-${item.id}`,
      type: 'news_flash' as const,
      title: item.title,
      summary: item.description,
      content: item.description,
      imageUrl: item.cover || undefined,
      sourceUrl: item.news_url || undefined,
      publishedAt: new Date(item.published_at).toISOString(),
      tags: item.extraction_tags_arr || [],
      source: {
        id: sourceId,
        name: 'Odaily',
        url: 'https://www.odaily.news',
      },
      author: item.user?.name,
      isHot: item.is_top === 1,
    }));
  }

  static getTransformer(sourceId: string): (response: any, sourceId: string) => UnifiedItem[] {
    switch (sourceId) {
      case 'chaincatcher':
        return this.transformChainCatcher;
      case 'techflow':
        return this.transformTechFlow;
      case 'foresightnews':
        return this.transformForesightNews;
      case 'panews':
        return this.transformPANews;
      case 'theblockbeats':
        return this.transformTheBlockBeats;
      case 'odaily':
        return this.transformOdaily;
      default:
        return () => [];
    }
  }
}

// ========================================
// Core Fetcher Class
// ========================================

export class BlockchainFetcher {
  private config: FetcherConfig;
  private proxyManager: ProxyManager;
  private cache: NodeCache;
  private stats: any = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0,
    sourceStats: {},
  };

  constructor(config: FetcherConfig) {
    this.config = config;
    this.proxyManager = new ProxyManager(config.proxies);
    this.cache = new NodeCache({
      stdTTL: config.cache.ttl,
      maxKeys: config.cache.maxSize,
      checkperiod: 60, // Check for expired keys every 60 seconds
    });

    this.initializeStats();
  }

  private initializeStats(): void {
    this.config.sources.forEach(source => {
      this.stats.sourceStats[source.id] = {
        requests: 0,
        successes: 0,
        failures: 0,
        averageResponseTime: 0,
      };
    });
  }

  private generateCacheKey(sourceId: string, endpoint: string, params?: any): string {
    const hash = crypto.createHash('md5');
    hash.update(sourceId + endpoint + JSON.stringify(params || {}));
    return hash.digest('hex');
  }

  private async fetchWithRetry<T>(
    url: string,
    options: AxiosRequestConfig,
    retries: number = 3,
    sourceId: string
  ): Promise<FetchResult<T>> {
    const startTime = Date.now();

    for (let attempt = 0; attempt < retries; attempt++) {
      try {
        // Add proxy if available
        const proxy = this.proxyManager.getNextProxy();
        if (proxy) {
          options.httpsAgent = this.proxyManager.createProxyAgent(proxy);
          options.httpAgent = this.proxyManager.createProxyAgent(proxy);
        }

        const response: AxiosResponse<T> = await axios(url, options);
        const responseTime = Date.now() - startTime;

        // Update stats
        this.updateStats(sourceId, true, responseTime);

        return {
          success: true,
          data: response.data,
          source: sourceId,
          cached: false,
          timestamp: Date.now(),
        };
      } catch (error) {
        logger.warn(`Fetch attempt ${attempt + 1} failed for ${sourceId}:`, error);
        console.log(`url: `, url)
        console.log(`options: `, options)
        console.log(`error: `, (error as AxiosError).response?.data)

        if (attempt === retries - 1) {
          // Update stats on final failure
          this.updateStats(sourceId, false, Date.now() - startTime);

          return {
            success: false,
            data: null,
            error: error instanceof Error ? error.message : 'Unknown error',
            source: sourceId,
            cached: false,
            timestamp: Date.now(),
          };
        }

        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }

    return {
      success: false,
      data: null,
      error: 'Max retries exceeded',
      source: sourceId,
      cached: false,
      timestamp: Date.now(),
    };
  }

  private updateStats(sourceId: string, success: boolean, responseTime: number): void {
    this.stats.totalRequests++;

    if (success) {
      this.stats.successfulRequests++;
    } else {
      this.stats.failedRequests++;
    }

    // Update average response time
    this.stats.averageResponseTime =
      (this.stats.averageResponseTime * (this.stats.totalRequests - 1) + responseTime) / this.stats.totalRequests;

    // Update source stats
    const sourceStats = this.stats.sourceStats[sourceId];
    sourceStats.requests++;

    if (success) {
      sourceStats.successes++;
    } else {
      sourceStats.failures++;
    }

    sourceStats.averageResponseTime =
      (sourceStats.averageResponseTime * (sourceStats.requests - 1) + responseTime) / sourceStats.requests;
  }

  private async fetchFromSource(
    sourceConfig: SourceConfig,
    endpoint: string,
    params?: any,
    options: FetchOptions = {}
  ): Promise<FetchResult<any>> {
    const cacheKey = this.generateCacheKey(sourceConfig.id, endpoint, params);

    // Check cache first
    if (options.cache) {
      const cached = this.cache.get(cacheKey);
      if (cached) {
        return {
          success: true,
          data: cached,
          source: sourceConfig.id,
          cached: true,
          timestamp: Date.now(),
        };
      }
    }

    const url = `${sourceConfig.baseUrl}${endpoint}`;
    const headers = HeaderManager.createHeaders(sourceConfig, options.headers);

    const axiosOptions: AxiosRequestConfig = {
      method: 'GET',
      headers,
      timeout: options.timeout || sourceConfig.timeout || 10000,
      params,
      ...options,
    };

    const result = await this.fetchWithRetry(
      url,
      axiosOptions,
      options.retries || sourceConfig.retries || 3,
      sourceConfig.id
    );
    if (sourceConfig.id === "foresightnews") {
      result.data = parseCompressedProperties(result.data)
    }
    // Cache successful results
    if (result.success && result.data && options.cache) {
      this.cache.set(cacheKey, result.data, options.cache.ttl || this.config.cache.ttl);
    }

    return result;
  }

  async fetchNews(
    sources?: string[],
    page: number = 1,
    limit: number = 20,
    options: FetchOptions = {}
  ): Promise<AggregatedResult<UnifiedItem>> {
    const targetSources = sources
      ? this.config.sources.filter(s => sources.includes(s.id) && s.enabled)
      : this.config.sources.filter(s => s.enabled);

    const fetchPromises = targetSources.map(async (source) => {
      const endpoint = source.endpoints.news || source.endpoints.flash || '';
      const params = { page, limit };

      const result = await this.fetchFromSource(source, endpoint, params, {
        ...options,
        cache: {
          key: `news-${source.id}-${page}-${limit}`,
          ttl: options.cache?.ttl || 300, // 5 minutes
        },
      });

      if (result.success && result.data) {
        const transformer = DataTransformer.getTransformer(source.id);
        return {
          ...result,
          data: transformer(result.data, source.id),
        };
      }

      return result;
    });

    const results = await Promise.all(fetchPromises);
    const items: UnifiedItem[] = [];
    const errors: Array<{ source: string; error: string }> = [];
    const successfulSources: string[] = [];

    for (const result of results) {
      if (result.success && result.data) {
        items.push(...result.data);
        successfulSources.push(result.source);
      } else {
        errors.push({
          source: result.source,
          error: result.error || 'Unknown error',
        });
      }
    }

    // Sort by publishedAt descending
    items.sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime());

    return {
      items: items.slice(0, limit),
      total: items.length,
      sources: successfulSources,
      cached: results.some(r => r.cached),
      timestamp: Date.now(),
      ...(errors.length > 0 && { errors }),
    };
  }

  async fetchFlash(
    sources?: string[],
    page: number = 1,
    limit: number = 20,
    options: FetchOptions = {}
  ): Promise<AggregatedResult<UnifiedItem>> {
    return this.fetchNews(sources, page, limit, options);
  }

  async fetchFeed(
    sources?: string[],
    page: number = 1,
    limit: number = 20,
    options: FetchOptions = {}
  ): Promise<AggregatedResult<UnifiedItem>> {
    const targetSources = sources
      ? this.config.sources.filter(s => sources.includes(s.id) && s.enabled)
      : this.config.sources.filter(s => s.enabled);

    const foresightSources = targetSources.filter(s => s.id === 'foresightnews');

    if (foresightSources.length === 0) {
      return this.fetchNews(sources, page, limit, options);
    }

    const fetchPromises = foresightSources.map(async (source) => {
      const endpoint = source.endpoints.feed || '';
      const params = { page, size: limit };

      const result = await this.fetchFromSource(source, endpoint, params, {
        ...options,
        cache: {
          key: `feed-${source.id}-${page}-${limit}`,
          ttl: options.cache?.ttl || 300,
        },
      });

      if (result.success && result.data) {
        const transformer = DataTransformer.getTransformer(source.id);
        return {
          ...result,
          data: transformer(result.data, source.id),
        };
      }

      return result;
    });

    const results = await Promise.all(fetchPromises);
    const items: UnifiedItem[] = [];
    const errors: Array<{ source: string; error: string }> = [];
    const successfulSources: string[] = [];

    for (const result of results) {
      if (result.success && result.data) {
        items.push(...result.data);
        successfulSources.push(result.source);
      } else {
        errors.push({
          source: result.source,
          error: result.error || 'Unknown error',
        });
      }
    }

    // Sort by publishedAt descending
    items.sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime());

    return {
      items: items.slice(0, limit),
      total: items.length,
      sources: successfulSources,
      cached: results.some(r => r.cached),
      timestamp: Date.now(),
      ...(errors.length > 0 && { errors }),
    };
  }

  async searchNews(
    query: string,
    sources?: string[],
    page: number = 1,
    limit: number = 20,
    options: FetchOptions = {}
  ): Promise<AggregatedResult<UnifiedItem>> {
    // For now, we'll get all news and filter on the backend
    // In a real implementation, you'd want to use each source's search API
    const allNews = await this.fetchNews(sources, 1, 1000, options);

    const searchLower = query.toLowerCase();
    const filteredItems = allNews.items.filter(item => {
      if (item.type === 'twitter') {
        return item.content.toLowerCase().includes(searchLower) ||
          item.hashtags.some((tag: string) => tag.toLowerCase().includes(searchLower));
      } else {
        return item.title.toLowerCase().includes(searchLower) ||
          item.summary.toLowerCase().includes(searchLower) ||
          item.tags.some((tag: string) => tag.toLowerCase().includes(searchLower));
      }
    });

    const startIndex = (page - 1) * limit;
    const paginatedItems = filteredItems.slice(startIndex, startIndex + limit);

    return {
      items: paginatedItems,
      total: filteredItems.length,
      sources: allNews.sources,
      cached: allNews.cached,
      timestamp: Date.now(),
      errors: allNews.errors,
    };
  }

  async fetchHotTweets(
    _sources?: string[],
    _page: number = 1,
    _limit: number = 20,
    _options: FetchOptions = {}
  ): Promise<AggregatedResult<TwitterItem>> {
    // This would need to be implemented with Twitter API or similar
    // For now, return empty results
    return {
      items: [],
      total: 0,
      sources: [],
      cached: false,
      timestamp: Date.now(),
    };
  }

  async fetchPrices(
    _symbols?: string[],
    _options: FetchOptions = {}
  ): Promise<AggregatedResult<PriceData>> {
    // This would need to be implemented with price API (CoinGecko, CoinMarketCap, etc.)
    // For now, return empty results
    return {
      items: [],
      total: 0,
      sources: [],
      cached: false,
      timestamp: Date.now(),
    };
  }

  getStats(): any {
    return {
      ...this.stats,
      cacheStats: {
        hits: this.cache.getStats().hits,
        misses: this.cache.getStats().misses,
        keys: this.cache.getStats().keys,
        ksize: this.cache.getStats().ksize,
        vsize: this.cache.getStats().vsize,
      },
    };
  }

  clearCache(): void {
    this.cache.flushAll();
  }

  updateConfig(config: Partial<FetcherConfig>): void {
    this.config = { ...this.config, ...config };
    if (config.proxies) {
      this.proxyManager = new ProxyManager(config.proxies);
    }
  }
}
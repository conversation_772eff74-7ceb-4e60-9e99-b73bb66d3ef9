import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import { useTheme } from '../../components/common/ThemeProvider';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants';
import PageHeader from '../../components/ui/PageHeader';
import Card from '../../components/ui/Card';
import { apiService } from '../../services/api';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  data?: any;
  error?: string;
  duration?: number;
}

const APITestScreen: React.FC = () => {
  const { colors } = useTheme();
  const [tests, setTests] = useState<TestResult[]>([
    { name: '获取新闻列表', status: 'pending' },
    { name: '获取分类列表', status: 'pending' },
    { name: '获取快讯', status: 'pending' },
    { name: '获取热门文章', status: 'pending' },
    { name: '搜索功能', status: 'pending' },
  ]);
  const [isRunning, setIsRunning] = useState(false);

  const styles = createStyles(colors);

  const runTest = async (testName: string, testFn: () => Promise<any>) => {
    const startTime = Date.now();
    try {
      const result = await testFn();
      const duration = Date.now() - startTime;
      
      setTests(prev => prev.map(test => 
        test.name === testName 
          ? { ...test, status: 'success', data: result, duration }
          : test
      ));
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      setTests(prev => prev.map(test => 
        test.name === testName 
          ? { ...test, status: 'error', error: error instanceof Error ? error.message : '未知错误', duration }
          : test
      ));
      
      throw error;
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    
    // 重置测试状态
    setTests(prev => prev.map(test => ({ ...test, status: 'pending', data: undefined, error: undefined })));

    try {
      // 测试1: 获取新闻列表
      await runTest('获取新闻列表', async () => {
        const response = await apiService.getArticles(1, 5);
        if (!response.success) throw new Error(response.error || '请求失败');
        return `获取到 ${response.data.items.length} 条新闻`;
      });

      // 测试2: 获取分类列表
      await runTest('获取分类列表', async () => {
        const response = await apiService.getCategories();
        if (!response.success) throw new Error(response.error || '请求失败');
        return `获取到 ${response.data.length} 个分类`;
      });

      // 测试3: 获取快讯
      await runTest('获取快讯', async () => {
        const response = await apiService.getFlashNews(1, 5);
        if (!response.success) throw new Error(response.error || '请求失败');
        return `获取到 ${response.data.items.length} 条快讯`;
      });

      // 测试4: 获取热门文章
      await runTest('获取热门文章', async () => {
        const response = await apiService.getTrendingArticles(5);
        if (!response.success) throw new Error(response.error || '请求失败');
        return `获取到 ${response.data.length} 篇热门文章`;
      });

      // 测试5: 搜索功能
      await runTest('搜索功能', async () => {
        const response = await apiService.searchArticles('Bitcoin', 1, 3);
        if (!response.success) throw new Error(response.error || '请求失败');
        return `搜索到 ${response.data.items.length} 条结果`;
      });

      Alert.alert('测试完成', '所有API测试已完成，请查看结果详情。');
    } catch (error) {
      console.error('测试过程中出现错误:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Ionicons name="time-outline" size={20} color={colors.textSecondary} />;
      case 'success':
        return <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />;
      case 'error':
        return <Ionicons name="close-circle" size={20} color="#F44336" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return colors.textSecondary;
      case 'success':
        return '#4CAF50';
      case 'error':
        return '#F44336';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <PageHeader
        title="API 测试"
        subtitle="前后端连接测试"
        rightComponent={
          <TouchableOpacity
            style={styles.runButton}
            onPress={runAllTests}
            disabled={isRunning}
          >
            {isRunning ? (
              <ActivityIndicator size="small" color={colors.primary} />
            ) : (
              <Ionicons name="play" size={20} color={colors.primary} />
            )}
          </TouchableOpacity>
        }
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Card style={styles.infoCard}>
          <Text style={styles.infoTitle}>测试说明</Text>
          <Text style={styles.infoText}>
            此页面用于测试前端与后端API的连接状态。点击右上角的播放按钮开始测试。
          </Text>
        </Card>

        {tests.map((test, index) => (
          <Card key={index} style={styles.testCard}>
            <View style={styles.testHeader}>
              <View style={styles.testInfo}>
                {getStatusIcon(test.status)}
                <Text style={[styles.testName, { color: getStatusColor(test.status) }]}>
                  {test.name}
                </Text>
              </View>
              {test.duration && (
                <Text style={styles.duration}>{test.duration}ms</Text>
              )}
            </View>
            
            {test.status === 'success' && test.data && (
              <View style={styles.resultContainer}>
                <Text style={styles.resultLabel}>结果:</Text>
                <Text style={styles.resultText}>{test.data}</Text>
              </View>
            )}
            
            {test.status === 'error' && test.error && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorLabel}>错误:</Text>
                <Text style={styles.errorText}>{test.error}</Text>
              </View>
            )}
          </Card>
        ))}

        <Card style={styles.statusCard}>
          <Text style={styles.statusTitle}>连接状态</Text>
          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>后端地址:</Text>
            <Text style={styles.statusValue}>http://localhost:3001/api</Text>
          </View>
          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>成功测试:</Text>
            <Text style={[styles.statusValue, { color: '#4CAF50' }]}>
              {tests.filter(t => t.status === 'success').length}/{tests.length}
            </Text>
          </View>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    content: {
      flex: 1,
      padding: SPACING.md,
    },
    runButton: {
      padding: SPACING.sm,
    },
    infoCard: {
      marginBottom: SPACING.md,
    },
    infoTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.text,
      marginBottom: SPACING.sm,
    },
    infoText: {
      fontSize: FONT_SIZES.base,
      color: colors.textSecondary,
      lineHeight: 20,
    },
    testCard: {
      marginBottom: SPACING.sm,
    },
    testHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: SPACING.xs,
    },
    testInfo: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    testName: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.medium,
      marginLeft: SPACING.sm,
    },
    duration: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    resultContainer: {
      backgroundColor: colors.success + '10',
      padding: SPACING.sm,
      borderRadius: BORDER_RADIUS.sm,
      marginTop: SPACING.xs,
    },
    resultLabel: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.medium,
      color: '#4CAF50',
      marginBottom: SPACING.xs,
    },
    resultText: {
      fontSize: FONT_SIZES.sm,
      color: colors.text,
    },
    errorContainer: {
      backgroundColor: colors.error + '10',
      padding: SPACING.sm,
      borderRadius: BORDER_RADIUS.sm,
      marginTop: SPACING.xs,
    },
    errorLabel: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.medium,
      color: '#F44336',
      marginBottom: SPACING.xs,
    },
    errorText: {
      fontSize: FONT_SIZES.sm,
      color: colors.text,
    },
    statusCard: {
      marginTop: SPACING.md,
    },
    statusTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.text,
      marginBottom: SPACING.md,
    },
    statusRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: SPACING.sm,
    },
    statusLabel: {
      fontSize: FONT_SIZES.base,
      color: colors.textSecondary,
    },
    statusValue: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.text,
    },
  });

export default APITestScreen;

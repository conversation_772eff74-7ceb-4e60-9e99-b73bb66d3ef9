import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAppStore } from '../../store';
import { COLORS, SPACING, FONT_SIZES } from '../../constants';

interface NetworkStatusIndicatorProps {
  isVisible: boolean;
  status: 'online' | 'offline' | 'cached' | 'mock';
  message?: string;
}

const NetworkStatusIndicator: React.FC<NetworkStatusIndicatorProps> = ({
  isVisible,
  status,
  message,
}) => {
  const { theme } = useAppStore();
  const colors = COLORS[theme];
  const [slideAnim] = useState(new Animated.Value(-100));

  useEffect(() => {
    if (isVisible) {
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: -100,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [isVisible]);

  const getStatusConfig = () => {
    switch (status) {
      case 'online':
        return {
          icon: 'wifi' as const,
          color: '#4CAF50',
          backgroundColor: '#E8F5E8',
          text: message || '网络连接正常',
        };
      case 'offline':
        return {
          icon: 'wifi-off' as const,
          color: '#F44336',
          backgroundColor: '#FFEBEE',
          text: message || '网络连接断开',
        };
      case 'cached':
        return {
          icon: 'archive' as const,
          color: '#FF9800',
          backgroundColor: '#FFF3E0',
          text: message || '使用缓存数据',
        };
      case 'mock':
        return {
          icon: 'flask' as const,
          color: '#2196F3',
          backgroundColor: '#E3F2FD',
          text: message || '使用模拟数据',
        };
      default:
        return {
          icon: 'information-circle' as const,
          color: colors.textSecondary,
          backgroundColor: colors.background,
          text: message || '状态未知',
        };
    }
  };

  const statusConfig = getStatusConfig();
  const styles = createStyles(colors, statusConfig);

  if (!isVisible) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ translateY: slideAnim }],
        },
      ]}
    >
      <View style={styles.content}>
        <Ionicons
          name={statusConfig.icon}
          size={16}
          color={statusConfig.color}
          style={styles.icon}
        />
        <Text style={styles.text}>{statusConfig.text}</Text>
      </View>
    </Animated.View>
  );
};

const createStyles = (colors: typeof COLORS.light, statusConfig: any) =>
  StyleSheet.create({
    container: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      zIndex: 1000,
      backgroundColor: statusConfig.backgroundColor,
      borderBottomWidth: 1,
      borderBottomColor: statusConfig.color + '20',
    },
    content: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: SPACING.sm,
      paddingHorizontal: SPACING.md,
    },
    icon: {
      marginRight: SPACING.xs,
    },
    text: {
      fontSize: FONT_SIZES.sm,
      color: statusConfig.color,
      fontWeight: '500',
    },
  });

export default NetworkStatusIndicator;

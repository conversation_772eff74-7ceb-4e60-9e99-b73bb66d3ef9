# ChainMix Backend Server

A modern Node.js + Express + TypeScript backend service for the ChainMix blockchain news mobile application.

## Features

- 🚀 **Modern Architecture**: Express.js with TypeScript and ES2022
- 🔒 **Security**: Helmet, CORS, rate limiting, input validation
- 📊 **Logging**: Structured logging with Winston
- ⚡ **Caching**: In-memory caching with node-cache
- 🧪 **Testing**: Jest with comprehensive test coverage
- 📝 **Validation**: Runtime type checking with Zod
- 🔄 **External APIs**: NewsAPI.org and CoinGecko integration
- 📈 **Monitoring**: Request logging and performance tracking

## Quick Start

### Prerequisites

- Node.js >= 18.0.0
- npm >= 9.0.0
- API keys for external services (optional for development)

### Installation

```bash
# Install dependencies
npm install

# Copy environment variables
cp .env.example .env

# Edit environment variables
nano .env
```

### Environment Variables

```bash
# Server Configuration
NODE_ENV=development
PORT=3000

# CORS Configuration
CORS_ORIGIN=http://localhost:8081,exp://localhost:19000

# API Keys (get from respective services)
NEWS_API_KEY=your_newsapi_key_here
COINGECKO_API_KEY=your_coingecko_api_key_here

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Cache Configuration
CACHE_TTL_MINUTES=5
CACHE_CHECK_PERIOD_MINUTES=10
```

### Development

```bash
# Start development server with hot reload
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Run tests
npm test

# Run tests with watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Lint code
npm run lint

# Format code
npm run format
```

## API Documentation

### Base URL
```
http://localhost:3000/api
```

### Health Check
```
GET /health
```

### News Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/news` | Get paginated news articles |
| GET | `/api/news/search` | Search news articles |
| GET | `/api/news/trending` | Get trending articles |
| GET | `/api/news/featured` | Get featured articles |
| GET | `/api/news/categories` | Get news categories |
| GET | `/api/news/:id` | Get article by ID |
| GET | `/api/news/category/:slug` | Get articles by category |

### Prices Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/prices` | Get cryptocurrency prices |
| GET | `/api/prices/trending` | Get trending cryptocurrencies |
| GET | `/api/prices/top` | Get top cryptocurrencies |
| GET | `/api/prices/market-overview` | Get market statistics |
| GET | `/api/prices/:id` | Get specific cryptocurrency |
| GET | `/api/prices/:id/history` | Get price history |

### Search Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/search` | Global search across content |

## Project Structure

```
server/
├── src/
│   ├── app.ts                 # Express application setup
│   ├── controllers/           # Request handlers
│   │   ├── news.ts
│   │   └── prices.ts
│   ├── middlewares/           # Express middleware
│   │   ├── error.ts
│   │   ├── validation.ts
│   │   └── logger.ts
│   ├── routes/                # API routes
│   │   ├── news.ts
│   │   ├── prices.ts
│   │   ├── categories.ts
│   │   └── search.ts
│   ├── services/              # Business logic
│   │   ├── news.ts
│   │   └── prices.ts
│   ├── types/                 # TypeScript definitions
│   │   └── index.ts
│   ├── utils/                 # Utility functions
│   │   ├── logger.ts
│   │   └── cache.ts
│   └── tests/                 # Test files
│       ├── setup.ts
│       └── news.test.ts
├── package.json
├── tsconfig.json
├── jest.config.js
├── .eslintrc.js
└── .prettierrc
```

## Technologies Used

- **Runtime**: Node.js 18+
- **Framework**: Express.js 4.19+
- **Language**: TypeScript 5.3+
- **Validation**: Zod 3.22+
- **Logging**: Winston 3.11+
- **Testing**: Jest 29.7+
- **Caching**: node-cache 5.1+
- **HTTP Client**: Axios 1.6+
- **Security**: Helmet, CORS, rate limiting

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

MIT License - see LICENSE file for details
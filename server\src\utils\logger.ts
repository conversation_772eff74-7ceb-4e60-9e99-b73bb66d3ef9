import winston from 'winston';
import path from 'path';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each log level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Tell winston about the colors
winston.addColors(colors);

// Define which log level to use based on environment
const level = (): string => {
  const env = process.env.NODE_ENV || 'development';
  const logLevel = process.env.LOG_LEVEL || 'info';
  
  if (env === 'development') {
    return 'debug';
  }
  
  return logLevel;
};

// Define different log formats
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf((info) => {
    if (info.stack) {
      return `${info.timestamp} ${info.level}: ${info.message}\n${info.stack}`;
    }
    
    if (typeof info.message === 'object') {
      return `${info.timestamp} ${info.level}: ${JSON.stringify(info.message, null, 2)}`;
    }
    
    return `${info.timestamp} ${info.level}: ${info.message}`;
  })
);

const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Define transports
const transports: winston.transport[] = [
  // Console transport
  new winston.transports.Console({
    level: level(),
    format: consoleFormat,
  }),
];

// Add file transport in non-test environments
if (process.env.NODE_ENV !== 'test') {
  // Ensure logs directory exists
  const logDir = path.join(process.cwd(), 'logs');
  
  // Add file transports
  transports.push(
    // Error log file
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      format: fileFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    
    // Combined log file
    new winston.transports.File({
      filename: path.join(logDir, 'combined.log'),
      format: fileFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  );
}

// Create the logger
export const logger = winston.createLogger({
  level: level(),
  levels,
  format: fileFormat,
  transports,
  exitOnError: false,
});

// Add stream interface for Morgan HTTP request logging
export const loggerStream = {
  write: (message: string): void => {
    logger.http(message.trim());
  },
};

// Helper functions for structured logging
export const logHelpers = {
  /**
   * Log API request/response
   */
  logApiCall: (
    method: string,
    url: string,
    statusCode: number,
    duration: number,
    requestId?: string
  ): void => {
    logger.info('API Call', {
      method,
      url,
      statusCode,
      duration,
      requestId,
    });
  },

  /**
   * Log external API call
   */
  logExternalApiCall: (
    service: string,
    endpoint: string,
    method: string,
    statusCode: number,
    duration: number,
    cached: boolean = false
  ): void => {
    logger.info('External API Call', {
      service,
      endpoint,
      method,
      statusCode,
      duration,
      cached,
    });
  },

  /**
   * Log cache operations
   */
  logCacheOperation: (
    operation: 'hit' | 'miss' | 'set' | 'delete' | 'clear',
    key: string,
    ttl?: number
  ): void => {
    logger.debug('Cache Operation', {
      operation,
      key,
      ttl,
    });
  },

  /**
   * Log business logic events
   */
  logBusinessEvent: (
    event: string,
    data: Record<string, unknown>
  ): void => {
    logger.info('Business Event', {
      event,
      ...data,
    });
  },

  /**
   * Log validation errors with details
   */
  logValidationError: (
    field: string,
    value: unknown,
    expectedType: string,
    requestId?: string
  ): void => {
    logger.warn('Validation Error', {
      field,
      value,
      expectedType,
      requestId,
    });
  },

  /**
   * Log performance metrics
   */
  logPerformance: (
    operation: string,
    duration: number,
    metadata?: Record<string, unknown>
  ): void => {
    logger.info('Performance Metric', {
      operation,
      duration,
      ...metadata,
    });
  },
};

export default logger;
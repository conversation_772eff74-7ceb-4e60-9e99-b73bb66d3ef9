import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../common/ThemeProvider';
import { SPACING, FONT_SIZES, BORDER_RADIUS, FONT_WEIGHTS } from '../../constants';
import TouchableScale from './TouchableScale';

export type FontSize = 'small' | 'medium' | 'large' | 'extra-large';

interface FontSizeControllerProps {
  visible: boolean;
  onClose: () => void;
  currentSize: FontSize;
  onSizeChange: (size: FontSize) => void;
}

const fontSizeOptions = [
  { key: 'small' as FontSize, label: '小', size: 14, icon: 'text-outline' },
  { key: 'medium' as FontSize, label: '中', size: 16, icon: 'text-outline' },
  { key: 'large' as FontSize, label: '大', size: 18, icon: 'text-outline' },
  { key: 'extra-large' as FontSize, label: '特大', size: 20, icon: 'text-outline' },
];

const FontSizeController: React.FC<FontSizeControllerProps> = ({
  visible,
  onClose,
  currentSize,
  onSizeChange,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  const handleSizeSelect = (size: FontSize) => {
    onSizeChange(size);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>字体大小</Text>
            <TouchableScale onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={20} color={colors.textSecondary} />
            </TouchableScale>
          </View>

          <View style={styles.optionsContainer}>
            {fontSizeOptions.map((option) => {
              const isSelected = currentSize === option.key;
              return (
                <TouchableScale
                  key={option.key}
                  style={isSelected ? [styles.optionItem, styles.optionItemSelected] : styles.optionItem}
                  onPress={() => handleSizeSelect(option.key)}
                >
                  <View style={styles.optionContent}>
                    <Ionicons
                      name={option.icon as any}
                      size={option.size}
                      color={isSelected ? colors.primary : colors.textSecondary}
                      style={styles.optionIcon}
                    />
                    <Text
                      style={[
                        styles.optionLabel,
                        { fontSize: option.size },
                        isSelected && styles.optionLabelSelected,
                      ]}
                    >
                      {option.label}
                    </Text>
                  </View>
                  {isSelected && (
                    <Ionicons
                      name="checkmark-circle"
                      size={20}
                      color={colors.primary}
                    />
                  )}
                </TouchableScale>
              );
            })}
          </View>

          <View style={styles.previewContainer}>
            <Text style={styles.previewLabel}>预览效果</Text>
            <Text
              style={[
                styles.previewText,
                {
                  fontSize: fontSizeOptions.find(opt => opt.key === currentSize)?.size || 16,
                },
              ]}
            >
              这是一段示例文本，用于预览字体大小效果。区块链技术正在改变世界。
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: colors.overlay,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: SPACING.xl,
    },
    container: {
      backgroundColor: colors.surfaceElevated,
      borderRadius: BORDER_RADIUS.xl,
      padding: SPACING.lg,
      width: '100%',
      maxWidth: 320,
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.25,
      shadowRadius: 16,
      elevation: 12,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: SPACING.lg,
    },
    title: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.text,
    },
    closeButton: {
      padding: SPACING.xs,
    },
    optionsContainer: {
      marginBottom: SPACING.lg,
    },
    optionItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: SPACING.md,
      paddingHorizontal: SPACING.md,
      borderRadius: BORDER_RADIUS.md,
      marginBottom: SPACING.xs,
    },
    optionItemSelected: {
      backgroundColor: colors.primary + '15',
    },
    optionContent: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    optionIcon: {
      marginRight: SPACING.md,
    },
    optionLabel: {
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.text,
    },
    optionLabelSelected: {
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.semibold,
    },
    previewContainer: {
      borderTopWidth: 1,
      borderTopColor: colors.border,
      paddingTop: SPACING.md,
    },
    previewLabel: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textSecondary,
      marginBottom: SPACING.sm,
    },
    previewText: {
      color: colors.text,
      lineHeight: 24,
    },
  });

export default FontSizeController;

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { RootStackParamList, NewsArticle } from '../../types';
import { useNewsStore } from '../../store';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants';
import { useTheme } from '../../components/common/ThemeProvider';
import EnhancedSearchBar from '../../components/ui/EnhancedSearchBar';
import SearchFiltersComponent, { SearchFilters } from '../../components/ui/SearchFilters';
import TouchableScale from '../../components/ui/TouchableScale';
import Card from '../../components/ui/Card';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import EmptyState from '../../components/ui/EmptyState';

type SearchScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const { width: screenWidth } = Dimensions.get('window');

// 模拟搜索建议数据
const searchSuggestions = [
  { id: '1', text: 'DeFi协议', type: 'suggestion' as const, category: 'DeFi' },
  { id: '2', text: 'NFT市场', type: 'suggestion' as const, category: 'NFT' },
  { id: '3', text: '比特币价格', type: 'suggestion' as const, category: '加密货币' },
  { id: '4', text: '以太坊升级', type: 'suggestion' as const, category: '区块链技术' },
  { id: '5', text: '监管政策', type: 'suggestion' as const, category: '监管政策' },
];

const SearchScreen: React.FC = () => {
  const navigation = useNavigation<SearchScreenNavigationProp>();
  const { colors } = useTheme();
  const { searchArticles, articles, isLoading } = useNewsStore();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [searchHistory, setSearchHistory] = useState<string[]>([
    '比特币突破新高',
    'DeFi总锁仓价值',
    'NFT艺术品',
  ]);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({
    categories: [],
    timeRange: 'all',
    sortBy: 'relevance',
  });
  const [searchResults, setSearchResults] = useState<NewsArticle[]>([]);
  const [hasSearched, setHasSearched] = useState(false);

  const styles = createStyles(colors);

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    setHasSearched(true);
    
    // 添加到搜索历史
    if (!searchHistory.includes(query)) {
      setSearchHistory(prev => [query, ...prev.slice(0, 9)]);
    }
    
    // 执行搜索
    await searchArticles(query);
    setSearchResults(articles);
  };

  const handleFiltersApply = () => {
    setShowFilters(false);
    if (searchQuery) {
      handleSearch(searchQuery);
    }
  };

  const handleFiltersReset = () => {
    setFilters({
      categories: [],
      timeRange: 'all',
      sortBy: 'relevance',
    });
  };

  const clearSearchHistory = () => {
    setSearchHistory([]);
  };

  const navigateToArticle = (articleId: string) => {
    navigation.navigate('ArticleDetail', { articleId });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.categories.length > 0) count++;
    if (filters.timeRange !== 'all') count++;
    if (filters.sortBy !== 'relevance') count++;
    return count;
  };

  const renderSearchResult = ({ item }: { item: NewsArticle }) => (
    <Card
      style={styles.resultCard}
      onPress={() => navigateToArticle(item.id)}
      variant="default"
      padding="md"
    >
      <View style={styles.resultRow}>
        <Image
          source={{ uri: item.imageUrl || 'https://via.placeholder.com/100x80' }}
          style={styles.resultImage}
        />
        <View style={styles.resultContent}>
          <Text style={styles.resultTitle} numberOfLines={2}>
            {item.title}
          </Text>
          <Text style={styles.resultSummary} numberOfLines={2}>
            {item.summary}
          </Text>
          <View style={styles.resultMeta}>
            <View style={styles.categoryBadge}>
              <Text style={styles.categoryText}>{item.category.name}</Text>
            </View>
            <Text style={styles.resultTime}>{item.publishedAt}</Text>
          </View>
        </View>
      </View>
    </Card>
  );

  const renderEmptySearch = () => {
    if (isLoading) {
      return <LoadingSpinner text="搜索中..." />;
    }

    if (hasSearched && searchResults.length === 0) {
      return (
        <EmptyState
          icon="search-outline"
          title="未找到相关内容"
          description={`没有找到与"${searchQuery}"相关的文章`}
        />
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <Ionicons
          name="search-outline"
          size={64}
          color={colors.textSecondary}
          style={styles.emptyIcon}
        />
        <Text style={styles.emptyTitle}>搜索区块链资讯</Text>
        <Text style={styles.emptyDescription}>
          输入关键词搜索最新的区块链新闻和资讯
        </Text>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableScale
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableScale>
        
        <View style={styles.searchContainer}>
          <EnhancedSearchBar
            onSearch={handleSearch}
            suggestions={searchSuggestions}
            searchHistory={searchHistory}
            onClearHistory={clearSearchHistory}
            autoFocus={true}
            style={styles.searchBar}
          />
        </View>

        <TouchableScale
          onPress={() => setShowFilters(true)}
          style={styles.filterButton}
        >
          <Ionicons
            name="options-outline"
            size={24}
            color={getActiveFiltersCount() > 0 ? colors.primary : colors.text}
          />
          {getActiveFiltersCount() > 0 && (
            <View style={styles.filterBadge}>
              <Text style={styles.filterBadgeText}>{getActiveFiltersCount()}</Text>
            </View>
          )}
        </TouchableScale>
      </View>

      <FlatList
        data={searchResults}
        renderItem={renderSearchResult}
        keyExtractor={(item) => item.id}
        style={styles.resultsList}
        contentContainerStyle={styles.resultsContent}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmptySearch}
        ListHeaderComponent={
          hasSearched && searchResults.length > 0 ? (
            <View style={styles.resultsHeader}>
              <Text style={styles.resultsCount}>
                找到 {searchResults.length} 篇相关文章
              </Text>
            </View>
          ) : null
        }
      />

      <SearchFiltersComponent
        visible={showFilters}
        onClose={() => setShowFilters(false)}
        filters={filters}
        onFiltersChange={setFilters}
        onApply={handleFiltersApply}
        onReset={handleFiltersReset}
      />
    </SafeAreaView>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      padding: SPACING.sm,
      marginRight: SPACING.sm,
    },
    searchContainer: {
      flex: 1,
    },
    searchBar: {
      marginHorizontal: 0,
    },
    filterButton: {
      padding: SPACING.sm,
      marginLeft: SPACING.sm,
      position: 'relative',
    },
    filterBadge: {
      position: 'absolute',
      top: 4,
      right: 4,
      backgroundColor: colors.primary,
      borderRadius: BORDER_RADIUS.full,
      minWidth: 16,
      height: 16,
      justifyContent: 'center',
      alignItems: 'center',
    },
    filterBadgeText: {
      fontSize: 10,
      color: '#FFFFFF',
      fontWeight: FONT_WEIGHTS.bold,
    },
    resultsList: {
      flex: 1,
    },
    resultsContent: {
      paddingBottom: SPACING.xl,
    },
    resultsHeader: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.md,
    },
    resultsCount: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    resultCard: {
      marginHorizontal: SPACING.md,
      marginVertical: SPACING.xs,
    },
    resultRow: {
      flexDirection: 'row',
    },
    resultImage: {
      width: 100,
      height: 80,
      borderRadius: BORDER_RADIUS.md,
      marginRight: SPACING.md,
    },
    resultContent: {
      flex: 1,
    },
    resultTitle: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
      marginBottom: SPACING.xs,
      lineHeight: 20,
    },
    resultSummary: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginBottom: SPACING.sm,
      lineHeight: 18,
    },
    resultMeta: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    categoryBadge: {
      backgroundColor: colors.primary + '20',
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.xs,
      borderRadius: BORDER_RADIUS.sm,
    },
    categoryText: {
      fontSize: FONT_SIZES.xs,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    resultTime: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: SPACING.xl,
    },
    emptyIcon: {
      marginBottom: SPACING.lg,
      opacity: 0.6,
    },
    emptyTitle: {
      fontSize: FONT_SIZES.xl,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.text,
      marginBottom: SPACING.sm,
      textAlign: 'center',
    },
    emptyDescription: {
      fontSize: FONT_SIZES.base,
      color: colors.textSecondary,
      textAlign: 'center',
      lineHeight: 22,
    },
  });

export default SearchScreen;

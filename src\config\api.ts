/**
 * API 配置文件
 * 管理不同环境下的API地址和配置
 */

// API 环境配置
export const API_CONFIG = {
  development: {
    baseURL: 'http://localhost:3001/api',
    timeout: 10000,
  },
  production: {
    baseURL: 'https://api.chainmix.com/api',
    timeout: 15000,
  },
};

// 获取当前环境配置
export const getCurrentApiConfig = () => {
  return __DEV__ ? API_CONFIG.development : API_CONFIG.production;
};

// API 端点配置
export const API_ENDPOINTS = {
  // 新闻相关
  NEWS: '/news',
  NEWS_DETAIL: (id: string) => `/news/${id}`,
  NEWS_TRENDING: '/news/trending',
  NEWS_RECOMMENDED: (id: string) => `/news/${id}/recommended`,
  
  // 快讯相关
  FLASH: '/flash',
  
  // 搜索相关
  SEARCH: '/search',
  
  // 分类相关
  CATEGORIES: '/categories',
  
  // 价格相关
  PRICES: '/prices',
  
  // 推特相关
  HOT_TWEETS: '/hot-tweets',
  
  // 统计相关
  STATS: '/stats',
  
  // 数据源相关
  SOURCES: '/sources',
};

// 缓存配置
export const CACHE_CONFIG = {
  // 文章列表缓存时间（5分钟）
  ARTICLES: 5 * 60 * 1000,
  
  // 文章详情缓存时间（10分钟）
  ARTICLE_DETAIL: 10 * 60 * 1000,
  
  // 分类列表缓存时间（30分钟）
  CATEGORIES: 30 * 60 * 1000,
  
  // 搜索结果缓存时间（5分钟）
  SEARCH: 5 * 60 * 1000,
  
  // 热门文章缓存时间（10分钟）
  TRENDING: 10 * 60 * 1000,
  
  // 推荐文章缓存时间（15分钟）
  RECOMMENDED: 15 * 60 * 1000,
  
  // 快讯缓存时间（2分钟）
  FLASH: 2 * 60 * 1000,
  
  // 价格数据缓存时间（1分钟）
  PRICES: 1 * 60 * 1000,
};

// 请求配置
export const REQUEST_CONFIG = {
  // 默认请求头
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  
  // 重试配置
  retry: {
    times: 3,
    delay: 1000,
  },
};
